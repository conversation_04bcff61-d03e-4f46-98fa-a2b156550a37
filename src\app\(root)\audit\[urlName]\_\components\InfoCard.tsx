"use client";
import { InfoIcon } from "@/ui/icons/general";
import { ReactNode } from "react";

type Props = {
  title: string;
  description: string | ReactNode;
  className?: string;
};

export default function InfoCard({ description, title, className }: Props) {
  return (
    <div className="w-full flex flex-col p-4 rounded-lg border border-light-gray">
      <div className={`flex items-center gap-2.5 ${className}`}>
        <div>
          <InfoIcon className="w-12 h-12 text-primary" />
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <h5 className="text-secondary font-semibold">{title}</h5>
          <p className="text-sm text-secondary/60">{description}</p>
        </div>
      </div>
    </div>
  );
}
