"use client";

import Modal from "@/ui/Modal";
import React, { useState } from "react";
import { FaTrash } from "react-icons/fa";
import ButtenSubmit from "../../../components/shared/ButtenSubmit";
import dashbordService from "@/services/dashboardServices";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";

export default function Setting() {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [lastRequestTime, setLastRequestTime] = useState<number>(0);
  const route = useRouter();
  const { logout } = useAuthStore();
  const [errText, setErrText] = useState<string[]>([]);
  const closeModal = () => setOpenModal(false);
  const extractInvalidChoice = (message: string): string => {
    const match = message.match(/"\[\s*'([^']+)'/);
    return match ? `${match[1]} is not a valid choice.` : message;
  };
  const { mutate: handleDelete, isPending } = useMutation({
    mutationFn: () => dashbordService.deleteAccount(),
    onSuccess: () => {
      // Logout is now optimistic - no need to wait
      logout();
      // Redirect immediately
      route.replace("/");
    },
    onError: (err: any) => {
      console.log(err);
      if (err.response?.data?.detail) {
        setErrText([err.response.data.detail]);
      } else {
        const finalMessages: string[] = [];
        for (const key in err.response?.data) {
          const messages: string[] = err.response?.data[key];
          messages.forEach((msg) => {
            finalMessages.push(extractInvalidChoice(msg));
          });
        }
        setErrText(finalMessages);
      }
    },
  });

  const handleDeleteWithRateLimit = () => {
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;

    // Rate limiting: 30 seconds between requests
    if (timeSinceLastRequest < 30000) {
      alert(
        "Please wait at least 30 seconds between account deletion requests"
      );
      return;
    }

    setLastRequestTime(now);
    handleDelete();
  };

  return (
    <>
      <div className="w-full md:w-1/2 lg:w-1/3">
        <ButtenSubmit
          text="Delete Account"
          color="primary__outline_hover"
          classPluss="w-full"
          icon={<FaTrash />}
          onClick={() => setOpenModal(true)}
        />
      </div>
      <Modal
        size="xl"
        onClose={closeModal}
        open={openModal}
        title={"Are you sure you want to delete your account?"}
      >
        <div>
          <p className="p-3 mb-5 rounded-md text-red-500 bg-red-100/60 text-sm text-justify lg:text-base">
            You can permanently delete your account along with all associated
            data. However, if you have an active subscription or add-ons, you
            must cancel them first before proceeding with deletion.
          </p>
          {Array.isArray(errText) && errText.length > 0
            ? errText.map((i, index) => (
                <span
                  key={index}
                  className="text-primary-red text-sm my-3 block"
                >
                  Error: {i}
                </span>
              ))
            : null}
          <div className="flex gap-5">
            <ButtenSubmit
              classPlussSvg="!text-primary"
              isLoading={isPending}
              textloading="Loading..."
              text="Yes i'm Sure"
              classPluss="w-full"
              color="null"
              onClick={handleDeleteWithRateLimit}
            />
            <ButtenSubmit
              text="No i'm not"
              classPluss="w-full"
              onClick={closeModal}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}
