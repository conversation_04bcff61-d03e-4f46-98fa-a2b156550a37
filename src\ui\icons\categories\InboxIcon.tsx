import { SVGProps } from "react";

export function InboxIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.75 12.5645H8.01393C8.77148 12.5645 9.464 12.9925 9.80279 13.67L10.1972 14.4589C10.536 15.1364 11.2285 15.5645 11.9861 15.5645H13.6796C14.3483 15.5645 14.9728 15.2303 15.3437 14.6739L16.1563 13.4551C16.5272 12.8987 17.1517 12.5645 17.8204 12.5645H21.75M3.75 12.5645V18.5645C3.75 19.669 4.64543 20.5645 5.75 20.5645H19.75C20.8546 20.5645 21.75 19.669 21.75 18.5645V12.5645M3.75 12.5645L6.26334 5.86221C6.55607 5.0816 7.30231 4.56445 8.136 4.56445H17.364C18.1977 4.56445 18.9439 5.0816 19.2367 5.86221L21.75 12.5645"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
