import { showToast } from "./toast";

/**
 * Centralized error and success handling for create-project route
 * Provides consistent toast notifications and error handling patterns
 */

// API Error types specific to create-project
export interface CreateProjectAPIError {
  response?: {
    status?: number;
    data?: {
      error_message?: string;
      message?: string;
      detail?: string;
      errors?: Record<string, string[]>;
    };
  };
  message?: string;
}

/**
 * Handles API errors specifically for create-project route
 * Filters out authentication errors and provides user-friendly messages
 */
export const handleCreateProjectAPIError = (
  error: CreateProjectAPIError,
  context: string = "operation"
): void => {
  // Don't show 401 authentication errors to users
  if (error?.response?.status === 401) {
    console.log(`Authentication error during ${context} - not displaying to user`);
    return;
  }

  // Extract error message from different API response formats
  let errorMessage = `Failed to ${context}`;

  if (error?.response?.data?.error_message) {
    errorMessage = error.response.data.error_message;
  } else if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  } else if (error?.response?.data?.detail) {
    errorMessage = error.response.data.detail;
  } else if (error?.message) {
    errorMessage = error.message;
  }

  // Show error toast
  showToast.error(errorMessage);
};

/**
 * Success notifications for create-project steps
 */
export const createProjectSuccess = {
  projectCreated: (projectName: string) => {
    showToast.success(`Project "${projectName}" created successfully!`);
  },

  searchEnginesConfigured: (count: number) => {
    showToast.success(`${count} search engine configuration${count > 1 ? 's' : ''} added successfully!`);
  },

  keywordsAdded: (count: number) => {
    showToast.success(`${count} keyword${count > 1 ? 's' : ''} added successfully!`);
  },

  keywordsSuggested: (count: number) => {
    showToast.success(`${count} keyword suggestion${count > 1 ? 's' : ''} extracted from your website!`);
  },

  competitorsAdded: (count: number) => {
    showToast.success(`${count} competitor${count > 1 ? 's' : ''} added successfully!`);
  },

  googleAnalyticsConnected: () => {
    showToast.success("Google Analytics connected successfully!");
  },

  projectCompleted: () => {
    showToast.success("Project setup completed! Redirecting to your projects...");
  },

  dataImported: (type: string, count: number) => {
    showToast.success(`${count} ${type}${count > 1 ? 's' : ''} imported successfully!`);
  },

  configurationSaved: () => {
    showToast.success("Configuration saved successfully!");
  }
};

/**
 * Loading notifications for create-project operations
 */
export const createProjectLoading = {
  creatingProject: () => {
    return showToast.loading("Creating your project...");
  },

  extractingKeywords: () => {
    return showToast.loading("Extracting keywords from your website...");
  },

  connectingGoogleAnalytics: () => {
    return showToast.loading("Connecting to Google Analytics...");
  },

  finalizingProject: () => {
    return showToast.loading("Finalizing your project setup...");
  },

  savingConfiguration: () => {
    return showToast.loading("Saving configuration...");
  },

  importingData: (type: string) => {
    return showToast.loading(`Importing ${type}...`);
  }
};

/**
 * Warning notifications for create-project
 */
export const createProjectWarning = {
  missingConfiguration: (step: string) => {
    showToast.error(`Please complete the ${step} configuration before proceeding.`);
  },

  invalidData: (field: string) => {
    showToast.error(`Please check your ${field} and try again.`);
  },

  networkIssue: () => {
    showToast.error("Network connection issue. Please check your internet and try again.");
  },

  sessionExpired: () => {
    showToast.error("Your session has expired. Please log in again.");
  }
};

/**
 * Promise-based API call wrapper for create-project
 * Automatically handles loading, success, and error states
 */
export const createProjectAPICall = async <T>(
  apiCall: Promise<T>,
  options: {
    loadingMessage?: string;
    successMessage?: string | ((data: T) => string);
    errorContext?: string;
    onSuccess?: (data: T) => void;
    onError?: (error: CreateProjectAPIError) => void;
  } = {}
): Promise<T> => {
  const {
    loadingMessage,
    successMessage,
    errorContext = "operation",
    onSuccess,
    onError
  } = options;

  // Show loading toast if message provided
  const loadingToastId = loadingMessage ? showToast.loading(loadingMessage) : null;

  try {
    const result = await apiCall;

    // Dismiss loading toast
    if (loadingToastId) {
      showToast.dismiss(loadingToastId);
    }

    // Show success message if provided
    if (successMessage) {
      const message = typeof successMessage === 'function' 
        ? successMessage(result) 
        : successMessage;
      showToast.success(message);
    }

    // Call success callback
    if (onSuccess) {
      onSuccess(result);
    }

    return result;
  } catch (error) {
    // Dismiss loading toast
    if (loadingToastId) {
      showToast.dismiss(loadingToastId);
    }

    // Handle error
    handleCreateProjectAPIError(error as CreateProjectAPIError, errorContext);

    // Call error callback
    if (onError) {
      onError(error as CreateProjectAPIError);
    }

    throw error;
  }
};

/**
 * Utility to dismiss all toasts (useful for page transitions)
 */
export const dismissAllToasts = () => {
  showToast.dismiss();
};

export default {
  handleError: handleCreateProjectAPIError,
  success: createProjectSuccess,
  loading: createProjectLoading,
  warning: createProjectWarning,
  apiCall: createProjectAPICall,
  dismissAll: dismissAllToasts
};
