"use client";
import { ApexOptions } from "apexcharts";
import dynamic from "next/dynamic";
const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export type PieChartDataItem = {
  name: string;
  value: number;
  color: string;
};

type PieChartProps = {
  data: PieChartDataItem[];
  title?: string;
  size?: "sm" | "md" | "lg";
  showLegend?: boolean;
  legendPosition?: "bottom" | "right";
  donut?: boolean;
  donutSize?: string;
  className?: string;
};

const PieChart = ({
  data,
  title,
  size = "md",
  showLegend = true,
  legendPosition = "bottom",
  donut = false,
  donutSize = "55%",
  className = "",
}: PieChartProps) => {
  // Calculate chart size based on the size prop
  const getChartSize = () => {
    switch (size) {
      case "sm":
        return { height: 200, width: "100%" };
      case "lg":
        return { height: 350, width: "100%" };
      case "md":
      default:
        return { height: 280, width: "100%" };
    }
  };

  // Extract series and labels from data
  const series = data.map((item) => item.value);
  const labels = data.map((item) => item.name);
  const colors = data.map((item) => item.color);

  // Configure chart options
  const options: ApexOptions = {
    chart: {
      type: "donut",
      fontFamily: "Inter, sans-serif",
      background: "transparent",
      toolbar: {
        show: false,
      },
    },
    colors: colors,
    labels: labels,
    legend: {
      show: showLegend,
      position: legendPosition,
      fontSize: "12px",
      fontFamily: "Inter, sans-serif",
      fontWeight: 500,
      markers: {
        size: 12,
        strokeWidth: 0,
        shape: "circle",
      },
      itemMargin: {
        horizontal: 10,
        vertical: 5,
      },
    },
    dataLabels: {
      enabled: false,
    },
    plotOptions: {
      pie: {
        donut: {
          size: donut ? donutSize : "0%",
          labels: {
            show: donut,
            name: {
              show: true,
              fontSize: "14px",
              fontWeight: 600,
              offsetY: -10,
            },
            value: {
              show: true,
              fontSize: "18px",
              fontWeight: 700,
              formatter: function(val: any) { return typeof val === 'number' ? val.toFixed(2) : String(val); },
            },
            total: {
              show: true,
              label: title || "Total",
              fontSize: "16px",
              fontWeight: 600,
              formatter: function(w: any) {
                const total = w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
                return total.toFixed(2);
              },
            },
          },
        },
      },
    },
    stroke: {
      width: 0,
    },
    tooltip: {
      enabled: true,
      y: {
        formatter: function(val: any) { return typeof val === 'number' ? val.toFixed(2) : String(val); },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  const { height, width } = getChartSize();

  return (
    <div className={`w-full ${className}`}>
      {title && !donut && (
        <h3 className="text-center font-semibold text-secondary mb-2">{title}</h3>
      )}
      <div className="chart-container">
        <ReactApexChart
          options={options}
          series={series}
          type="donut"
          height={height}
          width={width}
        />
      </div>
    </div>
  );
}

export default PieChart;
