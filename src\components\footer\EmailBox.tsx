import { ArrowRigthIcon } from "@/ui/icons/navigation";

export default function EmailBox() {
  return (
    <div className="w-full lg:w-auto">
      <div className="text-lg font-black text-secondary lg:mb-4">Audit</div>
      <p className="text-sm lg:text-base text-secondary font-medium my-2 lg:my-4">
        Enter your website to get a free SEO audit and discover opportunities
        for growth!
      </p>
      <div className="w-full bg-white/80 backdrop-blur-[10px] p-2 rounded-lg flex items-center gap-2">
        <input
          type="text"
          placeholder="Example.com"
          className="flex-1 text-sm p-4 appearance-none bg-transparent focus:outline-none text-secondary placeholder-secondary/30"
        />
        <button className="w-10 h-10 text-white bg-primary rounded-lg flex items-center justify-center btn--primary">
          <ArrowRigthIcon className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
}
