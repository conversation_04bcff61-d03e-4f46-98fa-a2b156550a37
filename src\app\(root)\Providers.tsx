"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { useAuthStore } from "@/store/authStore";
import { AuthProvider } from "@/providers/AuthProvider";
import authErrorHandler from "@/utils/authErrorHandler";
import { useRouter } from "next/navigation";
import storageService from "@/services/storageService";

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());
  const [isInitialAuthCheckComplete, setIsInitialAuthCheckComplete] =
    useState(false);
  const fetchProfile = useAuthStore((state) => state.fetchProfile);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const router = useRouter();

  // Check authentication status on initial load
  useEffect(() => {
    const performInitialAuthCheck = async () => {
      // Only check auth if the document is visible (page is loaded)
      if (
        typeof document !== "undefined" &&
        document.visibilityState === "visible"
      ) {
        console.log(
          "Page is visible, performing initial auth check with server validation"
        );

        try {
          // Force a fresh auth check on page load to validate token with server
          await fetchProfile(true);
        } catch (error) {
          console.error("Initial auth check failed:", error);
        } finally {
          // Mark initial auth check as complete regardless of result
          setIsInitialAuthCheckComplete(true);
        }

        // Set up visibility change listener to check auth when page becomes visible
        const handleVisibilityChange = () => {
          if (document.visibilityState === "visible") {
            console.log("Page became visible, checking auth status");
            // Use cached auth for visibility changes (not forced)
            fetchProfile();
          }
        };

        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
          document.removeEventListener(
            "visibilitychange",
            handleVisibilityChange
          );
        };
      } else {
        // If document is not visible, still mark as complete to prevent blocking
        setIsInitialAuthCheckComplete(true);
      }
    };

    performInitialAuthCheck();
  }, [fetchProfile]);

  // Quick token check on mount to set initial auth state if token exists
  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = storageService.getToken();
      const user = storageService.getUser();

      // If we have both token and user data in storage, but auth state is false,
      // set it to true temporarily until the server validation completes
      if (token && user && !isAuthenticated && !isInitialAuthCheckComplete) {
        useAuthStore.setState({
          isAuthenticated: true,
          user: user as any, // Cast to UserProfile - will be updated by fetchProfile
          isLoading: true,
        });
      }
    }
  }, [isAuthenticated, isInitialAuthCheckComplete]);

  // Initialize auth error handler to redirect to login page
  useEffect(() => {
    // Track if we've already redirected due to an error
    let authErrorRedirectTimestamp = 0;
    const AUTH_ERROR_DEBOUNCE = 10000; // 10 seconds

    // Set up auth error handler to redirect to login on 401 errors
    const handleAuthError = () => {
      const now = Date.now();

      // Only redirect if we haven't redirected recently
      if (now - authErrorRedirectTimestamp > AUTH_ERROR_DEBOUNCE) {
        console.log("Auth error detected in Providers, redirecting to login");
        authErrorRedirectTimestamp = now;
        router.push("/login");
      } else {
        console.log("Auth error detected, but redirect was recent. Ignoring.");
      }
    };

    // Register the handler
    authErrorHandler.addAuthErrorListener(handleAuthError);

    // Clean up on unmount
    return () => {
      authErrorHandler.removeAuthErrorListener(handleAuthError);
    };
  }, [router]);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>{children}</AuthProvider>
    </QueryClientProvider>
  );
}
