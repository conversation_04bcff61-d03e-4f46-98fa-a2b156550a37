import ButtenSubmit from "@/components/shared/ButtenSubmit";
import profileService from "@/services/profileService";
import { useRouter } from "next/navigation";
import { useState } from "react";
import AccordionSettingProfile, { FaqsType } from "./AccordionSettingProfile";
import { textSettingProfile } from "./AccordionSettingProfile";
type ChoiceType = Record<number, string[]>;

export default function FaqsSettingProfile({ faqs }: { faqs?: ChoiceType }) {
  const [iseLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<FaqsType[]>([]);
  const [choice, setChoice] = useState<Record<number, string[]>>(faqs || {});
  const route = useRouter();
  const submitSettingProfile = async () => {
    setIsLoading(true);
    const body = {
      business_type: choice[0]?.[0] || "",
      company_size: choice[1]?.[0] || "",
      user_role: choice[2]?.[0] || "",
      offers_seo_services: choice[3]?.[0] || "",
      help_areas: choice[4] || [],
      interested_features: choice[5] || [],
    } as any;
    const emptyFields = Object.entries(body).filter(([key, val]) => {
      return Array.isArray(val) ? val.length === 0 : !val;
    });
    const errorMessages = emptyFields.map(([key]) =>
      textSettingProfile.find((i) => i.name === key && i.title)
    ) as FaqsType[];
    if (errorMessages.length) {
      setIsLoading(false);
      setError(errorMessages);
      return;
    }
    setError([]);
    try {
      const response = await profileService.updateProfileSetting(body);
      console.log(response);

      if (response) {
        // Check for post-login redirect
        const postLoginRedirect = sessionStorage.getItem("postLoginRedirect");
        const postLoginCallback = sessionStorage.getItem("postLoginCallback");

        if (postLoginRedirect) {
          sessionStorage.removeItem("postLoginRedirect");
          console.log(
            "Profile setup complete, redirecting to stored URL:",
            postLoginRedirect
          );
          window.location.href = postLoginRedirect;
          return;
        }

        if (postLoginCallback) {
          sessionStorage.removeItem("postLoginCallback");
          console.log("Profile setup complete, post-login callback detected");
        }

        // Default redirect to dashboard
        route.replace("/my-projects");
      }
      setIsLoading(false);
    } catch (err: any) {
      console.error("Profile setting update error:", err);
      // Try to parse the error message to show specific validation errors
      try {
        const errorData = JSON.parse(err.message);
        if (errorData.help_areas) {
          console.error("Help areas validation error:", errorData.help_areas);
          alert(`Error: ${errorData.help_areas.join(", ")}`);
        } else {
          alert(`Error updating profile: ${err.message}`);
        }
      } catch {
        alert(`Error updating profile: ${err.message}`);
      }
      setIsLoading(false);
    }
  };
  return (
    <div className="bg-white mx-auto mb-12 w-11/12 sm:10/12 md:w-8/12 lg:w-6/12 -mt-24 rounded-xl shadow gap-2 md:gap-4 flex justify-center items-center flex-col">
      <p className="p-3 sm:p-5 w-full text-gray-700 text-xl font-bold md:text-3xl md:font-extrabold">
        Let’s Personalise Your Experience
      </p>
      <span className="border w-full border-gray-400"></span>
      <div className="px-3 sm:px-5">
        <AccordionSettingProfile choice={choice} setChoice={setChoice} />
      </div>
      <div className="w-full px-5 mb-5">
        {error.length ? (
          <div className="flex flex-col gap-2 md:gap-3 my-3">
            {error.map((i) => {
              return (
                <div key={i.name} className="text-primary-red text-xs">
                  Missing fields : {i?.title}
                </div>
              );
            })}
          </div>
        ) : null}
        <ButtenSubmit
          text="Submit"
          classPluss="w-full"
          textloading="Sending..."
          isLoading={iseLoading}
          onClick={submitSettingProfile}
        />
      </div>
    </div>
  );
}
