import http from "./httpService";

// Define types for the pricing data based on the new API response format
export type PricingPlan = {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  interval_count: number;
  product_id: string;
  metadata: Record<string, any>;
  price_metadata: Record<string, any>;
};

export type PricingData = {
  [key: string]: PricingPlan[];
};

/**
 * Fetch pricing data from the API
 * @returns Promise with pricing data
 */
export async function getPricingData(): Promise<PricingData> {
  return http.get("/api/pricing/").then(({ data }) => data);
}

/**
 * Get Pro Plan pricing plans only
 * @returns Promise with Pro Plan pricing data
 */
export async function getProPlanPricingData(): Promise<PricingPlan[]> {
  return getPricingData().then((data) => data["Pro Plan"] || []);
}

/**
 * Get White Label pricing plans only (deprecated - use getProPlanPricingData)
 * @returns Promise with Pro Plan pricing data
 */
export async function getWhiteLabelPricingData(): Promise<PricingPlan[]> {
  return getProPlanPricingData();
}

const pricingService = {
  getPricingData,
  getProPlanPricingData,
  getWhiteLabelPricingData,
};

export default pricingService;
