import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { projectAPI, FullProjectRequest } from "@/services/projectService";
import createProjectToast from "@/lib/createProjectToast";

interface KeywordTableRow {
  id: string;
  keyword: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
  volume: string;
  configId: string;
}

interface UseProjectSubmissionProps {
  keywords: KeywordTableRow[];
  dataState: {
    country: any;
    searchEngines: any[];
    language: any;
  };
  getValues: () => { domain: string; name: string };
}

export const useProjectSubmission = ({
  keywords,
  dataState,
  getValues,
}: UseProjectSubmissionProps) => {
  const [loading, setLoading] = useState(false);
  const [lastSubmitTime, setLastSubmitTime] = useState<number | null>(null);
  const navigate = useRouter();
  const { resetAll } = useCreateProjectStore();

  // Utility functions
  const sanitizeInput = (input: string) => input.replace(/[<>/"'`;()]/g, "");

  // Function to ensure URL has proper protocol
  const formatUrl = (url: string) => {
    const cleanUrl = sanitizeInput(url).trim();
    if (!cleanUrl) return cleanUrl;

    // If URL already has protocol, return as is
    if (cleanUrl.startsWith("http://") || cleanUrl.startsWith("https://")) {
      return cleanUrl;
    }

    // Add https:// prefix
    return `https://${cleanUrl}`;
  };

  const { mutate, isPending } = useMutation({
    mutationFn: async (): Promise<any> => {
      const now = Date.now();
      if (lastSubmitTime && now - lastSubmitTime < 3000) {
        return new Promise((res) => setTimeout(() => res(true), 500));
      }

      setLastSubmitTime(now);
      setLoading(true);

      const { domain, name } = getValues();

      // Validate required fields
      if (!domain) {
        createProjectToast.warning.invalidData("domain");
        throw new Error("Domain is required");
      }

      if (keywords.length === 0) {
        createProjectToast.warning.missingConfiguration("keywords");
        throw new Error(
          "Please add at least one keyword before creating the project"
        );
      }

      try {
        // Prepare API request data
        const formattedUrl = formatUrl(domain);
        const cleanDomain = formattedUrl
          .replace(/^https?:\/\//, "")
          .replace(/^www\./, "");

        // Transform keywords data to API format
        const apiKeywords = keywords.map((keyword) => ({
          keyword: keyword.keyword,
          search_engines: [keyword.searchEngine.name.toLowerCase()],
          countries: [keyword.country.code],
          languages: [keyword.language.code],
        }));

        // Transform search engines data to API format
        const apiSearchEngines =
          dataState.searchEngines?.map((engine) => ({
            search_engine: engine.name.toLowerCase(),
            countries: dataState.country ? [dataState.country.code] : [],
            languages: dataState.language ? [dataState.language.code] : [],
          })) || [];

        const projectData: FullProjectRequest = {
          url: formattedUrl,
          domain_type: `*.${cleanDomain}`,
          project_name: sanitizeInput(name || cleanDomain),
          project_color: "#914AC4", // Default purple color for shortcut projects
          primary_search_engines: apiSearchEngines,
          keywords: apiKeywords,
          competitors: [], // No competitors in shortcut flow
        };

        // Use the centralized API call handler
        return await createProjectToast.apiCall(
          projectAPI.createFullProject(projectData),
          {
            loadingMessage: "Creating your project...",
            successMessage: `Project "${sanitizeInput(
              name || cleanDomain
            )}" created successfully!`,
            errorContext: "create project",
          }
        );
      } finally {
        setLoading(false);
      }
    },
    onSuccess: () => {
      // Clear any existing project data and redirect to my-projects
      resetAll();
      navigate.push("/my-projects");
    },
    onError: (error: any) => {
      // Error handling is done by createProjectToast.apiCall
      console.error("Error creating project:", error);
    },
    onSettled: () => {
      setLoading(false);
    },
  });

  return {
    mutate,
    isPending,
    loading,
  };
};
