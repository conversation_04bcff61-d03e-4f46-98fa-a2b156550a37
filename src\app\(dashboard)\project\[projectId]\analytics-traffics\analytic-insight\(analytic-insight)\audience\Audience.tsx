"use client";
import React, { useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import GSCLineChart from "../../_components/line-chart/LineChart";
import LineChartCard from "../../../overview/_components/LineChartCard";
import { LineChartCards } from "./Audience.types";
import useAudience from "./Audience.hook";
import NoData from "../../_components/NoData";

/* ============================== FRAMER MOTION ============================= */
import { AnimatePresence, motion } from "framer-motion";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";
import LineChartSkeleton from "../../../_components/line-chart-skeleton/LineChartSkeleton";
import SmallChartSkeleton from "../../../_components/small-chart-skeleton/SmallChartSkeleton";

/* ========================================================================== */
const Audience = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const { data, isLoading, isPending, isError, error } = useAudience(activeTab);

  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && activeTab === "") {
      setActiveTab(data.cardTabs[0].title);
    }
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (!isError)
    return (
      <Card className="space-y-2">
        <div>
          <Title>Audience</Title>
        </div>
        <DateRange />
        <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap min-h-[74px]">
          {cardsDataChanged
            ? Array.from({ length: 7 }).map((_, i) => (
                <CardTab key={i} isLoading />
              ))
            : prevCardTabsRef.current &&
              prevCardTabsRef.current.map(
                (
                  {
                    title,
                    changeValue,
                    value,
                  }: { title: string; changeValue: string; value: string },
                  index: number,
                ) => (
                  <CardTab
                    key={index}
                    title={title}
                    value={value}
                    changeValue={changeValue}
                    className={`border-2 h-16 ${
                      activeTab === title
                        ? "border-primary"
                        : "border-transparent"
                    }`}
                    style={
                      activeTab === title
                        ? { borderColor: themeColor }
                        : { borderColor: "transparent" }
                    }
                    onSelect={() => setActiveTab(title)}
                  />
                ),
              )}
        </div>
        <div
          className="mt-8 min-h-[310px] overflow-hidden"
          style={{ position: "relative" }}
        >
          {isLoading || isPending ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <LineChartSkeleton />
            </motion.div>
          ) : data ? (
            <motion.div
              key="data"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <GSCLineChart
                lineChartData={data.lineChartData}
                colors={data.colors}
                selectedLines={data.selectedLines}
                cardsData={data.cardsData}
              />
            </motion.div>
          ) : null}
        </div>
        <div className="flex flex-col lg:flex-row w-full mt-8 lg:gap-2 px-8 min-h-[170px]">
          {isLoading || isPending
            ? Array.from({ length: 3 }).map((_, i) => (
                <motion.div
                  className="w-full"
                  key={`loading-${i}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <SmallChartSkeleton className="w-full" />
                </motion.div>
              ))
            : data &&
              data.lineChartCards.map((item: LineChartCards, index: number) => (
                <motion.div
                  key={`card-${index}`}
                  className="w-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <LineChartCard
                    data={item.data}
                    bigNumber={item.bigNumber}
                    smallNumber={item.smallNumber}
                    title={item.title}
                    className="w-full"
                  />
                </motion.div>
              ))}
        </div>
      </Card>
    );

  if (isError || !data) {
    console.error("isError: ", error);
    return <NoData title="Audience" />;
  }
};

export default Audience;
