import React from "react";

/* ================================ RECHARTS ================================ */
import { LineChart, Line, ResponsiveContainer } from "recharts";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";

/* ================================== TYPES ================================= */
import type { LineChartData } from "@/types/LineChartCard.type";
import Skeleton from "react-loading-skeleton";
import { CurveType } from "recharts/types/shape/Curve";
/* ========================================================================== */
/**

* A sleek little card component that displays a line chart with a big number and a growth indicator.
* 
* Perfect for dashboard flexing. 💪
* 
* @component
* @param props - Props for LineChartCard
* @param props.title - Title text shown at the top of the card
* @param [props.className] - Optional Tailwind class overrides for the card
* @param [props.bigNumber] - The big juicy number displayed prominently
* @param [props.smallNumber] - The smaller number showing growth like "+15%" or "-3%"
* @param props.data - Array of data points for the line chart
* @param [props.onClick] - Optional click handler for the whole card
* @param [props.chartType] - Optional chart type (line, bar, etc.)
* @returns A stylized line chart card component
*/
const LineChartCard = ({
  title,
  className,
  bigNumber,
  smallNumber,
  data,
  onClick,
  isLoading,
  chartType = "monotone",
}: LineChartData) => {
  return (
    <Card className={cn("border", className)} onClick={onClick}>
      {isLoading ? (
        <Skeleton height={120} />
      ) : (
        <>
          <p className="text-xs text-gray-700">{title}</p>
          <div
            style={{ width: "100%", height: 120 }}
            className={"flex flex-col items-start gap-1"}
          >
            <div className="space-x-1">
              {bigNumber && (
                <span className=" text-xl font-bold text-secondary">
                  {bigNumber}
                </span>
              )}
              {smallNumber && (
                <span
                  className={` text-sm ${
                    smallNumber.includes("+")
                      ? "text-green-600"
                      : smallNumber.includes("+")
                        ? "text-red-600"
                        : "text-secondary"
                  }`}
                >
                  {smallNumber}
                </span>
              )}
            </div>
            <ResponsiveContainer
              width="100%"
              className={"border-b-2 border-yellow-500"}
            >
              <LineChart data={data}>
                <Line
                  type={chartType as CurveType}
                  dataKey="value"
                  stroke="#914AC4"
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </>
      )}
    </Card>
  );
};

export default LineChartCard;
