"use client";
import React, { memo, useMemo, useEffect } from "react";
import SelectSearchEngine from "../SelectSearchEngine";
import { AutoCompleteCountry } from "../AutoCompleteCountry";
import { AutoComplete } from "../../shared/AutoComplete";
import <PERSON><PERSON>ounter from "../StepCounter";
import { getFlagImageUrl } from "@/utils/flagUtils";

interface SearchEngineLocationSectionProps {
  dataState: {
    country: {
      name: string;
      code: string;
      image: string;
    } | null;
    searchEngines:
      | {
          name: string;
          image: string;
        }[]
      | null;
    language: {
      code: string;
      name: string;
    } | null;
  };
  setDataState: React.Dispatch<React.SetStateAction<any>>;
  countriesData: any[];
  supportedLocations: Array<{
    code: string;
    name: string;
    primary_language: string;
  }>;
  googleLanguage: any[];
}

const SearchEngineLocationSection = memo(
  ({
    dataState,
    setDataState,
    countriesData,
    supportedLocations,
    googleLanguage,
  }: SearchEngineLocationSectionProps) => {
    // Merge supported locations with country images
    const supportedCountriesWithImages = useMemo(() => {
      if (supportedLocations.length === 0) return countriesData;

      return supportedLocations.map((location) => {
        const countryData = countriesData.find(
          (country) => country.code === location.code
        );
        return {
          ...location,
          image: countryData?.image || getFlagImageUrl(location.code),
        };
      });
    }, [supportedLocations, countriesData]);

    // Filter languages to only show those used as primary languages in supported locations
    const supportedLanguages = useMemo(() => {
      if (supportedLocations.length === 0 || googleLanguage.length === 0)
        return googleLanguage;

      const primaryLanguageCodes = [
        ...new Set(supportedLocations.map((loc) => loc.primary_language)),
      ];
      return googleLanguage.filter((lang) =>
        primaryLanguageCodes.includes(lang.code)
      );
    }, [supportedLocations, googleLanguage]);

    // Update default selections when supported locations are loaded
    useEffect(() => {
      if (
        supportedCountriesWithImages.length > 0 &&
        supportedLanguages.length > 0
      ) {
        // Check if current country is in supported list
        const currentCountrySupported = supportedCountriesWithImages.find(
          (country) => country.code === dataState.country?.code
        );

        // Check if current language is in supported list
        const currentLanguageSupported = supportedLanguages.find(
          (lang) => lang.code === dataState.language?.code
        );

        // If current selections are not supported, update to first supported options
        if (!currentCountrySupported || !currentLanguageSupported) {
          const defaultCountry =
            currentCountrySupported || supportedCountriesWithImages[0];
          const defaultLanguage =
            currentLanguageSupported ||
            supportedLanguages.find(
              (lang) => lang.code === defaultCountry.primary_language
            ) ||
            supportedLanguages[0];

          setDataState((prev: any) => ({
            ...prev,
            country: defaultCountry,
            language: defaultLanguage,
          }));
        }
      }
    }, [
      supportedCountriesWithImages,
      supportedLanguages,
      dataState.country?.code,
      dataState.language?.code,
      setDataState,
    ]);
    const handleSearchEngineChange = React.useCallback(
      (items: any[]) => {
        setDataState((prev: any) => ({
          ...prev,
          searchEngines: items,
        }));
      },
      [setDataState]
    );

    const handleCountryChange = React.useCallback(
      (item: any) => {
        setDataState((prev: any) => ({ ...prev, country: item }));
      },
      [setDataState]
    );

    const handleLanguageChange = React.useCallback(
      (item: any) => {
        setDataState((prev: any) => ({ ...prev, language: item }));
      },
      [setDataState]
    );

    return (
      <div className="space-y-3 pt-3 border-t border-gray-100">
        <StepCounter stepNumber={2} title="Search Engine & Location" />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          <div>
            <SelectSearchEngine
              soloChoose
              title="Search engine"
              setValue={handleSearchEngineChange}
              value={dataState.searchEngines || []}
              guideText="Select the search engines to track keywords in"
              classPlusButton="!bg-white border border-gray-200 shadow-none rounded-md h-[45px] hover:border-gray-300 focus:border-transparent focus:ring-2 focus:ring-primary transition-all duration-150"
            />
          </div>
          <div>
            <div className="flex mb-2 items-center gap-2 text-gray-600">
              <span className="text-sm lg:text-base">Location</span>
            </div>
            <div className="flex bg-white rounded-md overflow-hidden border border-gray-200 focus-within:border-transparent focus-within:ring-2 focus-within:ring-primary transition-all duration-150">
              <div className="flex-1">
                <AutoCompleteCountry
                  placeHoldre="Enter Your Country"
                  data={
                    supportedCountriesWithImages.length > 0
                      ? supportedCountriesWithImages
                      : countriesData
                  }
                  valueKey="code"
                  labelKey="name"
                  imageKey="image"
                  value={dataState.country?.code || ""}
                  setValue={handleCountryChange}
                  classPlusButton="!bg-white border-none shadow-none rounded-none h-[45px] hover:bg-gray-50 transition-colors"
                />
              </div>
              <div className="w-28 border-l border-gray-200">
                <AutoComplete
                  classValue="py-2 px-3"
                  classPlusButton="!bg-white border-none shadow-none rounded-none h-[45px] hover:bg-gray-50 transition-colors"
                  valueKey="name"
                  value={dataState.language?.name || ""}
                  dataSelector={
                    supportedLanguages.length > 0
                      ? supportedLanguages
                      : googleLanguage
                  }
                  setValue={handleLanguageChange}
                  placeHoldre="Language"
                  option={(item) => <div>{item?.code}</div>}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

SearchEngineLocationSection.displayName = "SearchEngineLocationSection";

export default SearchEngineLocationSection;
