import React from "react";

interface PerformanceGaugeChartProps {
  title: string;
  time?: number;
  max: number;
  // Keeping these props for backward compatibility
  green_time: number;
  warning_time: number;
  red_time: number;
}

/**
 * A performance gauge chart with a needle precisely aligned to the arc using consistent trigonometric logic.
 * Uses a purple gradient for the arc and displays time below the arc.
 */
const PerformanceGaugeChart: React.FC<PerformanceGaugeChartProps> = ({
  title,
  time,
  max,
  // Not using these anymore but keeping for backward compatibility
  green_time,
  warning_time,
  red_time,
}) => {
  const safeTime = time !== undefined ? time : 0;
  const clampedTime = Math.max(0, Math.min(safeTime, max));
  const percent = clampedTime / max;

  // Constants for gauge geometry
  const radius = 40;
  const centerX = 50;
  const centerY = 50;

  // The SVG arc goes from 180° (left) to 0° (right) in standard SVG coordinate system
  // We need to map our percentage (0-1) to this range for the needle
  const svgAngleDeg = 180 - percent * 180;
  const angleRad = (svgAngleDeg * Math.PI) / 180;

  // Needle end coordinates - using the correct angle calculation
  // SVG Y-axis is inverted (positive goes down), so we negate the sin component
  // Using radius as the needle length ensures it aligns perfectly with the arc
  const needleLength = radius;
  // Fix hydration issues by using toFixed(4) to ensure consistent string representation
  const needleX = Number((centerX + needleLength * Math.cos(angleRad)).toFixed(4));
  const needleY = Number((centerY - needleLength * Math.sin(angleRad)).toFixed(4));

  // Get color based on time thresholds
  const getColorInfo = () => {
    if (green_time !== undefined && clampedTime <= green_time)
      return { color: "#22c55e", textColor: "text-green-500" };
    if (warning_time !== undefined && clampedTime <= warning_time)
      return { color: "#eab308", textColor: "text-yellow-500" };
    return { color: "#ef4444", textColor: "text-red-500" };
  };

  const { color, textColor } = getColorInfo();

  return (
    <div className="flex flex-col items-center">
      <div className="relative w-40 h-20">
        {/* Gauge */}
        <svg
          viewBox="0 0 100 60"
          className="absolute top-0 left-0 w-full h-full"
        >
          {/* Background arc */}
          <path
            d="M10 50 A40 40 0 0 1 90 50"
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="16"
            strokeLinecap="round"
          />

          {/* Colored arc */}
          <path
            d="M10 50 A40 40 0 0 1 90 50"
            fill="none"
            stroke={color}
            strokeWidth="16"
            strokeLinecap="round"
            strokeDasharray="180"
            strokeDashoffset={180 - percent * 180}
          />

          {/* Bold line at the end of the arc */}
          <line
            x1={needleX}
            y1={needleY}
            x2={needleX + (needleX > centerX ? 3 : -3)}
            y2={needleY + (needleY < centerY ? 3 : -3)}
            stroke={color}
            strokeWidth="8"
            strokeLinecap="round"
            strokeOpacity="2"
          />
        </svg>
      </div>

      {/* Time display - centered and moved up closer to the arc */}
      <div className={`text-lg font-semibold ${textColor} -mt-6`}>
        {clampedTime.toFixed(1)} s
      </div>

      {/* Title */}
      <div className="text-sm text-gray-700 mt-1">{title}</div>
    </div>
  );
};

export default PerformanceGaugeChart;
