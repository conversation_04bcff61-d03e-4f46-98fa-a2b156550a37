import Card from "@/components/ui/card";
import { cn } from "@/lib/utils";
import React from "react";

const TableSkeleton = () => {
  return (
    <Card className="p-0">
      <div className="rounded-lg overflow-hidden animate-pulse">
        <div
          style={{ height: 59 }}
          className="bg-gray-200 flex items-center justify-around"
        >
          {Array.from({ length: 6 }).map((_, j) => (
            <div key={j} className={cn("w-8 h-2 bg-gray-50 rounded-[2px]")} />
          ))}
        </div>
        {Array.from({ length: 4 }).map((_, i) => (
          <div
            key={i}
            className={cn(
              "border-t-2 border-gray-300 h-[51px] flex items-center justify-around",
              i % 2 === 0 ? "bg-white" : "bg-gray-200",
            )}
          >
            {Array.from({ length: 6 }).map((_, j) => (
              <div
                key={j}
                className={cn(
                  "w-8 h-2 bg-gray-300 rounded-[2px]",
                  i % 2 !== 0 ? "bg-gray-50" : "bg-gray-200",
                )}
              />
            ))}
          </div>
        ))}
      </div>
    </Card>
  );
};

export default TableSkeleton;
