[data-today="true"] button {
  text-decoration: underline 1.2px !important;
  text-decoration-color: var(--color-primary) !important;
  text-underline-offset: 4px !important;
  font-weight: 600 !important;
}

.rdp-range_middle[data-today="true"] button {
  text-decoration-color: white !important;
}

/* Ensure consistent range styling */
.rdp-range_start,
.rdp-range_end,
.rdp-range_middle {
  position: relative;
}

/* Make sure range middle dates have consistent background */
.rdp-range_middle button {
  border-radius: 0 !important;
}

/* Ensure start and end dates have proper rounded corners */
.rdp-range_start button {
  border-radius: 9999px 0 0 9999px !important;
}

.rdp-range_end button {
  border-radius: 0 9999px 9999px 0 !important;
}
