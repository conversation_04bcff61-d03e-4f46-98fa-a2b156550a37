"use client";

import { Toaster, toast } from "react-hot-toast";
import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/solid";

// Custom close button component with framer-motion
const CustomCloseButton: React.FC<{ toastId: string }> = ({ toastId }) => {
  return (
    <motion.button
      onClick={() => toast.dismiss(toastId)}
      className="absolute top-1/2 -translate-y-1/2 right-3 p-2 rounded-full transition-all duration-200 hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-primary/20"
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      aria-label="Close notification"
    >
      <XMarkIcon className="w-5 h-5 text-secondary/60 hover:text-secondary" />
    </motion.button>
  );
};

// Custom toast container with framer-motion
const CustomToastContainer: React.FC<{
  children: React.ReactNode;
  type: "success" | "error" | "loading" | "default";
  visible: boolean;
}> = ({ children, type, visible }) => {
  const getTypeStyles = () => {
    switch (type) {
      case "success":
        return {
          background: "rgba(240, 253, 244, 1)",
          border: "1px solid rgba(52, 199, 89, 0.2)",
          progressColor: "rgba(52, 199, 89, 0.6)",
        };
      case "error":
        return {
          background: "rgba(254, 242, 242, 1)",
          border: "1px solid rgba(255, 59, 48, 0.2)",
          progressColor: "rgba(255, 59, 48, 0.6)",
        };
      case "loading":
        return {
          background: "rgba(250, 245, 255, 1)",
          border: "1px solid rgba(145, 74, 196, 0.2)",
          progressColor: "rgba(145, 74, 196, 0.6)",
        };
      default:
        return {
          background: "#ffffff",
          border: "1px solid rgba(0, 0, 0, 0.08)",
          progressColor: "rgba(0, 0, 0, 0.1)",
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, x: 100, scale: 0.95 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 100, scale: 0.95 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4,
          }}
          style={{
            background: styles.background,
            border: styles.border,
            maxWidth: type === "error" ? "450px" : "420px",
            minHeight: type === "error" ? "65px" : "60px",
            padding: type === "error" ? "20px" : "18px",
            paddingRight: "50px",
            borderRadius: "8px",
            position: "relative",
            overflow: "hidden",
            fontSize: "15px",
            fontWeight: "500",
            color: "#344054",
            display: "flex",
            alignItems: "flex-start",
            gap: "12px",
          }}
          className="relative"
        >
          {children}

          {/* Progress bar */}
          <motion.div
            className="absolute bottom-0 left-0 h-[3px]"
            style={{ backgroundColor: styles.progressColor }}
            initial={{ width: "100%" }}
            animate={{ width: "0%" }}
            transition={{ duration: 2.5, ease: "linear" }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Toast provider component with custom framer-motion animations
export const ToastProvider = () => {
  return (
    <Toaster
      position="top-right"
      reverseOrder={false}
      gutter={12}
      containerStyle={{
        top: 20,
        right: 20,
      }}
      toastOptions={{
        duration: 2500,
        style: {
          background: "transparent",
          boxShadow: "none",
          padding: 0,
          margin: 0,
        },
      }}
    >
      {(t) => (
        <CustomToastContainer
          type={
            t.type === "success"
              ? "success"
              : t.type === "error"
              ? "error"
              : t.type === "loading"
              ? "loading"
              : "default"
          }
          visible={t.visible}
        >
          {/* Icon */}
          {t.type === "success" && (
            <div className="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full text-green-600 flex-shrink-0">
              <CheckCircleIcon className="w-4 h-4" />
            </div>
          )}
          {t.type === "error" && (
            <div className="flex items-center justify-center w-6 h-6 bg-red-100 rounded-full text-red-600 flex-shrink-0">
              <XCircleIcon className="w-4 h-4" />
            </div>
          )}
          {t.type === "loading" && (
            <div className="flex items-center justify-center w-6 h-6 bg-purple-100 rounded-full flex-shrink-0">
              <motion.svg
                className="w-4 h-4"
                style={{ color: "rgba(145, 74, 196, 1)" }}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </motion.svg>
            </div>
          )}
          {t.type === "blank" && t.icon && (
            <div className="flex items-center justify-center w-6 h-6 flex-shrink-0">
              {t.icon}
            </div>
          )}

          {/* Message */}
          <div className="flex-1 text-sm leading-relaxed">
            {typeof t.message === "function" ? t.message(t) : t.message}
          </div>

          {/* Close Button */}
          <CustomCloseButton toastId={t.id} />
        </CustomToastContainer>
      )}
    </Toaster>
  );
};
