"use client";

import React, { useState, ReactNode, createContext, useContext } from "react";

/* ============================= MAINTAIN HOOKS ============================= */
import { useClickOutside } from "@mantine/hooks";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* ============================== FRAMER MOTION ============================= */
import { motion, AnimatePresence } from "framer-motion";

/* ================================== ICONS ================================= */
import { IoChevronDown } from "react-icons/io5";

/* ================================== TYPES ================================= */
type DropdownProps = { children: ReactNode; className?: string };

type DropdownButtonProps = {
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  chevronClassName?: string;
};

type DropdownOptionsProps = {
  children: ReactNode;
  setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  className?: string;
};

type DropdownOptionProps = {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
};

type DropdownContextType = {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  maxOptionWidth: number;
  setMaxOptionWidth: React.Dispatch<React.SetStateAction<number>>;
};

export const DropdownContext = createContext<DropdownContextType | null>(null);

export const useDropdownContext = () => {
  const context = useContext(DropdownContext);
  if (!context) throw new Error("Dropdown.* must be used within <Dropdown>");
  return context;
};

/* ========================================================================== */
/**
 * Main Dropdown wrapper that manages internal open/close state.
 * This is an uncontrolled component, meaning it owns its own state.
 *
 * Usage:
 * ```tsx
 * <Dropdown>
 *   <Dropdown.Button>Click Me</Dropdown.Button>
 *   <Dropdown.Options>
 *     <Dropdown.Option onClick={...}>Option 1</Dropdown.Option>
 *     <Dropdown.Option>Option 2</Dropdown.Option>
 *   </Dropdown.Options>
 * </Dropdown>
 * ```
 */
const Dropdown = ({ children, className }: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [maxOptionWidth, setMaxOptionWidth] = useState(0);

  const ref = useClickOutside(() => {
    if (isOpen) setIsOpen(false);
  });

  const renderChildren = React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) return child;

    return child;
  });

  return (
    <DropdownContext.Provider
      value={{ isOpen, setIsOpen, maxOptionWidth, setMaxOptionWidth }}
    >
      <div ref={ref} className={cn("relative inline-block z-20", className)}>
        {renderChildren}
      </div>
    </DropdownContext.Provider>
  );
};

/* ========================================================================== */
const DropdownButton = ({
  children,
  className,
  style,
  chevronClassName,
}: DropdownButtonProps) => {
  const { isOpen, setIsOpen, maxOptionWidth } = useDropdownContext();

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <button
      onClick={() => setIsOpen && setIsOpen(!isOpen)}
      className={cn(
        `px-2.5 py-3.5 bg-[#F4F4F4] text-secondary z-40 font-bold gap-2 text-nowrap ${
          isOpen ? "rounded-t-lg" : "rounded-lg border-0"
        } focus:outline-none flex items-center text-xs justify-between`,
        className
      )}
      type="button"
      style={{
        ...style,
        minWidth: maxOptionWidth > 0 ? `${maxOptionWidth}px` : 'auto',
      }}
    >
      {children}
      <motion.span
        animate={{ rotate: isOpen ? 180 : 0 }}
        transition={{ duration: 0.3 }}
        style={{ display: "flex", alignItems: "center" }}
      >
        <IoChevronDown className={cn("text-lg", chevronClassName)} />
      </motion.span>
    </button>
  );
};

/* ========================================================================== */
const DropdownOptions = ({ children, className }: DropdownOptionsProps) => {
  const { isOpen, setMaxOptionWidth } = useDropdownContext();
  const optionsRef = React.useRef<HTMLUListElement>(null);
  const measurementRef = React.useRef<HTMLUListElement>(null);

  // Calculate max width on mount and when children change
  React.useEffect(() => {
    if (measurementRef.current) {
      const options = measurementRef.current.children;
      let maxWidth = 0;

      Array.from(options).forEach((option) => {
        const optionWidth = option.scrollWidth;
        maxWidth = Math.max(maxWidth, optionWidth);
      });

      // Add padding to account for button padding
      const buttonPadding = 20; // px-2.5 (10px) * 2
      setMaxOptionWidth(maxWidth + buttonPadding);
    }
  }, [children, setMaxOptionWidth]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <>
      {/* Hidden measurement element */}
      <ul
        ref={measurementRef}
        className="absolute -top-96 left-0 invisible pointer-events-none bg-[#F4F4F4] text-secondary text-xs"
        style={{ zIndex: -1 }}
      >
        {children}
      </ul>

      <AnimatePresence>
        {isOpen && (
          <motion.ul
            ref={optionsRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.15 }}
            className={cn(
              "absolute w-full bg-[#F4F4F4] rounded-b-lg text-secondary text-xs min-w-max",
              className
            )}
          >
            {children}
          </motion.ul>
        )}
      </AnimatePresence>
    </>
  );
};

/* ========================================================================== */
const DropdownOption = ({
  children,
  className,
  onClick,
}: DropdownOptionProps) => {
  const { setIsOpen } = useDropdownContext();

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <li
      onClick={() => {
        onClick?.();
        setIsOpen?.(false);
      }}
      className={cn(
        `px-4 py-2 cursor-pointer hover:bg-primary/10 transition-colors border-t-1 last:rounded-b-lg whitespace-nowrap`,
        className
      )}
    >
      {children}
    </li>
  );
};

Dropdown.Button = DropdownButton;
Dropdown.Options = DropdownOptions;
Dropdown.Option = DropdownOption;

export default Dropdown;
