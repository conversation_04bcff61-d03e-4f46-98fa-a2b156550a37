"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useParams } from "next/navigation";
import httpService from "@/services/httpService";

interface ProjectContextType {
  projectId: string | null;
  isLoading: boolean;
  error: string | null;
  isValidProject: boolean;
}

interface ProjectProviderProps {
  children: React.ReactNode;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

/**
 * Project Context Provider
 * Manages project ID extraction from URL parameters and provides it to all child components
 */
export function ProjectProvider({ children }: ProjectProviderProps) {
  const params = useParams<{ projectId: string }>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isValidProject, setIsValidProject] = useState(false);
  const [validatedProjects, setValidatedProjects] = useState<Set<string>>(
    new Set()
  );

  // Extract project ID from URL parameters
  const projectId = params?.projectId || null;

  useEffect(() => {
    const validateProject = async () => {
      setIsLoading(true);
      setError(null);

      if (!projectId) {
        setError("Project ID is missing from URL");
        setIsValidProject(false);
        setIsLoading(false);
        return;
      }

      // Basic validation - check if projectId is a valid format
      if (typeof projectId !== "string" || projectId.trim().length === 0) {
        setError("Invalid project ID format");
        setIsValidProject(false);
        setIsLoading(false);
        return;
      }

      // Check if project is already validated (performance optimization)
      if (validatedProjects.has(projectId)) {
        setIsValidProject(true);
        setIsLoading(false);
        return;
      }

      // API validation - check if project exists and user has access
      try {
        const response = await httpService.get(
          `/api/dashboard/project/${projectId}/validate`,
          { useAuth: true }
        );

        if (response.data?.isValid) {
          setIsValidProject(true);
          // Cache the validated project ID
          setValidatedProjects((prev) => new Set(prev).add(projectId));
        } else {
          setError(
            response.data?.message || "Project not found or access denied"
          );
          setIsValidProject(false);
        }
      } catch (apiError: any) {
        // If API validation fails, fall back to basic validation for development
        console.warn(
          "Project API validation failed, using basic validation:",
          apiError.message
        );

        // Basic validation - assume project is valid if ID format is correct
        setIsValidProject(true);
        // Cache the validated project ID for fallback case
        setValidatedProjects((prev) => new Set(prev).add(projectId));
      }

      setIsLoading(false);
    };

    validateProject();
  }, [projectId]);

  const contextValue: ProjectContextType = {
    projectId,
    isLoading,
    error,
    isValidProject,
  };

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
}

/**
 * Hook to access project context
 * Provides project ID and related state to components
 */
export function useProjectContext(): ProjectContextType {
  const context = useContext(ProjectContext);

  if (context === undefined) {
    throw new Error("useProjectContext must be used within a ProjectProvider");
  }

  return context;
}

/**
 * Hook to get project ID directly
 * Convenience hook that returns just the project ID
 */
export function useProjectId(): string | null {
  const { projectId } = useProjectContext();
  return projectId;
}
