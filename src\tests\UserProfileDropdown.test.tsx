/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import UserProfileDropdown from '@/components/auth/UserProfileDropdown';
import { useAuth } from '@/providers/AuthProvider';

// Mock the AuthProvider
jest.mock('@/providers/AuthProvider', () => ({
  useAuth: jest.fn(),
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    button: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    ),
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h3: ({ children, ...props }: any) => <h3 {...props}>{children}</h3>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock user data
const mockUser = {
  email: '<EMAIL>',
  first_name: 'John',
  last_name: 'Doe',
  is_verified: true,
  date_joined: '2023-01-01',
  auth_provider: 'email',
  google_id: null,
  avatar_url: 'https://example.com/avatar.jpg',
  subscriptions: [
    {
      plan_name: 'Pro Plan',
      status: 'active',
      current_period_start: '2023-01-01',
      current_period_end: '2024-01-01',
      trial_start: null,
      trial_end: null,
      price_amount: 29.99,
      price_currency: 'USD',
      price_interval: 'month',
      price_interval_count: 1,
      auto_renew: true,
    },
  ],
};

const mockAuthContext = {
  user: mockUser,
  logout: jest.fn(),
  refreshProfile: jest.fn().mockResolvedValue(true),
  isAuthenticated: true,
  isLoading: false,
  error: null,
  login: jest.fn(),
  register: jest.fn(),
  verifyEmail: jest.fn(),
  requireLogin: jest.fn(),
  redirectToLogin: jest.fn(),
  hasTokens: true,
  isInitialized: true,
  isHydrated: true,
};

describe('UserProfileDropdown', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    (useAuth as jest.Mock).mockReturnValue(mockAuthContext);
  });

  it('renders user profile button', () => {
    render(<UserProfileDropdown />);
    
    const profileButton = screen.getByLabelText('User profile');
    expect(profileButton).toBeInTheDocument();
  });

  it('displays cached user data when dropdown opens', async () => {
    // Mock cached data
    const cachedData = {
      data: mockUser,
      timestamp: Date.now() - 1000, // 1 second ago
    };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(cachedData));

    render(<UserProfileDropdown />);
    
    const profileButton = screen.getByLabelText('User profile');
    fireEvent.click(profileButton);

    await waitFor(() => {
      expect(screen.getByText('👋 Hi John')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Pro Plan')).toBeInTheDocument();
    });
  });

  it('refreshes profile data in background when dropdown opens', async () => {
    render(<UserProfileDropdown />);
    
    const profileButton = screen.getByLabelText('User profile');
    fireEvent.click(profileButton);

    await waitFor(() => {
      expect(mockAuthContext.refreshProfile).toHaveBeenCalledWith(true);
    });
  });

  it('clears cache on logout', async () => {
    render(<UserProfileDropdown />);
    
    const profileButton = screen.getByLabelText('User profile');
    fireEvent.click(profileButton);

    await waitFor(() => {
      const logoutButton = screen.getByText('Log Out');
      fireEvent.click(logoutButton);
    });

    expect(localStorageMock.removeItem).toHaveBeenCalledWith('user_profile_cache');
    expect(mockAuthContext.logout).toHaveBeenCalled();
  });

  it('handles expired cache correctly', () => {
    // Mock expired cached data
    const expiredCachedData = {
      data: mockUser,
      timestamp: Date.now() - (6 * 60 * 1000), // 6 minutes ago (expired)
    };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredCachedData));

    render(<UserProfileDropdown />);

    // Should remove expired cache
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('user_profile_cache');
  });

  it('falls back to auth provider data when cache is unavailable', async () => {
    localStorageMock.getItem.mockReturnValue(null);

    render(<UserProfileDropdown />);
    
    const profileButton = screen.getByLabelText('User profile');
    fireEvent.click(profileButton);

    await waitFor(() => {
      expect(screen.getByText('👋 Hi John')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('updates cache when auth provider data changes', () => {
    const { rerender } = render(<UserProfileDropdown />);

    // Update user data
    const updatedUser = { ...mockUser, first_name: 'Jane' };
    (useAuth as jest.Mock).mockReturnValue({
      ...mockAuthContext,
      user: updatedUser,
    });

    rerender(<UserProfileDropdown />);

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'user_profile_cache',
      expect.stringContaining('"first_name":"Jane"')
    );
  });
});
