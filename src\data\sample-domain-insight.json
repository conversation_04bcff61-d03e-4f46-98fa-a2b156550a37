{"status": "success", "result": {"links": {"overall_title": "Backlinks Analysis", "overall_description": "Comprehensive analysis of your website's backlink profile and domain authority metrics.", "total_score": {"score": 75, "grade": "B+"}, "backlinks": {"pass": true, "total_backlinks": 1247, "unique_domains": 89, "sample_backlinks": [{"link": "https://example.com/blog/seo-tips", "title": "10 Essential SEO Tips for 2024", "description": "A comprehensive guide to modern SEO practices and techniques."}, {"link": "https://techblog.com/web-optimization", "title": "Web Optimization Best Practices", "description": "Learn how to optimize your website for better performance and rankings."}, {"link": "https://marketingsite.com/digital-strategy", "title": "Digital Marketing Strategy Guide", "description": "Complete guide to building an effective digital marketing strategy."}], "domains": [{"domain": "example.com", "count": 15}, {"domain": "techblog.com", "count": 12}, {"domain": "marketingsite.com", "count": 8}], "description": "Backlinks are links from other websites pointing to your site, indicating trust and authority.", "importance": "Backlinks are one of the most important ranking factors for search engines.", "recommendation": {"text": "Continue building high-quality backlinks from authoritative domains.", "priority": "High"}, "blog": "https://blog.example.com/backlink-strategy", "score": 78}, "backlinks_detail": {"overall_title": "Backlinks Detail", "overall_description": "Breaks down backlink metrics including link types, referring domains, and top linking domains and subdomains.", "nofollow_links": 342, "dofollow_links": 905, "unique_domains": 89, "top_backlinks_root_domain": [{"domain": "example.com", "count": 15}, {"domain": "techblog.com", "count": 12}, {"domain": "marketingsite.com", "count": 8}, {"domain": "seotools.org", "count": 7}, {"domain": "webdev.io", "count": 6}], "top_backlinks_sub_domain": [{"domain": "blog.example.com", "count": 10}, {"domain": "news.techblog.com", "count": 8}, {"domain": "resources.marketingsite.com", "count": 6}, {"domain": "tools.seotools.org", "count": 5}, {"domain": "tutorials.webdev.io", "count": 4}]}, "domain_insight": {"pass": true, "overall_title": "Domain Authority Overview", "overall_description": "Summarizes <PERSON><PERSON> metrics for this page and domain.", "domain_authority": 45, "page_authority": 38, "spam_score": 3, "trust_flow": 42, "citation_flow": 35, "referring_domains": 89, "total_backlinks": 1247, "description": "Domain insight provides comprehensive metrics about your domain's authority and link profile using industry-standard tools like Moz and Majestic.", "importance": "Domain authority metrics help understand your site's credibility and ranking potential compared to competitors.", "recommendation": {"text": "Your domain authority is moderate. Focus on acquiring high-quality backlinks from authoritative sites to improve these metrics.", "priority": "Medium"}, "blog": "https://blog.example.com/domain-authority-guide", "score": 72}, "friendly_links": {"pass": false, "friendly_percentage": 65, "unfriendly_links_sample": [{"url": "https://example.com/page1", "text": "click here", "score": 2}, {"url": "https://example.com/page2", "text": "read more", "score": 3}, {"url": "https://example.com/page3", "text": "here", "score": 1}], "description": "Analyzes the readability and descriptiveness of link anchor text on your page.", "importance": "Clear, descriptive anchor text improves user experience and helps search engines understand link context.", "recommendation": {"text": "Replace generic anchor text like 'click here' with more descriptive text that explains the link destination.", "priority": "Medium"}, "blog": "https://blog.example.com/anchor-text-best-practices", "score": 65}, "broken_links": {"pass": true, "total_checked": 156, "broken_count": 2, "broken_percentage": 1.3, "sample_broken_links": [{"url": "https://oldsite.com/removed-page", "status": "404", "details": 404}, {"url": "https://example.com/old-resource", "status": "500", "details": 500}], "description": "Identifies broken links on your page that return error status codes.", "importance": "Broken links negatively impact user experience and can hurt your SEO rankings.", "recommendation": {"text": "Fix or remove the 2 broken links found on your page to improve user experience.", "priority": "Medium"}, "blog": "https://blog.example.com/fixing-broken-links", "score": 87}}}}