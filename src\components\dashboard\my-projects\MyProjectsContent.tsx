"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import BoxDashboard from "@/components/dashboard/BoxDashboard";
import TitlePageDashboard from "@/components/dashboard/TitlePageDashboard";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import FetchLoadingBox from "@/components/shared/FetchLoadingBox";
import ErrorFetch from "@/components/shared/ErrorFetch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Image from "next/image";
import Link from "next/link";
import profileService from "@/services/profileService";
import projectAPI from "@/services/projectService";
import { ProjectListItem } from "@/services/projectService";
import { showToast } from "@/lib/toast";
import ProjectsList from "./ProjectsList";
import ProjectsFilter from "./ProjectsFilter";
import ProjectSearch from "./ProjectSearch";

const dataFilter = [
  {
    name: "Newest first",
    query: "newest-first",
  },
  {
    name: "Oldest first",
    query: "oldest-first",
  },
  {
    name: "Alphabetical (A-Z)",
    query: "alphabetical",
  },
  {
    name: "Alphabetical (Z-A)",
    query: "alphabetical-desc",
  },
  {
    name: "By status",
    query: "by-status",
  },
];

export default function MyProjectsContent() {
  const [closeModal, setCloseModal] = useState<boolean>(false);
  const search = useSearchParams();
  const [open, setOpen] = useState<boolean>(false);
  const [idProject, setIdProject] = useState<string | null>(null);
  const [filter, setFilter] = useState<{ name: string; query: string }>({
    name: "Newest first",
    query: "newest-first",
  });
  const route = useRouter();
  const queryClient = useQueryClient();

  const {
    data: profileSetting,
    isFetching: profileFething,
    isError: profileError,
  } = useQuery({
    queryKey: ["ProfileSetting"],
    queryFn: profileService.getProfileSetting,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });

  // Helper function to map filter query to API ordering parameter
  const getOrderingParam = (filterQuery: string) => {
    switch (filterQuery) {
      case "newest-first":
        return "-created_at"; // Newest created first
      case "oldest-first":
        return "created_at"; // Oldest created first
      case "alphabetical":
        return "project_name"; // Alphabetical by name (A-Z)
      case "alphabetical-desc":
        return "-project_name"; // Alphabetical by name (Z-A)
      case "by-status":
        return "-status"; // By status (enabled first, then disabled)
      default:
        return "-created_at"; // Default to newest first
    }
  };

  // Helper function to transform API data to component format
  const transformProjectData = (apiData: ProjectListItem[]) => {
    return apiData.map((project) => ({
      id: project.id,
      name: project.project_name,
      url: project.url,
      createdAt: project.created_at,
      color: project.project_color || "#8C00FF", // Default to purple if no color
      status: project.status,
    }));
  };

  const {
    data: projectsResponse,
    isFetching: projectFetching,
    isError,
  } = useQuery({
    queryKey: ["AllProjects", search?.get("search"), search?.get("filter")],
    queryFn: async () => {
      const searchParam = search?.get("search") || undefined;
      const filterParam = search?.get("filter") || "newest-first";
      const ordering = getOrderingParam(filterParam);

      const response = await projectAPI.getAllProjects({
        search: searchParam,
        ordering,
        page_size: 50, // Get more projects per page
      });

      return response.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });

  // Transform the API data for the component
  const allProject = projectsResponse?.results
    ? transformProjectData(projectsResponse.results)
    : undefined;

  const { mutate: switchProject, isPending: switchPending } = useMutation({
    mutationFn: async ({
      id,
      currentStatus,
    }: {
      id: string;
      currentStatus: "enabled" | "disabled";
    }) => {
      const newStatus = currentStatus === "enabled" ? "disabled" : "enabled";
      const response = await projectAPI.updateProjectStatus(id, newStatus);
      return response.data;
    },
    onSuccess: () => {
      showToast.success("Project status updated successfully");
      queryClient.invalidateQueries({ queryKey: ["AllProjects"] });
    },
    onError: (error) => {
      console.error("Switch project error:", error);
      showToast.error("Failed to update project status");
    },
  });

  const { mutate: handleExport, isPending: exportPending } = useMutation({
    mutationFn: async (id: string) => {
      // TODO: Implement project export API call when available
      setCloseModal((prev) => !prev);
      return new Promise((res) => setTimeout(() => res(true), 1000));
    },
    onSuccess: () => {
      showToast.success("Project exported successfully");
      setCloseModal((prev) => !prev);
    },
    onError: (error) => {
      console.error("Export project error:", error);
      showToast.error("Failed to export project");
      setCloseModal((prev) => !prev);
    },
  });

  const { mutate: handleDelete, isPending: deletePending } = useMutation({
    mutationFn: async (projectId: string) => {
      const response = await projectAPI.deleteProject(projectId);
      return response.data;
    },
    onSuccess: () => {
      showToast.success("Project deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["AllProjects"] });
      setOpen(false);
      setIdProject(null);
    },
    onError: (error) => {
      console.error("Delete project error:", error);
      showToast.error("Failed to delete project");
    },
  });

  const handleEdit = (id: string) => {
    route.push(`/create-project/project-information?project_id=${id}`);
    setCloseModal((prev) => !prev);
  };

  // Wrapper function for switch project to match the expected interface
  const handleSwitchProject = (
    id: string,
    currentStatus: "enabled" | "disabled"
  ) => {
    switchProject({ id, currentStatus });
  };

  const filterProject = (query: string) => {
    const newData = dataFilter.find((i) => i.query === query);
    route.push(
      `?${
        search?.get("search") ? "search=" + search?.get("search") + "&" : ""
      }filter=${query} `
    );

    setFilter({
      name: newData?.name || "Newest first",
      query: newData?.query || "newest-first",
    });
    setCloseModal((prev) => !prev);
  };

  useEffect(() => {
    const filterParam = search?.get("filter");
    if (filterParam) {
      const isValidFilter = dataFilter.some(
        (item) => item.query === filterParam
      );

      if (!isValidFilter) {
        route.push("?filter=newest-first");
        setFilter({
          name: "Newest first",
          query: "newest-first",
        });
        return;
      }

      const newData = dataFilter.find((i) => i.query === filterParam);
      setFilter({
        name: newData?.name || "Newest first",
        query: newData?.query || "newest-first",
      });
    }
  }, [search, route]);

  // Show all filter options in dropdown (including current selection)
  const visibleFilters = dataFilter;

  return (
    <>
      <div className="w-full h-full flex flex-col lg:flex-row gap-4 min-h-0 overflow-hidden">
        <BoxDashboard classPlus="flex-1 min-h-0 w-full lg:w-5/12 flex flex-col overflow-hidden">
          <div className="flex-shrink-0">
            <TitlePageDashboard value="All Projects" />
            <ProjectSearch />
            <ProjectsFilter
              filter={filter}
              visibleFilters={visibleFilters}
              onFilterChange={filterProject}
              closeModal={closeModal}
            />
          </div>
          <div className="flex-1 min-h-0 flex flex-col">
            <ErrorFetch isError={isError}>
              <FetchLoadingBox isFetching={projectFetching}>
                <div className="flex-1 min-h-0">
                  <ProjectsList
                    projects={allProject}
                    onSwitchProject={handleSwitchProject}
                    onEdit={handleEdit}
                    onExport={handleExport}
                    onDelete={(id) => {
                      setIdProject(id);
                      setOpen(true);
                      setCloseModal((prev) => !prev); // Close the dropdown when opening delete modal
                    }}
                    switchPending={switchPending}
                    deletePending={deletePending}
                    exportPending={exportPending}
                    closeModal={closeModal}
                    filterKey={filter.query} // Pass filter key for animations
                  />
                </div>
              </FetchLoadingBox>
            </ErrorFetch>
          </div>
        </BoxDashboard>
        <BoxDashboard classPlus="flex-1 min-h-0 w-full lg:w-7/12 flex flex-col overflow-hidden">
          <FetchLoadingBox isFetching={profileFething}>
            <div className="flex relative items-center justify-center flex-1 min-h-0 flex-col gap-10 overflow-hidden">
              <ErrorFetch isError={profileError} nameError="Your">
                <></>
              </ErrorFetch>
              <span className="text-2xl font-semibold text-[#344054]">
                Hi {profileSetting?.first_name || `Erfan`}, Welcome back
              </span>
              <Image
                alt="kangaroo"
                src={"/images/dashboard/kangaroo.svg"}
                width={293}
                height={243}
              />
              <Link href={"/create-project"}>
                <ButtenSubmit text="Let's Create A Project" color="primary" />
              </Link>
            </div>
          </FetchLoadingBox>
        </BoxDashboard>
      </div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-md z-[10000]">
          <DialogHeader className="text-center">
            {/* Warning Icon */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
            </div>
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Delete Project
            </DialogTitle>
            <DialogDescription className="text-gray-600 text-sm leading-relaxed">
              Are you sure you want to delete this project? This action cannot
              be undone. All project data, including analytics, reports, and
              configurations will be permanently deleted.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex flex-col sm:flex-row gap-3 justify-center">
            <ButtenSubmit
              text="Delete Project"
              textloading="Deleting..."
              onClick={() => idProject && handleDelete(idProject)}
              isLoading={deletePending}
              classPluss="bg-red-700 hover:bg-red-800 border-red-700 text-white font-semibold sm:min-w-[140px] w-full order-2 sm:order-1"
            />
            <ButtenSubmit
              onClick={() => setOpen(false)}
              text="Cancel"
              color="primary__outline_hover"
              classPluss="sm:min-w-[140px] w-full order-1 sm:order-2"
            />
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
