"use client";
import React, { useState } from "react";
import { PlaceholderIcon } from "./WatermarkComponents";

// Simple Technology Icon component that matches the working TechnologySocials pattern
export const TechnologyIcon: React.FC<{
  name: string;
  index: number;
  onImageLoad?: (index: number, success: boolean) => void;
}> = ({ name, index, onImageLoad }) => {
  const [hasError, setHasError] = useState(false);

  // Function to get the correct icon URL with special handling for common technologies
  const getIconUrl = (name: string): string => {
    const lowerName = name.toLowerCase();

    // Special cases for common technologies
    if (lowerName.includes("facebook")) {
      return "https://cdn.simpleicons.org/facebook";
    }
    if (lowerName.includes("google analytics")) {
      return "https://cdn.simpleicons.org/googleanalytics";
    }
    if (lowerName.includes("google tag manager")) {
      return "https://cdn.simpleicons.org/googletagmanager";
    }
    if (lowerName.includes("linkedin")) {
      return "https://cdn.simpleicons.org/linkedin";
    }
    if (lowerName.includes("twitter")) {
      return "https://cdn.simpleicons.org/twitter";
    }
    if (lowerName.includes("reddit")) {
      return "https://cdn.simpleicons.org/reddit";
    }
    if (lowerName.includes("jquery")) {
      return "https://cdn.simpleicons.org/jquery";
    }

    // For all other names, remove spaces and convert to lowercase
    return `https://cdn.simpleicons.org/${name
      .replace(/\s+/g, "")
      .toLowerCase()}`;
  };

  const handleImageError = () => {
    setHasError(true);
    onImageLoad?.(index, false);
  };

  const handleImageLoad = () => {
    onImageLoad?.(index, true);
  };

  // If image failed to load, show placeholder
  if (hasError) {
    return <PlaceholderIcon />;
  }

  // Otherwise, try to load the actual image using regular img tag for better PDF compatibility
  return (
    <img
      src={getIconUrl(name)}
      alt={name}
      width={32}
      height={32}
      className="w-full h-full object-contain"
      onError={handleImageError}
      onLoad={handleImageLoad}
      style={{
        // Ensure image remains visible during print
        opacity: 1,
        visibility: "visible",
        display: "block",
      }}
    />
  );
};
