"use client";

import React from "react";
import { usePathname, useSearchParams } from "next/navigation";
import Link from "next/link";
import LoginButton from "@/components/auth/LoginButton";
import { IoIosArrowDown } from "react-icons/io";
import Image from "next/image";
import MenuMobile from "./MenuMobile";
import ProfileIcon from "@/ui/icons/dashboard/ProfileIcon";
import WalletIcon from "@/ui/icons/dashboard/WalletIcon";
import SettingIcon from "@/ui/icons/dashboard/SettingIcon";
import { GoProjectSymlink } from "react-icons/go";

const menuText = [
  // {
  //   url: '/profile',
  //   name: 'Profile',
  //   icon: <ProfileIcon />,
  //   children: undefined
  // },
  {
    url: "/my-projects",
    name: "My Projects",
    icon: <GoProjectSymlink className="text-xl" />,
    children: undefined,
  },
  {
    url: "/billing",
    name: "Billing",
    icon: <WalletIcon />,
    children: [
      // { url: '/billing?step=current-plan', name: 'Current Plan', step: 'current-plan' },
      { url: "/billing?step=invoices", name: "Invoices", step: "invoices" },
    ],
  },
  {
    url: "/setting",
    name: "Setting",
    icon: <SettingIcon />,
    children: undefined,
  },
];
type MenuLinkType = {
  childrenLink?: { url: string; name: string; step: string }[];
  name: string;
  url: string;
  icon: React.ReactNode;
};

export default function NavbarDashboard() {
  const path = usePathname();
  const params = useSearchParams();

  const NavigationLink = ({ name, childrenLink, url, icon }: MenuLinkType) => {
    const classLink = `${
      path === url ? "text-primary bg-[#914AC41A]" : ""
    }  hidden lg:flex group p-3 relative px-5 items-center justify-center rounded-md gap-2 hover:text-primary duration-300 ease-in-out`;
    const Child = () =>
      childrenLink?.length ? (
        <>
          <IoIosArrowDown className="group-hover:rotate-180 transition-all" />
          <div
            className="absolute left-1/2 top-11 transform transition-all -translate-x-1/2 translate-y-1/2 
                bg-white shadow-md rounded-b-md py-3 px-5 flex items-center gap-2 
                text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible duration-300 z-50"
          >
            {childrenLink.map((child) => (
              <Link
                href={child.url}
                key={child.name}
                className={`${
                  params?.get("step") === child.step
                    ? "text-primary bg-[#914AC41A] "
                    : null
                }  hover:text-primary duration-300 ease-in-out px-3 py-2 rounded-md whitespace-nowrap text-black`}
              >
                {child.name}
              </Link>
            ))}
          </div>
        </>
      ) : null;

    return childrenLink?.length ? (
      <button
        type="button"
        className={classLink + " cursor-pointer focus:border"}
      >
        {icon}
        {name}
        <Child />
      </button>
    ) : (
      <Link href={url} className={classLink}>
        {icon}
        {name}
        <Child />
      </Link>
    );
  };

  return (
    <div className="no-print w-full mx-auto relative">
      <div className="bg-white p-3 lg:p-6 rounded-xl shadow grid grid-cols-3 lg:flex gap-4 justify-between  items-center">
        <div className="col-span-1 lg:w-8/12 flex items-center order-2 lg:order-1 justify-start gap-6">
          <Link
            href={"/"}
            className="w-[60px] focus:border h-[30px] mx-auto lg:mx-0 lg:w-[105px] lg:h-[55px] relative inline-block lg:mr-7"
          >
            <Image
              src="/images/appLogo.svg"
              alt="seo analyser logo"
              fill
              quality={105}
              className="w-full h-full"
              priority
            />
          </Link>
          <div className="hidden focus:border lg:flex gap-4">
            {menuText.map((item, index) => (
              <NavigationLink
                key={index}
                childrenLink={item?.children}
                icon={item.icon}
                name={item.name}
                url={item.url}
              />
            ))}
          </div>
        </div>
        <div className="col-span-1 lg:hidden order-3 lg:order-1 flex items-center">
          <MenuMobile menuItems={menuText} />
        </div>
        <div className="col-span-1 flex gap-3 order-1 lg:order-3 items-center">
          <LoginButton />
        </div>
      </div>
    </div>
  );
}
