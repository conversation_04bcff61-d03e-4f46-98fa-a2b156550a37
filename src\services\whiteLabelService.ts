import http from "./httpService";

/**
 * Interface for White Label settings
 */
export interface WhiteLabelSettings {
  id?: number;
  brand_name: string;
  logo?: string; // URL to the logo image
  phone_number?: string;
  website?: string;
}

/**
 * Interface for the API response
 */
export interface WhiteLabelResponse {
  success: boolean;
  data?: WhiteLabelSettings;
  error?: string;
}

/**
 * Get existing white label settings
 * @returns Promise with the white label settings
 */
export async function getWhiteLabelSettings(): Promise<WhiteLabelResponse> {
  try {
    const response = await http.get("/api/accounts/whitelabel-setting/", {
      useAuth: true,
    });

    return {
      success: true,
      data: response.data,
    };
  } catch (error: any) {
    console.error("Error fetching white label settings:", error);

    // Extract error message
    let errorMessage =
      "Failed to fetch white label settings. Please try again.";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Save white label settings
 * @param data The white label settings data
 * @returns Promise with the API response
 */
export async function saveWhiteLabelSettings(
  data: FormData
): Promise<WhiteLabelResponse> {
  try {
    const response = await http.post(
      "/api/accounts/whitelabel-setting/",
      data,
      {
        useAuth: true,
        timeout: 10000,
      }
    );

    return {
      success: true,
      data: response.data,
    };
  } catch (error: any) {
    console.error("Error saving white label settings:", error);

    // Extract error message
    let errorMessage = "Failed to save white label settings. Please try again.";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Update existing white label settings
 * @param data The white label settings data
 * @returns Promise with the API response
 */
export async function updateWhiteLabelSettings(
  data: FormData
): Promise<WhiteLabelResponse> {
  try {
    const response = await http.patch(
      "/api/accounts/whitelabel-setting/",
      data,
      {
        useAuth: true,
        timeout: 10000,
      }
    );

    return {
      success: true,
      data: response.data,
    };
  } catch (error: any) {
    console.error("Error updating white label settings:", error);

    // Extract error message
    let errorMessage =
      "Failed to update white label settings. Please try again.";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

const whiteLabelService = {
  getWhiteLabelSettings,
  saveWhiteLabelSettings,
  updateWhiteLabelSettings,
};

export default whiteLabelService;
