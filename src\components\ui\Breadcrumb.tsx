"use client";
import React, { useState } from "react";

/* ================================== ICON ================================== */
import { IoLogoVk } from "react-icons/io";
import projectFavicon from "@/../public/images/my-dino.svg";

/* =============================== COMPONENTS =============================== */

import { usePathname } from "next/navigation";
import Dropdown from "@/components/ui/Dropdown";

/* ================================= ZUSTAND ================================ */
import { useProjectThemeColor } from "@/store/useProjectThemeColor";
import { cn } from "@/utils/cn";
import Image from "next/image";

/* ========================================================================== */
const Breadcrumb = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { themeColor } = useProjectThemeColor();
  const pathname = usePathname();
  const pathSegments = pathname
    ?.split("/")
    .filter(Boolean)
    .map((segment) =>
      segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );

  const quickAccess = [
    {
      id: 1,
      link: "https://seoanalyser.com.au",
      Icon: projectFavicon,
      color: themeColor,
    },
    {
      id: 2,
      link: "https://seoanalyser.com.au",
      Icon: projectFavicon,
      color: "#FF9500",
    },
  ];
  const displayInRouts = pathname?.startsWith("/project");
  const [selectedQuickAccess, setSelectedQuickAccess] = useState<number>(-1);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (!displayInRouts) return;

  return (
    <div className="flex flex-col-reverse md:flex-row justify-between mb-3.5">
      <div className="flex items-center gap-2 py-2.5 text-xs text-gray-400">
        {pathSegments?.map((crumb, index) => (
          <div key={index} className="flex gap-2">
            <span className={`cursor-default last:text-secondary`}>
              {crumb}
            </span>
            {index === pathSegments.length - 1 ? null : (
              <span className="cursor-default">|</span>
            )}
          </div>
        ))}
      </div>

      <div className="mx-auto md:mx-0">
        <Dropdown>
          <Dropdown.Button
            className="bg-white py-2 w-full h-10"
            style={{
              backgroundColor:
                selectedQuickAccess >= 0
                  ? `${quickAccess[selectedQuickAccess].color}1A`
                  : "#fff",
              border: `1px solid ${
                selectedQuickAccess >= 0
                  ? quickAccess[selectedQuickAccess].color
                  : "transparent"
              }`,
            }}
          >
            {selectedQuickAccess >= 0 && (
              <div
                className="w-6 h-6 flex-shrink-0"
                style={{
                  backgroundColor: quickAccess[selectedQuickAccess].color,
                  WebkitMaskImage: `url(${quickAccess[selectedQuickAccess].Icon.src})`,
                  maskImage: `url(${quickAccess[selectedQuickAccess].Icon.src})`,
                  WebkitMaskRepeat: "no-repeat",
                  maskRepeat: "no-repeat",
                  WebkitMaskSize: "contain",
                  maskSize: "contain",
                  WebkitMaskPosition: "center",
                  maskPosition: "center",
                  maskType: "alpha",
                }}
              />
            )}
            <span
              style={{
                color:
                  selectedQuickAccess >= 0
                    ? quickAccess[selectedQuickAccess].color
                    : "#344054",
              }}
            >
              {selectedQuickAccess >= 0
                ? quickAccess[selectedQuickAccess].link.slice(0, 19) + "..."
                : "select your project"}
            </span>
          </Dropdown.Button>
          <Dropdown.Options className="bg-white shadow-md">
            {quickAccess.map(({ link, Icon, color }, index) => (
              <Dropdown.Option
                key={index}
                className="flex items-center gap-2"
                onClick={() => {
                  setSelectedQuickAccess(index);
                }}
              >
                <div
                  className="w-6 h-6 flex-shrink-0"
                  style={{
                    backgroundColor: color,
                    WebkitMaskImage: `url(${quickAccess[index].Icon.src})`,
                    maskImage: `url(${quickAccess[index].Icon.src})`,
                    WebkitMaskRepeat: "no-repeat",
                    maskRepeat: "no-repeat",
                    WebkitMaskSize: "contain",
                    maskSize: "contain",
                    WebkitMaskPosition: "center",
                    maskPosition: "center",
                    maskType: "alpha",
                  }}
                />
                <span className="truncate">{link}</span>
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>
    </div>
  );
};

export default Breadcrumb;
