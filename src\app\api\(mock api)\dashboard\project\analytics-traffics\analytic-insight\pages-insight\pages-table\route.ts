import { TableDataRequest } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { NextResponse } from "next/server";

const PAGES_TABLE_DATA: TableDataRequest = {
  tableData: {
    tableHeadings: [
      "PAGE URL",
      "SESSIONS",
      "ENGAGED SESSIONS",
      "NEW USERS",
      "TOTAL USERS",
      "VIEWS",
      "ENGAGED TIME",
      "ENGAGED RATE",
      "EVENT COUNT",
      "CONVERSIONS",
    ],

    tableBody: [
      [
        { value: "Web/Page" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Web/Page" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Web/Page" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Web/Page" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
    ],
  },
  pagination: { totalPages: 20, initialPage: 1 },
};

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function GET() {
  await delay(1000);
  return NextResponse.json(PAGES_TABLE_DATA);
}
