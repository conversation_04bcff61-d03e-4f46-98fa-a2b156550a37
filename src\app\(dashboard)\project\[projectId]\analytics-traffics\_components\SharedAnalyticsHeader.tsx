"use client";
import React, { forwardRef, useRef, useState } from "react";
import Image from "next/image";
import { useClickOutside } from "@mantine/hooks";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import DateRangePicker from "./date-range-picker/DateRangePicker";
import { useAnalyticsConnection } from "@/contexts/AnalyticsConnectionContext";

/* ================================== ICONS ================================= */
import googleAnalyticsLogo from "@/../public/images/create-project/google-analytics.svg";
import googleLogo from "@/../public/images/create-project/google.svg";
import { FiCalendar } from "react-icons/fi";
import ShareAndSettings from "@/ui/ShareAndSettings";

/* ========================================================================== */
type SharedAnalyticsHeaderProps = {
  title: string;
};

const SharedAnalyticsHeader = forwardRef<
  HTMLDivElement,
  SharedAnalyticsHeaderProps
>(({ title }, ref) => {
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const toggleButtonRef = useRef<HTMLDivElement>(null);

  // Use the analytics connection context
  const {
    isGoogleAnalyticsConnected,
    isGoogleSearchConsoleConnected,
    connectGA,
    connectGSC,
    gaConnecting,
    gscConnecting,
  } = useAnalyticsConnection();

  const dateRangePickerRef = useClickOutside(() => {
    if (showDateRangePicker) {
      const isClickInsideToggle = toggleButtonRef.current?.contains(
        document.activeElement
      );

      if (!isClickInsideToggle) {
        setShowDateRangePicker(false);
      }
    }
  });

  const handleDateRangeClick = () => {
    setShowDateRangePicker((prev) => !prev);
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full space-y-4">
      <DateRangePicker
        ShowDateRangePicker={showDateRangePicker}
        ref={dateRangePickerRef}
      />
      <Card className="flex flex-col xl:flex-row gap-2 w-full h-fit justify-between">
        <div className="flex lg:flex-col gap-2 justify-between">
          <div className="flex justify-between">
            <h3 className="font-extrabold text-xl text-secondary">{title}</h3>
          </div>
          <div className="text-xs" ref={toggleButtonRef}>
            <Button
              variant={"secondary"}
              className="bg-gray-200 text-secondary hover:text-white"
              onClick={handleDateRangeClick}
            >
              <FiCalendar />
              <span style={{ userSelect: "none" }}>Select Date Range</span>
            </Button>
          </div>
        </div>
        <div className="flex flex-col items-end gap-2">
          <ShareAndSettings />
          <div className="w-full flex flex-col lg:flex-row justify-end gap-4">
            <Button
              variant={"secondary"}
              className={
                isGoogleAnalyticsConnected
                  ? "text-secondary hover:text-white border border-[#344054CC]"
                  : "text-white"
              }
              style={
                isGoogleAnalyticsConnected
                  ? {
                      backgroundColor: "#F4F4F4",
                    }
                  : undefined
              }
              onClick={() => {
                if (!isGoogleAnalyticsConnected && connectGA) {
                  connectGA();
                }
              }}
              disabled={gaConnecting || isGoogleAnalyticsConnected}
            >
              <Image src={googleAnalyticsLogo} alt={"Google Analytics Logo"} />
              <span>
                {gaConnecting
                  ? "Connecting..."
                  : isGoogleAnalyticsConnected
                  ? "Connected"
                  : "Connect Google Analytics"}
              </span>
            </Button>
            <Button
              variant={"secondary"}
              className={
                isGoogleSearchConsoleConnected
                  ? "text-secondary hover:text-white border border-[#344054CC]"
                  : "text-white"
              }
              style={
                isGoogleSearchConsoleConnected
                  ? {
                      backgroundColor: "#F4F4F4",
                    }
                  : undefined
              }
              onClick={() => {
                if (!isGoogleSearchConsoleConnected && connectGSC) {
                  connectGSC();
                }
              }}
              disabled={gscConnecting || isGoogleSearchConsoleConnected}
            >
              <Image src={googleLogo} alt={"Google Logo"} />
              <span>
                {gscConnecting
                  ? "Connecting..."
                  : isGoogleSearchConsoleConnected
                  ? "Connected"
                  : "Connect Google Search Console"}
              </span>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
});

SharedAnalyticsHeader.displayName = "SharedAnalyticsHeader";

export default SharedAnalyticsHeader;
