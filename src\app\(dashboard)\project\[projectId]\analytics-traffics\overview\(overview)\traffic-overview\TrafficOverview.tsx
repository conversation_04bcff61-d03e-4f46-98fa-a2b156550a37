import React, { useState } from "react";

/* ================================ SKELETON ================================ */
import Skeleton from "react-loading-skeleton";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import CardTab from "@/components/ui/card-tab/CardTab";
import OrganicTraffic from "./_components/OrganicTraffic";
import { Button } from "@/components/ui/button";
import DateRange from "../../../_components/date-range/DateRange";
import NoData from "../../../analytic-insight/_components/NoData";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

const TrafficOverViewSkeleton = () => {
  return (
    <div className="w-[90%] mx-8 p-2 space-y-2 border-l border-b">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index}>
          <Skeleton width={80} height={15} />
          <Skeleton width={`${Math.floor(Math.random() * 101)}%`} />
        </div>
      ))}
    </div>
  );
};

/* ================================ API CALLS =============================== */
import useTrafficOverview from "./TrafficOverview.hook";
import Link from "next/link";

/* ========================================================================== */
const TrafficOverview = () => {
  /* ================================ CONSTANTS =============================== */
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const trafficOverviewTabs = [
    {
      id: 1,
      title: "Total Users",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      id: 2,
      title: "New Users",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      id: 3,
      title: "Sessions",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      id: 4,
      title: "Active Users",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      id: 5,
      title: "Views",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      id: 6,
      title: "Event Count",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      id: 7,
      title: "Conversions",
      value: "10.3K",
      changeValue: "+21.2",
    },
  ];
  const [activeTab, setActiveTab] = useState(
    trafficOverviewTabs[0]?.title || "",
  );
  const {
    data: barsData,
    isLoading: barsDataIsLoading,
    isPending: barsDataIsPending,
    error: barsDataError,
  } = useTrafficOverview(activeTab);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (!barsData && !barsDataIsLoading && !barsDataIsPending && !barsDataError) {
    return <NoData title="Audience Overview" />;
  }

  return (
    <Card className="space-y-4">
      <Title>Traffic Overview</Title>
      <DateRange />

      <div className="flex justify-between overflow-x-auto gap-1 h-fit text-nowrap">
        {trafficOverviewTabs.map(({ id, title, value, changeValue }) => (
          <CardTab
            key={id}
            title={title}
            value={value}
            changeValue={changeValue}
            className={`border-2 `}
            style={
              activeTab === title
                ? { borderColor: themeColor }
                : { borderColor: "transparent" }
            }
            onSelect={() => setActiveTab(title)}
          />
        ))}
      </div>
      <div>
        {barsDataIsLoading || barsDataIsPending ? (
          <TrafficOverViewSkeleton />
        ) : (
          barsData && <OrganicTraffic barsData={barsData} />
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={"/project/analytics-traffics/analytic-insight?tab=traffics"}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default TrafficOverview;
