import ProgressChart from "@/ui/charts/ProgressChart";
import BoxPrimary from "../../BoxPrimary";
import ShowMoreSection from "../usability/ShowMoreSection";
import HeaderTags from "./HeaderTags";
import Keywords from "./Keywords";
import Analytics from "./Analytics";
import HeaderContent from "./HeaderContent";
import { DocumentCheckIcon, DocumentCrossIcon } from "@/ui/icons/general";
import { OnPageAnalysis } from "@/types/seoAnalyzerTypes";
import OverallSection from "../OverallSection";

// Using the imported OnPageAnalysis type from seoAnalyzerTypes.ts
// We need to export these types for use in child components
export type KeywordType = {
  keyword: string;
  frequency: number;
  in_title: boolean;
  in_meta_description: boolean;
  in_headings: boolean;
  in_footer?: boolean;
  percentage?: number;
};

export type PharseType = {
  phrase: string;
  frequency: number;
  in_title: boolean;
  in_meta_description: boolean;
  in_headings: boolean;
  in_footer?: boolean;
  percentage?: number;
};

type OnPageSeoProps = {
  results: Partial<OnPageAnalysis>;
};

export default function OnPageSeo({ results }: OnPageSeoProps) {
  // Always render with the passed data - no loading states
  // Use simple fallbacks for missing data

  // Extract the total score and grade - show default values if not available yet
  const totalScore = results?.total_score || { score: 0, grade: "F" };

  // Ensure grade is properly typed for ProgressChart
  const grade =
    (totalScore.grade as
      | "A+"
      | "A"
      | "A-"
      | "B+"
      | "B"
      | "B-"
      | "C+"
      | "C"
      | "C-"
      | "D+"
      | "D"
      | "D-"
      | "F") || "F";

  // We have data, so we should show the content immediately
  // No need to check for hasHeaderData since we have the results object

  return (
    <BoxPrimary title="On-Page SEO Results">
      <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
        <ProgressChart
          value={grade}
          title="On-Page SEO Score"
          size="lg"
          progressStates={[
            { label: "Grade", value: totalScore.score, isNoColor: false },
          ]}
        />

        <OverallSection
          title={results.overall_title || "On-Page SEO Analysis"}
          description={
            results.overall_description ||
            "Analyzing your page's on-page SEO elements..."
          }
        />
      </div>

      <div className="mt-8 space-y-6">
        {/* Title Tag Section - Show when available */}
        {results.title_tag && (
          <ShowMoreSection
            title="Title Tag"
            description={results.title_tag?.description || "Title tag analysis"}
            passed={results.title_tag?.is_optimal_length || false}
            icon={
              results.title_tag?.is_optimal_length ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.title_tag?.importance || "High"}
            recommendation={
              results.title_tag?.recommendation || {
                text: "Optimize title tag",
                priority: "High",
              }
            }
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Title:</strong>{" "}
                {results.title_tag?.title || "No title found"}
              </p>
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Length:</strong> {results.title_tag?.length || 0}{" "}
                characters
              </p>
            </div>
          </ShowMoreSection>
        )}

        {/* Meta Description Section - Show when available */}
        {results.meta_description && (
          <ShowMoreSection
            title="Meta Description Tag"
            description={
              results.meta_description?.description ||
              "Meta description analysis"
            }
            passed={results.meta_description?.pass || false}
            icon={
              results.meta_description?.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.meta_description?.importance || "High"}
            recommendation={
              results.meta_description?.recommendation || {
                text: "Optimize meta description",
                priority: "High",
              }
            }
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Content:</strong>{" "}
                {results.meta_description?.content ||
                  "No meta description found"}
              </p>
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Length:</strong> {results.meta_description?.length || 0}{" "}
                characters
              </p>
            </div>
          </ShowMoreSection>
        )}

        {results.serp_preview && (
          <ShowMoreSection
            title="SERP Snippet Preview"
            description={results.serp_preview.description}
            passed={true}
            icon={
              <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
            }
            importance={results.serp_preview.importance}
            recommendation={results.serp_preview.recommendation}
          >
            <div className="p-4 border border-gray-200 rounded-lg max-h-80 overflow-y-auto">
              <div className="flex gap-2">
                {" "}
                <div className="flex items-center ">
                  <div className="bg-gray-50 border border-gray-300 rounded-full p-2">
                    <img
                      src={results.serp_preview.favicon || ""}
                      alt="favicon"
                      className="w-4 h-4  "
                    />
                  </div>
                </div>
                <div className="flex flex-col">
                  <div>
                    {" "}
                    {
                      results.serp_preview.url
                        .replace(/^https?:\/\//, "") // Remove http:// or https://
                        .replace(/^www\./, "") // Remove www.
                        .split(".")[0]
                    }{" "}
                  </div>
                  <div className="flex items-center">
                    <p className="text-gray-600 text-sm break-all">
                      {results.serp_preview.url}
                    </p>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-2 w-4 h-4 text-gray-600"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 7a2 2 0 110-4 2 2 0 010 4zm0 5a2 2 0 110-4 2 2 0 010 4zm0 5a2 2 0 110-4 2 2 0 010 4z" />
                    </svg>
                  </div>
                </div>
              </div>

              <p className="text-blue-800 text-lg font-medium">
                {results.serp_preview.title}
              </p>

              <p className="text-gray-600 text-sm mt-1">
                {results.serp_preview.caption}
              </p>
            </div>
          </ShowMoreSection>
        )}

        {/* {results.language && results.language.hreflang && (
          <ShowMoreSection
            title="Hreflang Usage"
            description={results.language.description}
            passed={results.language.hreflang.exists}
            icon={
              results.language.hreflang.exists ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.language.importance}
            recommendation={results.language.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Declared Language:</strong> {results.language.declared}
              </p>
              {results.language.hreflang.tags &&
                results.language.hreflang.tags.length > 0 && (
                  <div className="mt-3">
                    <h6 className="text-sm font-semibold text-secondary mb-2">
                      Hreflang Tags:
                    </h6>
                    <div>
                      <table className="w-full text-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="p-2 text-left">Language</th>
                            <th className="p-2 text-left">URL</th>
                          </tr>
                        </thead>
                        <tbody>
                          {results.language.hreflang.tags.map(
                            (
                              tag: { lang: string; href: string },
                              index: number
                            ) => (
                              <tr
                                key={index}
                                className="border-b border-gray-100"
                              >
                                <td className="p-2">{tag.lang}</td>
                                <td className="p-2 break-all">{tag.href}</td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )} */}

        {results.headers && results.headers.h1 && (
          <ShowMoreSection
            title="H1 Header Tag Usage"
            description={results.headers.h1.description}
            passed={results.headers.h1.pass}
            icon={
              results.headers.h1.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.headers.h1.importance}
            recommendation={results.headers.h1.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <HeaderContent content={results.headers.h1.content} />
            </div>
          </ShowMoreSection>
        )}

        {results.language && (
          <ShowMoreSection
            title="Language"
            description={results.language.description}
            passed={true}
            icon={
              <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
            }
            importance={results.language.importance}
            recommendation={results.language.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Declared Language:</strong> {results.language.declared}
              </p>
            </div>
          </ShowMoreSection>
        )}

        {results.headers && results.headers.other_headers && (
          <div>
            <HeaderTags headers={results.headers.other_headers} />
          </div>
        )}

        {results.headers && (
          <ShowMoreSection
            title="H2-H6 Header Tag Usage"
            description={results.headers.h1.description}
            passed={results.headers.h1.pass}
            icon={
              results.headers.h1.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.headers.h1.importance}
            recommendation={results.headers.hierarchy_recommendation}
          >
            <div className="space-y-4 max-h-80 overflow-y-auto pr-2">
              {results.headers.other_headers?.h2?.content?.length > 0 && (
                <div>
                  <h6 className="text-sm font-semibold text-secondary mb-2">
                    H2 Headers:
                  </h6>
                  <HeaderContent
                    content={results.headers.other_headers.h2.content}
                  />
                </div>
              )}
              {results.headers.other_headers?.h3?.content?.length > 0 && (
                <div>
                  <h6 className="text-sm font-semibold text-secondary mb-2">
                    H3 Headers:
                  </h6>
                  <HeaderContent
                    content={results.headers.other_headers.h3.content}
                  />
                </div>
              )}
              {results.headers.other_headers?.h4?.content?.length > 0 && (
                <div>
                  <h6 className="text-sm font-semibold text-secondary mb-2">
                    H4 Headers:
                  </h6>
                  <HeaderContent
                    content={results.headers.other_headers.h4.content}
                  />
                </div>
              )}
              {results.headers.other_headers?.h5?.content?.length > 0 && (
                <div>
                  <h6 className="text-sm font-semibold text-secondary mb-2">
                    H5 Headers:
                  </h6>
                  <HeaderContent
                    content={results.headers.other_headers.h5.content}
                  />
                </div>
              )}
              {results.headers.other_headers?.h6?.content?.length > 0 && (
                <div>
                  <h6 className="text-sm font-semibold text-secondary mb-2">
                    H6 Headers:
                  </h6>
                  <HeaderContent
                    content={results.headers.other_headers.h6.content}
                  />
                </div>
              )}
            </div>
          </ShowMoreSection>
        )}

        {results.keyword_consistency && (
          <>
            <ShowMoreSection
              title="Keyword Consistency"
              description={
                results.keyword_consistency.description +
                results.keyword_consistency.error
              }
              passed={results.keyword_consistency.score > 3}
              icon={
                results.keyword_consistency.score > 3 ? (
                  <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
                ) : (
                  <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
                )
              }
              importance={results.keyword_consistency.importance}
              recommendation={results.keyword_consistency.recommendation}
            >
              <div className="max-h-80 overflow-y-auto pr-2"></div>
            </ShowMoreSection>
            <div>
              <Keywords
                pharses={results.keyword_consistency.phrases || []}
                data={results.keyword_consistency.keywords || []}
              />
            </div>
          </>
        )}

        {results.content_amount && (
          <ShowMoreSection
            title="Amount of Content"
            description={results.content_amount.description}
            passed={results.content_amount.pass}
            icon={
              results.content_amount.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.content_amount.importance}
            recommendation={results.content_amount.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="p-3 bg-gray-50 rounded">
                  <p className="text-sm font-semibold text-secondary">
                    Word Count
                  </p>
                  <p className="text-lg font-bold text-primary">
                    {results.content_amount.word_count}
                  </p>
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <p className="text-sm font-semibold text-secondary">
                    Text/HTML Ratio
                  </p>
                  <p className="text-lg font-bold text-primary">
                    {results.content_amount.text_html_ratio_percent}%
                  </p>
                </div>
              </div>
            </div>
          </ShowMoreSection>
        )}

        {results.image_alt_attributes && (
          <ShowMoreSection
            title="Image Alt Attributes"
            description={results.image_alt_attributes.description}
            passed={results.image_alt_attributes.pass}
            icon={
              results.image_alt_attributes.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.image_alt_attributes.importance}
            recommendation={results.image_alt_attributes.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div className="p-3 bg-gray-50 rounded">
                  <p className="text-sm font-semibold text-secondary">
                    Total Images
                  </p>
                  <p className="text-lg font-bold text-primary">
                    {results.image_alt_attributes.total_images}
                  </p>
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <p className="text-sm font-semibold text-secondary">
                    With Alt Text
                  </p>
                  <p className="text-lg font-bold text-primary">
                    {results.image_alt_attributes.images_with_alt}
                  </p>
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <p className="text-sm font-semibold text-secondary">
                    Missing Alt Text
                  </p>
                  <p className="text-lg font-bold text-primary">
                    {results.image_alt_attributes.images_without_alt}
                  </p>
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <p className="text-sm font-semibold text-secondary">
                    Missing Percentage
                  </p>
                  <p className="text-lg font-bold text-primary">
                    {results.image_alt_attributes.percent_missing}%
                  </p>
                </div>
              </div>
              {results.image_alt_attributes.missing_alt_images_sample &&
                results.image_alt_attributes.missing_alt_images_sample.length >
                  0 && (
                  <div className="mt-6">
                    <h6 className="text-sm font-semibold text-secondary mb-3">
                      Images Missing Alt Text:
                    </h6>
                    <div className="space-y-4">
                      {Array.isArray(
                        results.image_alt_attributes.missing_alt_images_sample
                      )
                        ? results.image_alt_attributes.missing_alt_images_sample
                            .slice(0, 10)
                            .map((img: any, index: number) => (
                              <div
                                key={index}
                                className="p-4 bg-white rounded-lg border border-gray-200 shadow-sm"
                              >
                                {typeof img === "string" ? (
                                  <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                    <div className="flex-1">
                                      <p className="text-sm text-secondary/80 break-all">
                                        <span className="font-semibold text-secondary">
                                          Source:
                                        </span>{" "}
                                        {img}
                                      </p>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="space-y-3">
                                    {/* Header with status indicator */}
                                    <div className="flex items-start gap-3">
                                      <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                      <div className="flex-1">
                                        <p className="text-sm text-secondary break-all">
                                          <span className="font-semibold">
                                            Image Source:
                                          </span>{" "}
                                          <span className="text-secondary/80">
                                            {img.src || "Empty/No source"}
                                          </span>
                                        </p>
                                      </div>
                                    </div>

                                    {/* Alt text status */}
                                    <div className="flex items-start gap-3">
                                      <div className="flex-shrink-0 w-6"></div>
                                      <div className="flex-1">
                                        <div className="flex items-center gap-2 flex-wrap">
                                          <span className="text-sm font-semibold text-secondary">
                                            Alt Text Status:
                                          </span>
                                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            {img.element?.match(
                                              /alt="([^"]*)"/
                                            )?.[1] !== undefined
                                              ? img.element.match(
                                                  /alt="([^"]*)"/
                                                )[1] === ""
                                                ? "Empty alt attribute"
                                                : img.element.match(
                                                    /alt="([^"]*)"/
                                                  )[1]
                                              : "No alt attribute"}
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* HTML Element */}
                                    <div className="flex items-start gap-3">
                                      <div className="flex-shrink-0 w-6"></div>
                                      <div className="flex-1">
                                        <div className="bg-gray-50 p-3 rounded-md border">
                                          <p className="text-xs font-semibold text-secondary mb-2">
                                            HTML Element:
                                          </p>
                                          <code className="text-xs text-gray-700 break-all leading-relaxed block">
                                            {img.element}
                                          </code>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            ))
                        : null}
                      {Array.isArray(
                        results.image_alt_attributes.missing_alt_images_sample
                      ) &&
                        results.image_alt_attributes.missing_alt_images_sample
                          .length > 10 && (
                          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center gap-3">
                              <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                              <p className="text-sm text-blue-700 font-medium">
                                And{" "}
                                {results.image_alt_attributes
                                  .missing_alt_images_sample.length - 10}{" "}
                                more images missing alt text...
                              </p>
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {results.canonical_tag && (
          <ShowMoreSection
            title="Canonical Tag"
            description={results.canonical_tag.description}
            passed={results.canonical_tag.pass}
            icon={
              results.canonical_tag.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.canonical_tag.importance}
            recommendation={results.canonical_tag.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Canonical URL:</strong>{" "}
                {results.canonical_tag.canonical_url}
              </p>
            </div>
          </ShowMoreSection>
        )}

        {results.noindex_tag && (
          <ShowMoreSection
            title="Noindex Tag Test"
            description={results.noindex_tag.description}
            passed={!results.noindex_tag.pass}
            icon={
              !results.noindex_tag.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.noindex_tag.importance}
            recommendation={results.noindex_tag.recommendation}
          />
        )}

        {results.noindex_header && (
          <ShowMoreSection
            title="Noindex Header Test"
            description={results.noindex_header.description}
            passed={!results.noindex_header.pass}
            icon={
              !results.noindex_header.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.noindex_header.importance}
            recommendation={results.noindex_header.recommendation}
          />
        )}

        {results.ssl_enabled && (
          <ShowMoreSection
            title="SSL Enabled"
            description={results.ssl_enabled.description}
            passed={results.ssl_enabled.pass}
            icon={
              results.ssl_enabled.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.ssl_enabled.importance}
            recommendation={results.ssl_enabled.recommendation}
          />
        )}

        {results.https_redirect && (
          <ShowMoreSection
            title="HTTPS Redirect"
            description={results.https_redirect.description}
            passed={results.https_redirect.pass}
            icon={
              results.https_redirect.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.https_redirect.importance}
            recommendation={results.https_redirect.recommendation}
          />
        )}

        {results.robots_txt && results.analytics && results.xml_sitemap && (
          <div>
            <Analytics
              robots={{
                status: results.robots_txt.pass,
                pass: results.robots_txt.pass,
                description: results.robots_txt.description,
                recommendation: results.robots_txt.recommendation,
                importance: results.robots_txt.importance,
                blog_url: results.robots_txt.blog,
              }}
              analytics={{
                status: results.analytics.pass,
                pass: results.analytics.pass,
                description: results.analytics.description,
                recommendation: results.analytics.recommendation,
                importance: results.analytics.importance,
                blog_url: results.analytics.blog,
                tools: results.analytics.detected_tools,
              }}
              sitemap={{
                status: results.xml_sitemap.pass,
                pass: results.xml_sitemap.pass,
                description: results.xml_sitemap.description,
                recommendation: results.xml_sitemap.recommendation,
                importance: results.xml_sitemap.importance,
                blog_url: results.xml_sitemap.blog,
                urls: results.xml_sitemap.sitemap_urls_found,
              }}
            />
          </div>
        )}
      </div>
    </BoxPrimary>
  );
}
