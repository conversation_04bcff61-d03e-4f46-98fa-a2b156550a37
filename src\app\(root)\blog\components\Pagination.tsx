"use client";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import clsx from "clsx";
import { ArrowRigthIcon } from "@/ui/icons/navigation";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  siblingCount?: number;
  boundaryCount?: number;
  searchQuery?: string;
  tagFilter?: string;
  nextPageUrl?: string | null;
  previousPageUrl?: string | null;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  siblingCount = 1,
  boundaryCount = 2,
  searchQuery,
  tagFilter,
  nextPageUrl,
  previousPageUrl,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());

    // Preserve search query if it exists
    if (searchQuery) {
      params.set("q", searchQuery);
    }

    // Preserve tag filter if it exists
    if (tagFilter) {
      params.set("tag", tagFilter);
    }

    return `${pathname}?${params.toString()}`;
  };

  const getPaginationItems = () => {
    const pages: (number | "...")[] = [];

    const startPages = Array.from(
      { length: Math.min(boundaryCount, totalPages) },
      (_, i) => i + 1
    );

    const endPages = Array.from(
      { length: boundaryCount },
      (_, i) => totalPages - boundaryCount + 1 + i
    ).filter((p) => p > boundaryCount);

    const siblingsStart = Math.max(
      Math.min(
        currentPage - siblingCount,
        totalPages - boundaryCount - siblingCount * 2 - 1
      ),
      boundaryCount + 2
    );

    const siblingsEnd = Math.min(
      Math.max(
        currentPage + siblingCount,
        boundaryCount + siblingCount * 2 + 2
      ),
      endPages.length > 0 ? endPages[0] - 2 : totalPages - 1
    );

    // Add start pages
    pages.push(...startPages);

    if (siblingsStart > boundaryCount + 2) {
      pages.push("...");
    } else if (boundaryCount + 1 < totalPages - boundaryCount) {
      pages.push(boundaryCount + 1);
    }

    // Add sibling pages
    for (let i = siblingsStart; i <= siblingsEnd; i++) {
      pages.push(i);
    }

    if (siblingsEnd < totalPages - boundaryCount - 1) {
      pages.push("...");
    } else if (endPages.length && siblingsEnd + 1 < endPages[0]) {
      pages.push(endPages[0] - 1);
    }

    // Add end pages
    pages.push(...endPages);

    return pages;
  };

  const paginationItems = getPaginationItems();

  return (
    <div className="flex items-center justify-between w-full mt-10 rounded-lg bg-[#914AC41A] p-4">
      {/* Previous Button */}
      <Link
        href={createPageURL(currentPage - 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === 1 || !previousPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        <ArrowRigthIcon className="rotate-180" />
        Previous
      </Link>

      {/* Full Pagination (Desktop) */}
      <div className="hidden sm:flex items-center gap-2">
        {paginationItems.map((item, index) =>
          item === "..." ? (
            <span key={index} className="px-3 py-1 text-gray-500">
              ...
            </span>
          ) : (
            <Link
              key={index}
              href={createPageURL(item)}
              className={clsx(
                "px-3 py-1 rounded text-secondary transition-all",
                item === currentPage
                  ? "bg-primary text-white"
                  : "hover:bg-gray-100"
              )}
            >
              {item}
            </Link>
          )
        )}
      </div>

      {/* Next Button */}
      <Link
        href={createPageURL(currentPage + 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === totalPages || !nextPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        Next
        <ArrowRigthIcon />
      </Link>
    </div>
  );
};

const NoNumberPagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  searchQuery,
  tagFilter,
  nextPageUrl,
  previousPageUrl,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());

    // Preserve search query if it exists
    if (searchQuery) {
      params.set("q", searchQuery);
    }

    // Preserve tag filter if it exists
    if (tagFilter) {
      params.set("tag", tagFilter);
    }

    return `${pathname}?${params.toString()}`;
  };

  return (
    <div className="flex items-center justify-between w-full  rounded-lg bg-[#914AC41A] p-4">
      {/* Previous Button */}
      <Link
        href={createPageURL(currentPage - 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === 1 || !previousPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        <ArrowRigthIcon className="rotate-180" />
        Previous
      </Link>

      {/* Next Button */}
      <Link
        href={createPageURL(currentPage + 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === totalPages || !nextPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        Next
        <ArrowRigthIcon />
      </Link>
    </div>
  );
};

export { Pagination, NoNumberPagination };
