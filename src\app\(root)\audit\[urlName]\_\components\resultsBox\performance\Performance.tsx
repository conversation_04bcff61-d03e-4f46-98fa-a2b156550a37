import BoxPrimary from "../../BoxPrimary";
import {
  CheckCircleIcon,
  CrossIcon,
  DocumentCheckIcon,
  DocumentCrossIcon,
} from "@/ui/icons/general";
import ResourcesCount from "./ResourcesCount";
import ProgressChart from "@/ui/charts/ProgressChart";
import ShowMoreSection from "../usability/ShowMoreSection";
import PerformanceGaugeChart from "@/ui/charts/PerfomanceGaugeChart";
import CompressionPieChart from "./CompressionPieChart";
import CompressionRates from "./CompressionRates";
import {
  PerformanceAnalysis,
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
} from "@/types/seoAnalyzerTypes";
import LoadingSlate from "@/components/loading/LoadingSlate";
import OverallSection from "../OverallSection";

// No type aliases needed

type PerformanceProps = {
  results: PerformanceAnalysis | string | null; // Allow for loading state
  pagespeedData?: PageSpeedAnalysis | null; // Optional pagespeed data
  pagespeedMobileData?: PageSpeedMobileAnalysis | null; // Optional pagespeed mobile data
};

export default function Performance({
  results,
  pagespeedData,
  pagespeedMobileData,
}: PerformanceProps) {
  // Optimized data handling - show content immediately when any data is available
  const hasAnyData =
    results &&
    typeof results === "object" &&
    results !== null &&
    Object.keys(results).length > 0;

  // Only show loading state if we have no data at all
  if (!hasAnyData) {
    return (
      <BoxPrimary title="Performance">
        <LoadingSlate
          title="Loading performance results..."
          showHeader={true}
          showCards={true}
          cardCount={6}
          showChart={true}
          showProgress={false}
          height="xl"
        />
      </BoxPrimary>
    );
  }

  // Check if we have header data
  const hasHeaderData = results.overall_title || results.overall_description;

  // Use the total_score from API response instead of calculating
  const overallScore = results.total_score?.score || 0;
  const grade =
    (results.total_score?.grade as
      | "A+"
      | "A"
      | "A-"
      | "B+"
      | "B"
      | "B-"
      | "C+"
      | "C"
      | "C-"
      | "D+"
      | "D"
      | "D-"
      | "F") || "F";

  // Calculate sections for the metrics display (keeping this for the passed/failed count)
  const sections = [
    results.javascript_errors,
    results.deprecated_html,
    results.compression,
    results.resource_count,
    results.amp,
    results.page_size,
    results.inline_styles,
    results.performance_timing,
    results.http_protocol,
    results.image_optimization,
    results.minification,
  ];

  const validSections = sections.filter((section) => section !== undefined);
  const passedSections = validSections.filter(
    (section) => section?.pass === true
  );

  // Create data for the performance metrics display
  const performanceMetrics = [
    {
      name: "JavaScript Errors",
      pass: results.javascript_errors?.pass,
      detail:
        results.javascript_errors?.error_count !== undefined
          ? `${results.javascript_errors.error_count} errors`
          : undefined,
    },
    {
      name: "Deprecated HTML",
      pass: results.deprecated_html?.pass,
      detail:
        results.deprecated_html?.count !== undefined
          ? `${results.deprecated_html.count} elements`
          : undefined,
    },
    {
      name: "Compression",
      pass: results.compression?.pass,
      detail:
        results.compression?.compression_ratio !== undefined
          ? `${
              results.compression?.compression_ratio !== undefined
                ? results.compression.compression_ratio.toFixed(1)
                : "0"
            }% ratio`
          : undefined,
    },
    {
      name: "Resource Count",
      pass: results.resource_count?.pass,
      detail:
        results.resource_count?.total !== undefined
          ? `${results.resource_count.total} resources`
          : undefined,
    },
    {
      name: "AMP",
      pass: results.amp?.pass,
      detail: results.amp?.is_amp_page ? "AMP enabled" : "Not AMP",
    },
    {
      name: "Page Size",
      pass: results.page_size?.pass,
      detail:
        results.page_size?.total_estimated_size_mb !== undefined
          ? `${
              results.page_size?.total_estimated_size_mb !== undefined
                ? results.page_size.total_estimated_size_mb.toFixed(2)
                : "0"
            } MB`
          : undefined,
    },
    {
      name: "Inline Styles",
      pass: results.inline_styles?.pass,
      detail:
        results.inline_styles?.inline_style_percentage !== undefined
          ? `${
              results.inline_styles?.inline_style_percentage !== undefined
                ? results.inline_styles.inline_style_percentage.toFixed(1)
                : "0"
            }% of elements`
          : undefined,
    },
    {
      name: "Performance Timing",
      pass: results.performance_timing?.pass,
      detail:
        results.performance_timing?.time_to_first_byte_s !== undefined
          ? `TTFB: ${
              results.performance_timing?.time_to_first_byte_s !== undefined
                ? results.performance_timing.time_to_first_byte_s.toFixed(2)
                : "0"
            }s`
          : undefined,
    },
    {
      name: "HTTP Protocol",
      pass: results.http_protocol?.pass,
      detail: results.http_protocol?.protocol_used,
    },
    {
      name: "Image Optimisation",
      pass: results.image_optimization?.pass,
      detail:
        results.image_optimization?.total_images !== undefined
          ? `${results.image_optimization.total_images} images`
          : undefined,
    },
    {
      name: "Minification",
      pass: results.minification?.pass,
      detail:
        results.minification?.unminified_js_count !== undefined &&
        results.minification?.unminified_css_count !== undefined
          ? `${
              results.minification.unminified_js_count +
              results.minification.unminified_css_count
            } unminified files`
          : undefined,
    },
  ];

  return (
    <BoxPrimary title="Performance">
      <div className="w-full flex flex-col lg:flex-row items-center lg:items-start gap-6">
        {/* Score Display */}
        <ProgressChart
          value={grade}
          title="Performance Score"
          size="lg"
          progressStates={[
            { label: "Score", value: overallScore, isNoColor: false },
          ]}
        />

        {hasHeaderData ? (
          <OverallSection
            title={results.overall_title || "Performance Analysis"}
            description={results.overall_description || "Analysis of your website's performance metrics."}
          />
        ) : (
          <div className="flex-1 p-4 lg:p-6 rounded-lg bg-primary/10 animate-pulse">
            <div className="h-5 bg-primary/20 rounded w-3/4 mb-3"></div>
            <div className="h-4 bg-primary/15 rounded w-full mb-2"></div>
            <div className="h-4 bg-primary/15 rounded w-5/6"></div>
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      <div className="my-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h4 className="font-semibold text-secondary">Performance Metrics</h4>
          <div className="flex items-center gap-4 mt-2 md:mt-0">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-primary-green mr-2"></div>
              <span className="text-xs text-secondary/60">
                Passed: {passedSections.length}
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-primary-red mr-2"></div>
              <span className="text-xs text-secondary/60">
                Failed: {validSections.length - passedSections.length}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {performanceMetrics.map((metric, index) => (
            <div
              key={index}
              className="flex items-center p-3 rounded-lg border border-light-gray"
            >
              <div className="mr-3">
                {metric.pass ? (
                  <CheckCircleIcon className="w-6 h-6 text-primary-green" />
                ) : (
                  <CrossIcon className="w-6 h-6 text-primary-red" />
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-secondary font-medium">
                  {metric.name}
                </span>
                {metric.detail && (
                  <span className="text-xs text-secondary/60">
                    {metric.detail}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* PageSpeed Desktop Section */}
      {pagespeedData && pagespeedData.performance_desktop && (
        <div className="my-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <h4 className="font-semibold text-secondary">
              PageSpeed Desktop Metrics
            </h4>
            <div className="flex items-center gap-4 mt-2 md:mt-0">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-primary-green mr-2"></div>
                <span className="text-xs text-secondary/60">Good</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-primary-red mr-2"></div>
                <span className="text-xs text-secondary/60">
                  Needs Improvement
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {pagespeedData?.performance_desktop?.[
              "First Contentful Paint (FCP)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="First Contentful Paint"
                time={
                  pagespeedData?.performance_desktop?.[
                    "First Contentful Paint (FCP)"
                  ]
                }
                max={10}
                green_time={1.8} // Good: <= 1.8s
                warning_time={5.0} // Needs improvement: <= 3.0s
                red_time={6.0} // Poor: > 3.0s
              />
            )}

            {pagespeedData?.performance_desktop?.[
              "Largest Contentful Paint (LCP)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Largest Contentful Paint"
                time={
                  pagespeedData?.performance_desktop?.[
                    "Largest Contentful Paint (LCP)"
                  ]
                }
                max={10}
                green_time={2.5} // Good: <= 2.5s
                warning_time={5.0} // Needs improvement: <= 4.0s
                red_time={6.0} // Poor: > 4.0s
              />
            )}

            {pagespeedData?.performance_desktop?.[
              "Time to Interactive (TTI)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Time to Interactive"
                time={
                  pagespeedData?.performance_desktop?.[
                    "Time to Interactive (TTI)"
                  ]
                }
                max={7.5}
                green_time={3.8} // Good: <= 3.8s
                warning_time={7.3} // Needs improvement: <= 7.3s
                red_time={7.3} // Poor: > 7.3s
              />
            )}
          </div>

          {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {pagespeedData.performance_desktop["Speed Index (SI)"] !==
              undefined && (
              <PerformanceGaugeChart
                title="Speed Index"
                time={pagespeedData.performance_desktop["Speed Index (SI)"]}
                max={10}
                green_time={3.4} // Good: <= 3.4s
                warning_time={5.8} // Needs improvement: <= 5.8s
                red_time={5.8} // Poor: > 5.8s
              />
            )}

            {pagespeedData.performance_desktop["Total Blocking Time (TBT)"] !==
              undefined && (
              <PerformanceGaugeChart
                title="Total Blocking Time"
                time={
                  pagespeedData.performance_desktop["Total Blocking Time (TBT)"]
                }
                max={1000}
                green_time={200} // Good: <= 200ms
                warning_time={600} // Needs improvement: <= 600ms
                red_time={600} // Poor: > 600ms
              />
            )}

            {pagespeedData.performance_desktop[
              "Cumulative Layout Shift (CLS)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Cumulative Layout Shift"
                time={
                  pagespeedData.performance_desktop[
                    "Cumulative Layout Shift (CLS)"
                  ]
                }
                max={1}
                green_time={0.1} // Good: <= 0.1
                warning_time={0.25} // Needs improvement: <= 0.25
                red_time={0.25} // Poor: > 0.25
              />
            )}
          </div> */}
        </div>
      )}

      {/* PageSpeed Mobile Section */}
      {pagespeedMobileData && pagespeedMobileData.performance_mobile && (
        <div className="my-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <h4 className="font-semibold text-secondary">
              PageSpeed Mobile Metrics
            </h4>
            <div className="flex items-center gap-4 mt-2 md:mt-0">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-primary-green mr-2"></div>
                <span className="text-xs text-secondary/60">Good</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-primary-red mr-2"></div>
                <span className="text-xs text-secondary/60">
                  Needs Improvement
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {pagespeedMobileData?.performance_mobile?.[
              "First Contentful Paint (FCP)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="First Contentful Paint"
                time={
                  pagespeedMobileData?.performance_mobile?.[
                    "First Contentful Paint (FCP)"
                  ]
                }
                max={10}
                green_time={1.8} // Good: <= 1.8s
                warning_time={5.0} // Needs improvement: <= 3.0s
                red_time={6.0} // Poor: > 3.0s
              />
            )}

            {pagespeedMobileData?.performance_mobile?.[
              "Largest Contentful Paint (LCP)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Largest Contentful Paint"
                time={
                  pagespeedMobileData?.performance_mobile?.[
                    "Largest Contentful Paint (LCP)"
                  ]
                }
                max={10}
                green_time={2.5} // Good: <= 2.5s
                warning_time={5.0} // Needs improvement: <= 4.0s
                red_time={8.0} // Poor: > 4.0s
              />
            )}

            {pagespeedMobileData?.performance_mobile?.[
              "Time to Interactive (TTI)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Time to Interactive"
                time={
                  pagespeedMobileData?.performance_mobile?.[
                    "Time to Interactive (TTI)"
                  ]
                }
                max={7.5}
                green_time={3.8} // Good: <= 3.8s
                warning_time={6.3} // Needs improvement: <= 7.3s
                red_time={11.3} // Poor: > 7.3s
              />
            )}
          </div>

          {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {pagespeedMobileData.performance_mobile["Speed Index (SI)"] !==
              undefined && (
              <PerformanceGaugeChart
                title="Speed Index"
                time={
                  pagespeedMobileData.performance_mobile["Speed Index (SI)"]
                }
                max={10}
                green_time={3.4} // Good: <= 3.4s
                warning_time={5.8} // Needs improvement: <= 5.8s
                red_time={5.8} // Poor: > 5.8s
              />
            )}

            {pagespeedMobileData.performance_mobile[
              "Total Blocking Time (TBT)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Total Blocking Time"
                time={
                  pagespeedMobileData.performance_mobile[
                    "Total Blocking Time (TBT)"
                  ]
                }
                max={1000}
                green_time={200} // Good: <= 200ms
                warning_time={600} // Needs improvement: <= 600ms
                red_time={600} // Poor: > 600ms
              />
            )}

            {pagespeedMobileData.performance_mobile[
              "Cumulative Layout Shift (CLS)"
            ] !== undefined && (
              <PerformanceGaugeChart
                title="Cumulative Layout Shift"
                time={
                  pagespeedMobileData.performance_mobile[
                    "Cumulative Layout Shift (CLS)"
                  ]
                }
                max={1}
                green_time={0.1} // Good: <= 0.1
                warning_time={0.25} // Needs improvement: <= 0.25
                red_time={0.25} // Poor: > 0.25
              />
            )}
          </div> */}
        </div>
      )}

      <div className="flex flex-col gap-8 mt-4 lg:mt-6">
        {/* JavaScript Errors Section */}
        {results.javascript_errors && (
          <ShowMoreSection
            title="JavaScript Errors"
            passed={results.javascript_errors.pass}
            description={results.javascript_errors.description}
            icon={
              results.javascript_errors.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.javascript_errors.importance}
            recommendation={results.javascript_errors.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-2">
                <strong>Error Count:</strong>{" "}
                {results.javascript_errors?.error_count || 0}
              </p>
              {results.javascript_errors?.errors &&
                results.javascript_errors.errors.length > 0 && (
                  <div>
                    <p className="text-sm font-semibold text-secondary mb-1">
                      Errors:
                    </p>
                    <ul className="list-disc pl-5 text-sm text-secondary/60">
                      {results.javascript_errors.errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Deprecated HTML Section */}
        {results.deprecated_html && (
          <ShowMoreSection
            title="Deprecated HTML"
            passed={results.deprecated_html.pass}
            description={results.deprecated_html.description}
            icon={
              results.deprecated_html.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.deprecated_html.importance}
            recommendation={results.deprecated_html.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/70 mb-2">
                <strong>Deprecated Elements Count:</strong>{" "}
                {results.deprecated_html?.count || 0}
              </p>

              {results.deprecated_html?.elements &&
                results.deprecated_html.elements.length > 0 && (
                  <div className="mt-4">
                    <p className="text-sm font-semibold text-secondary/80 mb-2">
                      Deprecated Elements Found:
                    </p>
                    <div className="space-y-3">
                      {results.deprecated_html.elements.map(
                        (element, index) => (
                          <div
                            key={index}
                            className="p-2 py-4 rounded-md border border-light-gray"
                          >
                            <div className="flex items-center mb-1">
                              <span className="inline-block px-2 py-1 bg-primary-red/10 text-primary-red text-xs font-bold rounded mr-2">
                                {element.tag}
                              </span>
                              <span className="text-sm text-secondary/85">
                                Deprecated HTML Tag
                              </span>
                            </div>

                            <div className="mt-2">
                              <p className="text-xs text-secondary/70 mb-1">
                                <strong>Content:</strong> {element.content}
                              </p>
                              <div className="mt-1">
                                <p className="text-xs text-secondary/70 mb-1">
                                  <strong>HTML:</strong>
                                </p>
                                <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                  {element.html}
                                </pre>
                              </div>
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Compression Section */}
        {results.compression && (
          <ShowMoreSection
            title="Compression"
            passed={results.compression.pass}
            description={results.compression.description}
            icon={
              results.compression.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.compression.importance}
            recommendation={results.compression.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Compression Type:</strong>{" "}
                {results.compression?.compression_type || "Unknown"}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Original Size:</strong>{" "}
                {results.compression?.size_mb !== undefined
                  ? results.compression.size_mb.toFixed(2)
                  : "0"}{" "}
                MB
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Compressed Size:</strong>{" "}
                {results.compression?.compressed_size_mb !== undefined
                  ? results.compression.compressed_size_mb.toFixed(2)
                  : "0"}{" "}
                MB
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Compression Ratio:</strong>{" "}
                {results.compression?.compression_ratio !== undefined
                  ? results.compression.compression_ratio.toFixed(1)
                  : "0"}
                %
              </p>
            </div>
          </ShowMoreSection>
        )}

        {/* Resource Count Section - Now displayed in a custom section above */}

        {/* AMP Section */}
        {results.amp && (
          <ShowMoreSection
            title="AMP (Accelerated Mobile Pages)"
            passed={results.amp.pass}
            description={results.amp.description}
            icon={
              results.amp.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.amp.importance}
            recommendation={results.amp.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Is AMP Page:</strong>{" "}
                {results.amp?.is_amp_page ? "Yes" : "No"}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Has AMP Link:</strong>{" "}
                {results.amp?.has_amp_link ? "Yes" : "No"}
              </p>
              {results.amp?.amp_url && (
                <p className="text-sm text-secondary/60 mb-1">
                  <strong>AMP URL:</strong> {results.amp?.amp_url}
                </p>
              )}
            </div>
          </ShowMoreSection>
        )}

        {/* Page Size Section */}
        {results.page_size && (
          <ShowMoreSection
            title="Page Size"
            passed={results.page_size.pass}
            description={results.page_size.description}
            icon={
              results.page_size.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.page_size.importance}
            recommendation={results.page_size.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Total Size:</strong>{" "}
                {results.page_size?.total_estimated_size_mb !== undefined
                  ? results.page_size.total_estimated_size_mb.toFixed(2)
                  : "0"}{" "}
                MB ({results.page_size?.size_category || "Unknown"})
              </p>

              {/* Display CompressionPieChart and CompressionRates side by side */}
              <div className="mt-4 mb-4 flex flex-col lg:flex-row gap-6">
                <div className="lg:w-1/2 flex mt-3">
                  <CompressionRates
                    pageSizeData={results.page_size}
                    className="w-full"
                  />
                </div>
                <div className="lg:w-1/2 flex justify-center">
                  <CompressionPieChart
                    pageSizeData={results.page_size}
                    className="max-w-md"
                  />
                </div>
              </div>
            </div>
          </ShowMoreSection>
        )}

        {/* Inline Styles Section */}
        {results.inline_styles && (
          <ShowMoreSection
            title="Inline Styles"
            passed={results.inline_styles.pass}
            description={results.inline_styles.description}
            icon={
              results.inline_styles.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.inline_styles.importance}
            recommendation={results.inline_styles.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Elements with Inline Styles:</strong>{" "}
                {results.inline_styles?.elements_with_style || 0} of{" "}
                {results.inline_styles?.total_elements || 0} (
                {results.inline_styles?.inline_style_percentage !== undefined
                  ? results.inline_styles.inline_style_percentage.toFixed(1)
                  : "0"}
                %)
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Total Style Size:</strong>{" "}
                {results.inline_styles?.total_style_size_kb !== undefined
                  ? results.inline_styles.total_style_size_kb.toFixed(1)
                  : "0"}{" "}
                KB
              </p>

              {results.inline_styles?.most_common_properties &&
                Object.keys(results.inline_styles.most_common_properties)
                  .length > 0 && (
                  <div>
                    <p className="text-sm font-semibold text-secondary mt-2 mb-1">
                      Most Common Properties:
                    </p>
                    <ul className="list-disc pl-5 text-sm text-secondary/60">
                      {Object.entries(
                        results.inline_styles.most_common_properties
                      )
                        .slice(0, 5)
                        .map(([property, count], index) => (
                          <li key={index}>
                            {property}: {count} occurrences
                          </li>
                        ))}
                    </ul>
                  </div>
                )}

              {results.inline_styles?.style_examples &&
                results.inline_styles.style_examples.length > 0 && (
                  <div>
                    <p className="text-sm font-semibold text-secondary mt-2 mb-1">
                      Examples:
                    </p>
                    <ul className="list-disc pl-5 text-sm text-secondary/60">
                      {results.inline_styles.style_examples
                        .slice(0, 3)
                        .map((example, index) => (
                          <li key={index}>
                            <code>{example.tag}</code>:{" "}
                            <code>{example.style}</code>
                          </li>
                        ))}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Performance Timing Section */}
        {results.performance_timing && (
          <ShowMoreSection
            title="Performance Timing"
            passed={results.performance_timing.pass}
            description={results.performance_timing.description}
            icon={
              results.performance_timing.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.performance_timing.importance}
            recommendation={results.performance_timing.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Time to First Byte (TTFB):</strong>{" "}
                {results.performance_timing?.time_to_first_byte_s !== undefined
                  ? results.performance_timing.time_to_first_byte_s.toFixed(2)
                  : "0"}
                s
              </p>
              <div className="mt-4 flex justify-center">
                <PerformanceGaugeChart
                  title="Time to First Byte"
                  time={results.performance_timing?.time_to_first_byte_s}
                  max={10}
                  green_time={0.8} // Good: <= 0.8s
                  warning_time={1.8} // Needs improvement: <= 1.8s
                  red_time={1.8} // Poor: > 1.8s
                />
              </div>
            </div>
          </ShowMoreSection>
        )}

        {/* HTTP Protocol Section */}
        {results.http_protocol && (
          <ShowMoreSection
            title="HTTP Protocol"
            passed={results.http_protocol.pass}
            description={results.http_protocol.description}
            icon={
              results.http_protocol.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.http_protocol.importance}
            recommendation={results.http_protocol.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Protocol Used:</strong>{" "}
                {results.http_protocol?.protocol_used || "Unknown"}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Supports HTTP/2:</strong>{" "}
                {results.http_protocol?.supports_http2 ? "Yes" : "No"}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Supports HTTP/3:</strong>{" "}
                {results.http_protocol?.supports_http3 ? "Yes" : "No"}
              </p>
              {results.http_protocol?.alt_svc_header && (
                <p className="text-sm text-secondary/60 mb-1">
                  <strong>Alt-Svc Header:</strong>{" "}
                  {results.http_protocol?.alt_svc_header}
                </p>
              )}
            </div>
          </ShowMoreSection>
        )}

        {/* Image Optimization Section */}
        {results.image_optimization && (
          <ShowMoreSection
            title="Image Optimisation"
            passed={results.image_optimization.pass}
            description={results.image_optimization.description}
            icon={
              results.image_optimization.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.image_optimization.importance}
            recommendation={results.image_optimization.recommendation}
          >
            <div>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Total Images:</strong>{" "}
                {results.image_optimization?.total_images || 0}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Next-Gen Format Images:</strong>{" "}
                {results.image_optimization?.next_gen_images_count || 0}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Images Missing Alt Text:</strong>{" "}
                {results.image_optimization?.missing_alt_count || 0}
              </p>
              <p className="text-sm text-secondary/60 mb-1">
                <strong>Images Missing Dimensions:</strong>{" "}
                {results.image_optimization?.missing_dimensions_count || 0}
              </p>

              {results.image_optimization?.problematic_images_sample &&
                results.image_optimization.problematic_images_sample.length >
                  0 && (
                  <div>
                    <p className="text-sm font-semibold text-secondary mt-2 mb-1">
                      Problematic Images:
                    </p>
                    <ul className="list-disc pl-5 text-sm text-secondary/60">
                      {results.image_optimization.problematic_images_sample.map(
                        (image, index) => (
                          <li key={index}>
                            {image.src
                              ? image.src.substring(0, 50) +
                                (image.src.length > 50 ? "..." : "")
                              : "Unknown image"}
                            {image.alt_missing && " (Missing alt text)"}
                            {image.dims_missing && " (Missing dimensions)"}
                            {image.not_next_gen && " (Not next-gen format)"}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Minification Section */}
        {results.minification && (
          <ShowMoreSection
            title="Minification"
            passed={results.minification.pass}
            description={results.minification.description}
            icon={
              results.minification.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.minification.importance}
            recommendation={results.minification.recommendation}
          >
            <div>
              <div>
                <p className="text-sm font-semibold text-secondary mb-1">
                  JavaScript:
                </p>
                <p className="text-sm text-secondary/60 mb-1">
                  <strong>Total JS Files:</strong>{" "}
                  {results.minification?.total_js || 0}
                </p>
                <p className="text-sm text-secondary/60 mb-1">
                  <strong>Unminified JS Files:</strong>{" "}
                  {results.minification?.unminified_js_count || 0}
                </p>

                {results.minification?.unminified_js_samples &&
                  results.minification.unminified_js_samples.length > 0 && (
                    <div>
                      <p className="text-sm font-semibold text-secondary mt-2 mb-1">
                        Unminified JS Examples:
                      </p>
                      <ul className="list-disc pl-5 text-sm text-secondary/60">
                        {results.minification.unminified_js_samples
                          .slice(0, 3)
                          .map((file, index) => (
                            <li key={index}>{file}</li>
                          ))}
                      </ul>
                    </div>
                  )}
              </div>

              <div className="mt-3">
                <p className="text-sm font-semibold text-secondary mb-1">
                  CSS:
                </p>
                <p className="text-sm text-secondary/60 mb-1">
                  <strong>Total CSS Files:</strong>{" "}
                  {results.minification?.total_css || 0}
                </p>
                <p className="text-sm text-secondary/60 mb-1">
                  <strong>Unminified CSS Files:</strong>{" "}
                  {results.minification?.unminified_css_count || 0}
                </p>

                {results.minification?.unminified_css_samples &&
                  results.minification.unminified_css_samples.length > 0 && (
                    <div>
                      <p className="text-sm font-semibold text-secondary mt-2 mb-1">
                        Unminified CSS Examples:
                      </p>
                      <ul className="list-disc pl-5 text-sm text-secondary/60">
                        {results.minification.unminified_css_samples
                          .slice(0, 3)
                          .map((file, index) => (
                            <li key={index}>{file}</li>
                          ))}
                      </ul>
                    </div>
                  )}
              </div>
            </div>
          </ShowMoreSection>
        )}
      </div>
      {/* Number of Resources Section */}
      {results.resource_count && (
        <div className="my-8">
          <ResourcesCount
            html={results.resource_count?.html || 0}
            css={results.resource_count?.css || 0}
            js={results.resource_count?.js || 0}
            images={results.resource_count?.images || 0}
            other={
              (results.resource_count?.other || 0) +
              (results.resource_count?.fonts || 0) +
              (results.resource_count?.iframes || 0)
            }
            description={results.resource_count?.description || ""}
          />
        </div>
      )}
    </BoxPrimary>
  );
}
