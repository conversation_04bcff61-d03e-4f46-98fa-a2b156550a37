import { motion } from "framer-motion";
import {
  CrownIcon,
  FlagIcon,
} from "@/ui/icons/general";
import StateCard from "../../StateCard";
import OverallSection from "../OverallSection";
import ShowMoreSection from "../usability/ShowMoreSection";
import { LinksAnalysisData } from "./types";
import {
  formatMetricValue,
  formatMetricValueWithPrefix,
  getMetricInfo,
  getActiveData,
} from "./utils";

// Shield Check Icon for Page Authority
const ShieldCheckIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="28"
    height="28"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" />
    <path d="m9 12 2 2 4-4" />
  </svg>
);

type DomainInsightTabProps = {
  data: LinksAnalysisData;
  itemVariants: any;
};

export default function DomainInsightTab({
  data,
  itemVariants,
}: DomainInsightTabProps) {
  const domainInsight = data.domain_insight;
  const activeData = getActiveData("domain_insight", data);

  if (!domainInsight) {
    return (
      <div className="bg-white rounded-lg border border-light-gray p-6">
        <div className="text-center py-8">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-700 mb-2">
            Domain Insight Data Unavailable
          </h3>
          <p className="text-red-600">
            Something went wrong while getting domain insight data. Please try
            again later.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <motion.div variants={itemVariants}>
        <OverallSection
          title={activeData.title}
          description={activeData.description}
        />
      </motion.div>

      <motion.div
        variants={itemVariants}
        className="w-full grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4"
      >
        <StateCard
          icon={<CrownIcon className="w-6 h-6" />}
          value={formatMetricValue(
            domainInsight,
            "domain_authority",
            0,
            "score"
          )}
          label="Domain Authority"
        />
        <StateCard
          icon={<ShieldCheckIcon className="w-6 h-6" />}
          value={formatMetricValue(domainInsight, "page_authority", 0, "score")}
          label="Page Authority"
        />
        <StateCard
          icon={<FlagIcon className="w-6 h-6" />}
          value={formatMetricValue(domainInsight, "spam_score", 0, "score")}
          label="Spam Score"
        />
      </motion.div>

      {/* Detailed sections */}
      <div className="space-y-6 mt-6">
        {/* Domain Authority Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Domain Authority"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    domainInsight,
                    "domain_authority",
                    0,
                    "score"
                  )}
                </div>
                {getMetricInfo(domainInsight, "domain_authority")?.description ||
                  "Domain Authority is a score predicting a website's ranking strength based on its overall backlink profile."}
              </div>
            }
            importance={
              getMetricInfo(domainInsight, "domain_authority")?.importance
            }
            recommendation={
              getMetricInfo(domainInsight, "domain_authority")?.recommendation ||
              undefined
            }
            icon={<CrownIcon className="w-12 h-12 text-purple-600" />}
          />
        </div>

        {/* Page Authority Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Page Authority"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    domainInsight,
                    "page_authority",
                    0,
                    "score"
                  )}
                </div>
                {getMetricInfo(domainInsight, "page_authority")?.description ||
                  "Page Authority predicts the ranking strength of a specific page."}
              </div>
            }
            importance={
              getMetricInfo(domainInsight, "page_authority")?.importance
            }
            recommendation={
              getMetricInfo(domainInsight, "page_authority")?.recommendation ||
              undefined
            }
            icon={<ShieldCheckIcon className="w-12 h-12 text-blue-600" />}
          />
        </div>

        {/* Spam Score Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Spam Score"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    domainInsight,
                    "spam_score",
                    0,
                    "percentage"
                  )}
                </div>
                {getMetricInfo(domainInsight, "spam_score")?.description ||
                  "Spam Score represents the percentage of sites with similar features to yours that have been found to be penalized or banned by Google."}
              </div>
            }
            importance={getMetricInfo(domainInsight, "spam_score")?.importance}
            recommendation={
              getMetricInfo(domainInsight, "spam_score")?.recommendation ||
              undefined
            }
            icon={<FlagIcon className="w-12 h-12 text-red-600" />}
          />
        </div>
      </div>
    </>
  );
}
