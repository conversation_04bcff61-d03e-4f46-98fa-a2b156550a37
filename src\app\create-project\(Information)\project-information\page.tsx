"use client";
import BoxCreateProject from "@/components/CreateProject/BoxCreateProject";
import InfoCard from "@/components/CreateProject/InfoCard";
import NavbarCreateProject from "@/components/CreateProject/NavbarCreateProject";
import SelectDomainType from "@/components/CreateProject/SelectDomainType";
import StepMobileCreateProject from "@/components/CreateProject/StepMobileCreateProject";
import TitleCreateProject from "@/components/CreateProject/TitleCreateProject";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import ColorPickerBox from "@/components/shared/ColorPickerBox";
import CustomInput from "@/components/shared/CustomInput";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { useCreateProjectStore } from "@/store/createProjectStore";
import PageTransition, {
  AnimatedElement,
} from "@/components/CreateProject/PageTransition";
import {
  projectAPI,
  CreateProjectRequest,
  UpdateProjectRequest,
} from "@/services/projectService";
import createProjectToast from "@/lib/createProjectToast";

const schema = yup.object({
  domain: yup
    .string()
    .required("")
    .matches(
      /^(?:(?:https?:\/\/)?(?:www\.)?)?((([a-zA-Z0-9\u0600-\u06FF\-]+\.)+[a-zA-Z]{2,}|localhost|\d{1,3}(\.\d{1,3}){3})(:\d+)?)(\/.*)?$/,
      "Enter a valid domain like example.com or https://example.com"
    )
    .test("valid-domain", "Enter a valid domain", function (value) {
      if (!value) return false;

      // Remove protocol and www for validation
      const cleanDomain = value
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "")
        .split("/")[0]; // Remove path

      // Basic domain validation
      const domainRegex =
        /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.([a-zA-Z]{2,}|[a-zA-Z0-9-]{2,}\.[a-zA-Z]{2,})$/;
      return domainRegex.test(cleanDomain) || cleanDomain === "localhost";
    }),
  name: yup
    .string()
    .required("Name is required")
    .max(50, "Project name must be at most 50 characters"),
});

export default function Page() {
  // Store hooks
  const {
    projectInfo,
    setProjectInfo,
    setCurrentStep,
    setLoading,
    markStepComplete,
    hasDataChanged,
    markAsSaved,
  } = useCreateProjectStore();

  const [isNameTouched, setIsNameTouched] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [selectedColor, setSelectedColor] = useState<string>(
    projectInfo?.color || "#8C00FF"
  );

  const sanitizeInput = (input: string) => input.replace(/[<>/"'`;()]/g, "");

  // Function to ensure URL has proper protocol
  const formatUrl = (url: string) => {
    const cleanUrl = sanitizeInput(url).trim();
    if (!cleanUrl) return cleanUrl;

    // If URL already has protocol, return as is
    if (cleanUrl.startsWith("http://") || cleanUrl.startsWith("https://")) {
      return cleanUrl;
    }

    // Add https:// prefix
    return `https://${cleanUrl}`;
  };

  // Function to extract clean domain for display (without protocol)
  const getCleanDomainForDisplay = (url: string) => {
    if (!url) return "";
    return url
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .split("/")[0]; // Remove path if any
  };

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    getValues,
    reset,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "", // Always start with empty values - will be populated in useEffect for edit mode
      domain: "",
    },
  });
  const [lastSubmitTime, setLastSubmitTime] = useState<number | null>(null);
  const route = useRouter();
  const {
    isEditMode,
    isProjectDataReady,
    isLoadingProject,
    projectInfo: editProjectInfo,
    isInCreateMode,
    isInEditMode,
  } = useEditProject();
  const { urls } = useEditNavigation();

  const domainValue = watch("domain");

  const { mutate, isPending } = useMutation({
    mutationFn: async (): Promise<any> => {
      const now = Date.now();
      if (lastSubmitTime && now - lastSubmitTime < 3000) {
        return new Promise((res) => setTimeout(() => res(true), 500));
      }

      setLastSubmitTime(now);
      setLoading(true);
      // Remove setError(null) as we're using toast notifications

      const domain = getValues("domain");
      const name = getValues("name");

      // Prepare API request data
      const formattedUrl = formatUrl(domain);
      const cleanDomain = formattedUrl
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "");

      // Check if project already exists (has an ID)
      const existingProjectId = projectInfo?.id;
      const isUpdating = !!existingProjectId;

      // Check if data has actually changed before making API call
      if (isUpdating && !hasDataChanged()) {
        // No changes detected, just proceed to next step without API call
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({ data: { id: existingProjectId } });
          }, 100);
        });
      }

      if (isUpdating) {
        // Update existing project using PUT request
        const updateData: UpdateProjectRequest = {
          url: formattedUrl,
          domain_type: `*.${cleanDomain}`,
          project_name: sanitizeInput(name),
          project_color: selectedColor,
          status: "enabled",
          primary_search_engines: [],
          keywords: [],
          competitors: [],
        };

        return await createProjectToast.apiCall(
          projectAPI.updateProject(existingProjectId, updateData),
          {
            loadingMessage: "Updating your project...",
            successMessage: `Project "${sanitizeInput(
              name
            )}" updated successfully!`,
            errorContext: "update project",
            onSuccess: (response) => {
              // Update the project data in store
              const updatedProjectInfo = {
                name: sanitizeInput(name),
                domain: formattedUrl,
                color: selectedColor,
                id: response.data.id,
              };
              setProjectInfo(updatedProjectInfo);
              markAsSaved(); // Mark as saved after successful update
            },
          }
        );
      } else {
        // Create new project using POST request
        const projectData: CreateProjectRequest = {
          url: formattedUrl,
          domain_type: `*.${cleanDomain}`,
          project_name: sanitizeInput(name),
          project_color: selectedColor,
        };

        return await createProjectToast.apiCall(
          projectAPI.createProject(projectData),
          {
            loadingMessage: "Creating your project...",
            successMessage: `Project "${sanitizeInput(
              name
            )}" created successfully!`,
            errorContext: "create project",
            onSuccess: (response) => {
              // Store the project data with the returned ID
              const projectInfo = {
                name: sanitizeInput(name),
                domain: formattedUrl, // Store the properly formatted URL
                color: selectedColor,
                id: response.data.id, // Store the project ID from API response
              };
              setProjectInfo(projectInfo);
              markAsSaved(); // Mark as saved after successful creation
            },
          }
        );
      }
    },
    onSuccess: () => {
      // Mark project information step as complete and update current step
      markStepComplete("projectInformation");
      setCurrentStep("search-engines");

      // Use replace instead of push to avoid back button issues
      // and ensure clean navigation state
      route.replace(urls.searchEngines);
    },
    onError: () => {
      // Error handling is now done by createProjectToast.apiCall
    },
    onSettled: () => {
      setLoading(false);
    },
  });

  const handleFormSubmit = () => {
    // Set that user has attempted to submit
    setHasAttemptedSubmit(true);
    // The handleSubmit will validate and only call mutate if validation passes
    handleSubmit(() => mutate())();
  };

  // Set current step on mount
  useEffect(() => {
    setCurrentStep("project-information");
  }, [setCurrentStep]);

  // Update form when project info changes (ONLY for edit mode) - optimized with debouncing
  useEffect(() => {
    // Only populate form in edit mode with valid project data
    if (!isInEditMode || !isProjectDataReady) {
      return;
    }

    // Use requestAnimationFrame to defer form updates and prevent blocking animations
    const updateForm = () => {
      if (editProjectInfo?.name && editProjectInfo?.domain) {
        // Use reset to properly update all form state
        reset({
          name: editProjectInfo.name,
          domain: getCleanDomainForDisplay(editProjectInfo.domain),
        });

        setSelectedColor(editProjectInfo.color || "#8C00FF");

        // Mark that we've populated the form to prevent auto-generation from domain
        setIsNameTouched(true);
      }
    };

    // Defer the update to the next frame to avoid blocking animations
    const frameId = requestAnimationFrame(updateForm);
    return () => cancelAnimationFrame(frameId);
  }, [editProjectInfo, reset, isInEditMode, isProjectDataReady]);

  // Reset form when entering create mode to ensure clean state
  useEffect(() => {
    if (isInCreateMode) {
      // Clear form and reset to initial state
      reset({
        name: "",
        domain: "",
      });
      setSelectedColor("#8C00FF");
      setIsNameTouched(false);
    }
  }, [isInCreateMode, reset]);
  const splitDomain = (domain: string) => {
    // Remove protocol and www prefix before processing
    const cleanDomain = domain
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .split("/")[0]; // Remove path if any

    const parts = cleanDomain.split(".");
    if (parts.length < 2) return cleanDomain;
    const name = parts.slice(0, -1).join(".");
    return name;
  };

  const handleColorChange = (color: string) => {
    setSelectedColor(color);
  };

  // Optimized domain auto-completion with debouncing
  useEffect(() => {
    if (!isNameTouched) {
      // Use requestIdleCallback for non-critical updates to avoid blocking animations
      const updateName = () => {
        if (domainValue) {
          setValue("name", splitDomain(domainValue) || "", {
            shouldValidate: true,
            shouldDirty: true,
          });
        } else {
          setValue("name", "");
        }
      };

      // Use requestIdleCallback if available, otherwise fallback to setTimeout
      if (typeof window !== "undefined" && "requestIdleCallback" in window) {
        const idleId = window.requestIdleCallback(updateName);
        return () => window.cancelIdleCallback(idleId);
      } else {
        const timeoutId = setTimeout(updateName, 0);
        return () => clearTimeout(timeoutId);
      }
    }
  }, [domainValue, isNameTouched, setValue]);

  // Show loading state while fetching project data in edit mode
  if (isEditMode && isLoadingProject) {
    return (
      <PageTransition>
        <div className="flex justify-between flex-col gap-6 lg:gap-8 h-full min-h-[calc(100vh-2rem)]">
          <div className="flex flex-col gap-3">
            <div className="space-y-3">
              <div className="h-8 bg-gray-200 rounded animate-pulse w-1/2"></div>
              <div className="h-4 bg-gray-100 rounded animate-pulse w-3/4"></div>
            </div>
            <div className="space-y-6 mt-6">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
                <div className="h-12 bg-gray-100 rounded-lg animate-pulse"></div>
              </div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
                <div className="h-12 bg-gray-100 rounded-lg animate-pulse"></div>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-2 text-gray-600">
              <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">Loading project data...</span>
            </div>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="flex justify-between flex-col gap-6 lg:gap-8 h-full min-h-[calc(100vh-2rem)]">
        <div className="flex flex-col gap-3">
          <AnimatedElement variant="child">
            <NavbarCreateProject>Project information</NavbarCreateProject>
          </AnimatedElement>
          <AnimatedElement variant="child" delay={0.1}>
            <StepMobileCreateProject />
          </AnimatedElement>

          {/* Main Content Card */}
          <AnimatedElement variant="card" delay={0.2}>
            <BoxCreateProject classPlus="relative">
              {/* Header Section */}
              <AnimatedElement variant="child" delay={0.3}>
                <div className="mb-4">
                  <TitleCreateProject
                    head="Set up your website"
                    description="Configure your project settings to start tracking your website performance across all major search engines. Customize parameters according to your specific requirements."
                    classHead="text-lg lg:text-xl font-semibold text-[#344054]"
                    classP="text-[#344054] mt-3 leading-relaxed"
                  />
                </div>
              </AnimatedElement>

              {/* Error handling is now done via toast notifications */}

              {/* Form Section */}
              <AnimatedElement variant="form" delay={0.4}>
                <form className="space-y-8">
                  {/* Website Configuration */}
                  <AnimatedElement variant="child" delay={0.5}>
                    <div className="space-y-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <span className="text-primary font-semibold text-sm">
                            1
                          </span>
                        </div>
                        <h3 className="text-base font-semibold text-[#344054]">
                          Website Configuration
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <AnimatedElement variant="form" delay={0.6}>
                          <div className="space-y-2">
                            <CustomInput
                              required
                              error={
                                hasAttemptedSubmit
                                  ? errors.domain?.message
                                  : undefined
                              }
                              {...register("domain")}
                              label="Website URL"
                              guideText="Enter your website address (e.g., mycompany.com or https://mycompany.com)"
                              placeholder="mycompany.com"
                            />
                          </div>
                        </AnimatedElement>
                        <AnimatedElement variant="form" delay={0.7}>
                          <div className="space-y-2">
                            <SelectDomainType />
                          </div>
                        </AnimatedElement>
                      </div>
                    </div>
                  </AnimatedElement>

                  {/* Project Settings */}
                  <AnimatedElement variant="child" delay={0.8}>
                    <div className="space-y-6 pt-6 border-t border-gray-100">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <span className="text-primary font-semibold text-sm">
                            2
                          </span>
                        </div>
                        <h3 className="text-base font-semibold text-[#344054]">
                          Project Settings
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <AnimatedElement variant="form" delay={0.9}>
                          <div className="space-y-2">
                            <CustomInput
                              required
                              error={errors.name?.message}
                              onFocus={() => setIsNameTouched(true)}
                              {...register("name")}
                              label="Project name"
                              guideText="Choose a descriptive name for easy identification in your dashboard"
                              placeholder="My Website Project"
                            />
                          </div>
                        </AnimatedElement>
                        <AnimatedElement variant="form" delay={1.0}>
                          <div className="space-y-2">
                            <ColorPickerBox
                              title="Project Color"
                              guideText="Select a color to help identify your project in the dashboard"
                              value={selectedColor}
                              onChange={handleColorChange}
                            />
                          </div>
                        </AnimatedElement>
                      </div>
                    </div>
                  </AnimatedElement>

                  {/* Info Card */}
                  <AnimatedElement variant="card" delay={1.1}>
                    <InfoCard
                      variant="info"
                      title="Getting Started"
                      description="Once you complete this setup, you'll be able to track keyword rankings, monitor competitors, and analyze your website's SEO performance across multiple search engines."
                      className="mt-6"
                    />
                  </AnimatedElement>
                </form>
              </AnimatedElement>
            </BoxCreateProject>
          </AnimatedElement>
        </div>

        {/* Action Buttons */}
        <AnimatedElement variant="card" delay={1.2}>
          <div className="flex flex-col sm:flex-row gap-3 justify-end bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
            <ButtenSubmit
              isLoading={isPending}
              textloading={projectInfo?.id ? "Updating..." : "Setting up..."}
              onClick={handleFormSubmit}
              text={
                projectInfo?.id
                  ? "Update & Continue"
                  : "Continue to Search Engines"
              }
              classPluss="sm:w-auto w-full"
            />
          </div>
        </AnimatedElement>
      </div>
    </PageTransition>
  );
}
