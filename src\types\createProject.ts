/**
 * Type definitions for the create-project flow
 * Provides comprehensive type safety for all create-project related functionality
 */

// Step identifiers for the create-project flow
export type CreateProjectStep = 
  | "project-information"
  | "search-engines" 
  | "keywords"
  | "competitors"
  | "analytics-services";

// URL paths for create-project steps
export type CreateProjectStepUrl = 
  | "/create-project/project-information"
  | "/create-project/search-engines"
  | "/create-project/keywords" 
  | "/create-project/competitors"
  | "/create-project/analytics-services";

// Mode types for create-project flow
export type CreateProjectMode = "create" | "edit" | "shortcut";

// Step completion status
export interface StepCompletion {
  projectInformation: boolean;
  searchEngines: boolean;
  keywords: boolean;
  competitors: boolean;
  analyticsServices: boolean;
}

// Project information structure
export interface ProjectInfo {
  id?: string;
  name: string;
  domain: string;
  color?: string;
  description?: string;
}

// Search engine configuration
export interface SearchEngineConfig {
  id: string;
  searchEngine: string;
  country: string;
  language: string;
  location?: string;
}

// Keyword structure
export interface Keyword {
  id: string;
  keyword: string;
  searchEngineId: string;
  volume?: number;
  difficulty?: number;
  cpc?: number;
}

// Competitor structure  
export interface Competitor {
  id: string;
  domain: string;
  name?: string;
  strength_level?: string;
  organic_keywords?: number;
  organic_traffic?: number;
  organic_cost?: number;
  competitor_score?: number;
}

// Navigation URLs structure
export interface CreateProjectUrls {
  projectInformation: string;
  searchEngines: string;
  keywords: string;
  competitors: string;
  analyticsServices: string;
}

// Hook return types
export interface UseEditProjectReturn {
  // Edit mode state
  isEditMode: boolean;
  editProjectId: string | null;
  
  // Project data state
  existingProject: any;
  isLoadingProject: boolean;
  projectError: any;
  isProjectLoaded: boolean;
  isProjectDataReady: boolean;
  
  // Helper functions
  buildUrl: (path: string) => string;
  
  // Mode detection with validation
  isInCreateMode: boolean;
  isInEditMode: boolean;
  isValidCreateMode: boolean;
  isValidEditMode: boolean;
  
  // Store data
  projectInfo: ProjectInfo | null;
}

export interface UseEditNavigationReturn {
  urls: CreateProjectUrls;
  buildUrl: (path: string) => string;
  isEditMode: boolean;
  editProjectId: string | null;
}

// Store state interface
export interface CreateProjectState {
  // Project data
  projectInfo: ProjectInfo | null;
  searchEngineConfigs: SearchEngineConfig[];
  keywords: Keyword[];
  competitors: Competitor[];
  selectedConfigIds: string[];
  
  // Current step/page
  currentStep: string;
  
  // Step completion tracking
  stepCompletion: StepCompletion;
  
  // API state management
  isLoading: boolean;
  error: string | null;
  
  // Change tracking
  hasUnsavedChanges: boolean;
  originalProjectData: any;
  
  // Mode management
  isEditMode: boolean;
  editProjectId: string | null;
  isCreateFromScratch: boolean;
  lastModeTransition: number;
  
  // Suggestions
  suggestedKeywords: string[];
  competitorSuggestions: Competitor[];
  isLoadingCompetitorSuggestions: boolean;
  competitorSuggestionsError: string | null;
  supportedLocations: any[];
}

// Action types for store
export interface CreateProjectActions {
  // Project info actions
  setProjectInfo: (info: ProjectInfo) => void;
  
  // Search engine config actions
  addSearchEngineConfig: (config: Omit<SearchEngineConfig, "id">) => void;
  removeSearchEngineConfig: (id: string) => void;
  updateSearchEngineConfigs: (configs: SearchEngineConfig[]) => void;
  
  // Keyword actions
  addKeyword: (keyword: Omit<Keyword, "id">) => void;
  removeKeyword: (id: string) => void;
  updateKeywords: (keywords: Keyword[]) => void;
  
  // Competitor actions
  addCompetitor: (competitor: Omit<Competitor, "id">) => void;
  removeCompetitor: (id: string) => void;
  updateCompetitors: (competitors: Competitor[]) => void;
  
  // Step management
  setCurrentStep: (step: string) => void;
  markStepComplete: (step: keyof StepCompletion) => void;
  markStepIncomplete: (step: keyof StepCompletion) => void;
  isStepComplete: (step: keyof StepCompletion) => boolean;
  canAccessStep: (step: string) => boolean;
  
  // API state actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Mode management
  setEditMode: (isEditMode: boolean, projectId?: string) => void;
  clearEditMode: () => void;
  setCreateFromScratch: () => void;
  clearCreateFromScratch: () => void;
  isInCreateMode: () => boolean;
  isInEditMode: () => boolean;
  
  // Utility functions
  resetStore: (preserveCreateMode?: boolean) => void;
  resetAll: () => void;
  loadExistingProject: (projectData: any) => Promise<void>;
}

// Component prop types
export interface SidebarCreateProjectProps {
  className?: string;
}

export interface StepMobileCreateProjectProps {
  className?: string;
}

export interface NavbarCreateProjectProps {
  children: React.ReactNode;
}

// API request/response types
export interface CreateProjectRequest {
  project_name: string;
  url: string;
  project_color?: string;
}

export interface FullProjectRequest extends CreateProjectRequest {
  domain_type: string;
  primary_search_engines: any[];
  keywords: any[];
  competitors: any[];
}

export interface UpdateProjectRequest {
  project_name?: string;
  url?: string;
  project_color?: string;
  primary_search_engines?: any[];
  keywords?: any[];
  competitors?: any[];
}

// Validation types
export interface ModeValidation {
  validateCreateMode: (isInCreateMode: boolean, hasProjectData: boolean) => boolean;
  validateEditMode: (isInEditMode: boolean, hasProjectData: boolean, projectId: string | null) => boolean;
  detectDataLeakage: (isInCreateMode: boolean, isInEditMode: boolean, hasProjectData: boolean, projectId: string | null) => {
    hasLeakage: boolean;
    reason?: string;
  };
  cleanupLeakedData: (resetStore: Function, setCreateFromScratch: Function) => void;
}

// Error types
export interface CreateProjectError {
  message: string;
  code?: string;
  field?: string;
  context?: string;
}

// Toast notification types
export interface CreateProjectToastOptions {
  loadingMessage?: string;
  successMessage?: string;
  errorContext?: string;
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
}
