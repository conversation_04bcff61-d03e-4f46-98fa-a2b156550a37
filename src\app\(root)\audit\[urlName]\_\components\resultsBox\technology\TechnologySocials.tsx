import React, { useState, useMemo } from "react";
import Image from "next/image";

type Technology = {
  name: string;
  version?: string;
  category?: string;
};

// Function to get the correct icon URL with special handling for common technologies
const getIconUrl = (name: string): string => {
  const lowerName = name.toLowerCase();

  // Special cases for common technologies
  if (lowerName.includes("facebook")) {
    return "https://cdn.simpleicons.org/facebook";
  }
  if (lowerName.includes("google analytics")) {
    return "https://cdn.simpleicons.org/googleanalytics";
  }
  if (lowerName.includes("google tag manager")) {
    return "https://cdn.simpleicons.org/googletagmanager";
  }
  if (lowerName.includes("linkedin")) {
    return "https://cdn.simpleicons.org/linkedin";
  }
  if (lowerName.includes("twitter")) {
    return "https://cdn.simpleicons.org/twitter";
  }
  if (lowerName.includes("reddit")) {
    return "https://cdn.simpleicons.org/reddit";
  }
  if (lowerName.includes("jquery")) {
    return "https://cdn.simpleicons.org/jquery";
  }

  // For all other names, remove spaces and convert to lowercase
  return `https://cdn.simpleicons.org/${name
    .replace(/\s+/g, "")
    .toLowerCase()}`;
};

const PlaceholderIcon = () => (
  <svg
    className="w-7 h-7 text-gray-600"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm7.4-2a5.6 5.6 0 0 0 0-2l2-1.5a.5.5 0 0 0 .1-.7l-2-3.5a.5.5 0 0 0-.6-.2l-2.3 1a5.7 5.7 0 0 0-1.7-1l-.3-2.5a.5.5 0 0 0-.5-.4h-4a.5.5 0 0 0-.5.4l-.3 2.5a5.7 5.7 0 0 0-1.7 1l-2.3-1a.5.5 0 0 0-.6.2l-2 3.5a.5.5 0 0 0 .1.7l2 1.5a5.6 5.6 0 0 0 0 2l-2 1.5a.5.5 0 0 0-.1.7l2 3.5a.5.5 0 0 0 .6.2l2.3-1a5.7 5.7 0 0 0 1.7 1l.3 2.5a.5.5 0 0 0 .5.4h4a.5.5 0 0 0 .5-.4l.3-2.5a5.7 5.7 0 0 0 1.7-1l2.3 1a.5.5 0 0 0 .6-.2l2-3.5a.5.5 0 0 0-.1-.7l-2-1.5z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

function TechnologySocials({ data }: { data: Technology[] }) {
  // Track which images have failed to load
  const [failedImages, setFailedImages] = useState<Record<number, boolean>>({});

  // Handle image load error
  const handleImageError = (index: number) => {
    setFailedImages((prev) => ({
      ...prev,
      [index]: true,
    }));
  };

  // Memoize the icon URLs to prevent unnecessary recalculations
  const iconUrls = useMemo(() => {
    return data.map((item) => getIconUrl(item.name));
  }, [data]);

  return (
    <div className="w-full border border-light-gray rounded-lg p-4">
      <h4 className="text-secondary font-semibold mb-4">
        Detected Technologies
      </h4>
      <div className="flex flex-col gap-6">
        {data.map((item, index) => (
          <div
            key={index}
            className="w-full pb-6 last:pb-0 border-b border-light-gray last:border-b-0 flex items-center justify-between"
          >
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                {failedImages[index] ? (
                  <PlaceholderIcon />
                ) : (
                  <Image
                    src={iconUrls[index]}
                    alt={item.name}
                    width={28}
                    height={28}
                    className="w-7 h-7"
                    onError={() => handleImageError(index)}
                    unoptimized
                  />
                )}
                <span className="text-sm font-semibold text-secondary">
                  {item.name}
                </span>
              </div>

              {item.category && (
                <span className="text-xs text-secondary/70 mt-1">
                  {item.category}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Wrap the component with React.memo to prevent unnecessary re-renders
export default React.memo(TechnologySocials);
