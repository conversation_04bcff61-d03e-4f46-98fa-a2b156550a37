"use client";
import React, { useState } from "react";
import Image from "next/image";

export interface HeaderSectionProps {
  urlName: string;
  onPageSeoData?: {
    serp_preview?: {
      url?: string;
    };
  };
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const HeaderSection: React.FC<HeaderSectionProps> = ({
  urlName,
  onPageSeoData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  const [failedImages, setFailedImages] = useState<Record<string, boolean>>({});

  // Get current date for report generation timestamp
  const today = new Date();
  const time = {
    day: today.getDate(),
    month: today.toLocaleString("default", { month: "long" }),
    year: today.getFullYear(),
    hours: today.getHours(),
    minutes: today.getMinutes().toString().padStart(2, "0"),
  };

  const handleImageError = (id: string) => {
    setFailedImages((prev) => ({
      ...prev,
      [id]: true,
    }));
    onImageError?.(id);
  };

  const handleImageLoad = (id: string) => {
    onImageLoad?.(id);
  };

  return (
    <div className="relative">
      {/* Professional header with modern design - reduced padding for better space utilization */}
      <div className="bg-gradient-to-br from-primary/8 via-primary/5 to-primary/3 rounded-2xl p-6 border border-primary/20 shadow-lg">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-6">
            {/* Enhanced brand logo */}
            <div className="w-16 h-16 relative">
              {brand_photo != null || true ? (
                <div className="w-full h-full relative">
                  {/* Try regular img tag first for better PDF compatibility */}
                  <img
                    src={brand_photo || "/images/appLogo.svg"}
                    alt="Brand Logo"
                    className="w-full h-full object-contain rounded-xl shadow-md border border-white/50"
                    referrerPolicy="no-referrer"
                    onLoad={() => handleImageLoad("brandLogo")}
                    onError={() => handleImageError("brandLogo")}
                    style={{
                      maxWidth: "64px",
                      maxHeight: "64px",
                      width: "64px",
                      height: "64px",
                    }}
                  />
                  {failedImages["brandLogo"] && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-xl border border-gray-300">
                      <span className="text-gray-500 font-bold text-sm">
                        LOGO
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-xl border border-gray-300 shadow-md">
                  <span className="text-gray-500 font-bold text-sm">LOGO</span>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-primary tracking-tight">
                SEO Audit Report
              </h1>
              <div className="flex items-center gap-4 text-sm">
                <p className="text-gray-600 font-medium">
                  Generated on {time.day} {time.month} {time.year} at{" "}
                  {time.hours}:{time.minutes}
                </p>
                <>
                  <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                  <p className="text-primary/90 font-semibold">
                    Audit by {brand_name || "SEO Analyser"}
                  </p>
                </>
              </div>
            </div>
          </div>

          {/* Website info card */}
          <div className="text-right bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 shadow-sm">
            <h2 className="text-lg font-bold text-primary mb-1 break-all">
              {onPageSeoData?.serp_preview?.url || urlName}
            </h2>
            <span className="text-sm font-medium text-primary/70">
              {brand_website ?? "www.seoanalyser.com.au"}
            </span>
          </div>
        </div>

        {/* Professional description - reduced padding */}
        <div className="mt-4 bg-white/60 backdrop-blur-sm p-4 rounded-xl border border-white/50 shadow-sm">
          <p className="text-gray-700 text-sm leading-relaxed">
            This comprehensive SEO audit analyzes{" "}
            <span className="font-semibold text-primary">
              {onPageSeoData?.serp_preview?.url || urlName}
            </span>
            &apos;s performance across multiple critical factors including
            on-page optimization, technical SEO, usability, performance, and
            more. Each section provides actionable recommendations to enhance
            your search engine visibility and user experience.
          </p>
        </div>
      </div>
    </div>
  );
};
