import React, { useState, useMemo, forwardRef } from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* =============================== STYLES =============================== */
import "./DateRangePicker.css";

/* ============================ REACT DAY PICKER ============================ */
import { DateRange } from "react-day-picker";

/* ================================== ICONS ================================= */
import { MdCompareArrows } from "react-icons/md";

/* ============================= EXTRA CONSTANTS ============================ */
import { getPastTenYears, months } from "./Constants";

/* =============================== COMPONENTS =============================== */
import RangePickerCalendar from "@/components/ui/range-picker-calendar/RangePickerCalendar";
import Card from "@/components/ui/card";
import { AnimatePresence } from "framer-motion";

/* ================================= ZUSTAND ================================ */
import { useDateRangeStore } from "@/store/useDateRangeStore";

/* ================================== MAIN ================================== */
const DateRangePicker = forwardRef<
  HTMLDivElement,
  { ShowDateRangePicker: boolean }
>(({ ShowDateRangePicker }, ref) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */

  // Zustand store
  const {
    selectedRange,
    setSelectedRange,
    comparisonRange,
    setComparisonRange,
    isComparisonEnabled,
    setComparisonEnabled,
  } = useDateRangeStore();

  const monthsDropdownData = useMemo(
    () => ({
      button: months[0],
      options: months,
    }),
    [months]
  );

  const yearsDropdownData = useMemo(
    () => ({
      button: getPastTenYears()[0],
      options: getPastTenYears(),
    }),
    [getPastTenYears]
  );

  const [month, setMonth] = useState(new Date());
  const [monthNext, setMonthNext] = useState(new Date());

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <AnimatePresence>
      {ShowDateRangePicker && (
        <Card
          ref={ref}
          key={ShowDateRangePicker ? "show" : "hide"}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          className={cn(
            "w-fit flex p-0 overflow-hidden border-2 shadow-xl absolute z-40 top-[290px] xl:top-[320px] left-[50vw] -translate-x-1/2 xl:-translate-x-0 xl:left-auto min-w-[800px] min-h-[400px]"
          )}
        >
          <div className="flex p-4 pl-4 items-start gap-4 w-full">
            <div className="flex-shrink-0">
              <RangePickerCalendar
                selectedColor="bg-primary"
                yearDropdown={yearsDropdownData}
                monthDropdown={monthsDropdownData}
                month={month}
                setMonth={setMonth}
                selected={selectedRange}
                setSelected={setSelectedRange}
              />
            </div>
            <div className="h-full flex items-center justify-center pt-[10%]">
              <button
                role="button"
                className={cn(
                  "rounded-lg p-1.5",
                  !isComparisonEnabled
                    ? "inner-shadow-disabled"
                    : "inner-shadow-enabled"
                )}
              >
                <MdCompareArrows
                  className={cn(
                    "text-xl text-secondary cursor-pointer transition-all duration-200"
                  )}
                  onClick={() => {
                    setComparisonRange(undefined);
                    setComparisonEnabled(!isComparisonEnabled);
                  }}
                />
              </button>
            </div>
            <div className="flex-shrink-0">
              <RangePickerCalendar
                disabled={!isComparisonEnabled}
                selectedColor="bg-primary-orange"
                yearDropdown={yearsDropdownData}
                monthDropdown={monthsDropdownData}
                month={monthNext}
                setMonth={setMonthNext}
                selected={comparisonRange}
                setSelected={setComparisonRange}
              />
            </div>
          </div>
        </Card>
      )}
    </AnimatePresence>
  );
});

DateRangePicker.displayName = "DateRangePicker";

export default DateRangePicker;
