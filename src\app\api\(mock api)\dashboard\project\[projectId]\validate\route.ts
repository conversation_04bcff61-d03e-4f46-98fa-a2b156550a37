import { NextRequest, NextResponse } from "next/server";

/**
 * Mock API endpoint for project validation
 * Validates if a project exists and user has access to it
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const { projectId } = params;

    // Basic validation
    if (!projectId || typeof projectId !== "string" || projectId.trim().length === 0) {
      return NextResponse.json(
        {
          isValid: false,
          message: "Invalid project ID format",
        },
        { status: 400 }
      );
    }

    // Mock validation logic - in a real app, this would check the database
    // For now, we'll consider any non-empty project ID as valid
    // You can add specific invalid project IDs for testing error states
    const invalidProjectIds = ["invalid", "deleted", "unauthorized"];
    
    if (invalidProjectIds.includes(projectId.toLowerCase())) {
      return NextResponse.json(
        {
          isValid: false,
          message: "Project not found or access denied",
        },
        { status: 404 }
      );
    }

    // Simulate API delay for realistic behavior
    await new Promise(resolve => setTimeout(resolve, 100));

    return NextResponse.json({
      isValid: true,
      message: "Project is valid and accessible",
      projectId,
    });

  } catch (error) {
    console.error("Project validation error:", error);
    
    return NextResponse.json(
      {
        isValid: false,
        message: "Internal server error during project validation",
      },
      { status: 500 }
    );
  }
}
