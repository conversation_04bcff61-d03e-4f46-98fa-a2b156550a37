import { SVGProps } from "react";

export function FacebookCircleIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_3859)">
        <path
          d="M24 12.001C24 5.3736 18.6274 0.000976562 12 0.000976562C5.37262 0.000976562 0 5.3736 0 12.001C0 17.9905 4.38825 22.955 10.125 23.8552V15.4697H7.07812V12.001H10.125V9.35723C10.125 6.34973 11.9166 4.68848 14.6576 4.68848C15.9705 4.68848 17.3438 4.92285 17.3438 4.92285V7.87598H15.8306C14.3399 7.87598 13.875 8.80101 13.875 9.75004V12.001H17.2031L16.6711 15.4697H13.875V23.8552C19.6117 22.955 24 17.9906 24 12.001Z"
          fill="#1877F2"
        />
        <path
          d="M16.6711 15.4697L17.2031 12.001H13.875V9.75004C13.875 8.80091 14.3399 7.87598 15.8306 7.87598H17.3438V4.92285C17.3438 4.92285 15.9705 4.68848 14.6575 4.68848C11.9166 4.68848 10.125 6.34973 10.125 9.35723V12.001H7.07812V15.4697H10.125V23.8552C10.7453 23.9524 11.3722 24.0011 12 24.001C12.6278 24.0011 13.2547 23.9524 13.875 23.8552V15.4697H16.6711Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_204_3859">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
