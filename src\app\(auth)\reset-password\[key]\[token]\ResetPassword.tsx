"use client";

import BoxForm from "@/app/(auth)/components/BoxForm";
import PassInput from "@/app/(auth)/components/input/PassInput";
import schemaPassword from "@/app/(auth)/components/input/passwordYum";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import { resetPassword } from "@/services/authService";
import { useAuthStore } from "@/store/authStore";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";

export default function ResetPassword() {
  const { key, token } = useParams<{ key: string; token: string }>()!;
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [step, setStep] = useState("reset");
  const clearError = useAuthStore((state) => state.clearErrors);
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (confirmPassword !== password) return setError("Passwords do not match");
    if (!confirmPassword || !password)
      return setError("Please fill in all fields");
    try {
      await schemaPassword.validate({ password });
    } catch (err: any) {
      if (err.name === "ValidationError") {
        return setError(err.message);
      }
    }
    setIsLoading(true);
    setError("");
    const body = {
      new_password: password,
      confirm_password: confirmPassword,
    };
    const response = await resetPassword(
      body,
      `/api/accounts/reset-password/${key}/${token}/`
    );
    if (response.statusCode) {
      if (response.statusCode >= 200 && response.statusCode < 300) {
        setStep("save");
      }
    }
    if (response?.error) {
      setError(response?.error);
    }
    setIsLoading(false);
  };
  useEffect(() => {
    clearError();
  }, [clearError]);
  switch (step) {
    case "reset":
      return (
        <BoxForm>
          <form
            onSubmit={handleSubmit}
            className="flex flex-col gap-5"
            autoComplete="off"
            method="post"
            action="javascript:void(0);"
            data-form-type="signup"
          >
            <PassInput
              onChange={setPassword}
              placeholder="Type Your Password here."
            />
            <PassInput
              confirmPassword
              onChange={setConfirmPassword}
              placeholder="Confirm Your Password here."
            />
            {error && <div className="text-primary-red text-xs">{error}</div>}
            <ButtenSubmit
              text="Change Password"
              type="submit"
              textloading="Sending..."
              isLoading={isLoading}
            />
          </form>
        </BoxForm>
      );
    case "save":
      return (
        <BoxForm>
          <div>
            <h2 className="text-lg">Password Changed</h2>
            <p className="my-5">
              Your password has been successfully changed.
              <br />
              You can now log in using your new password.
            </p>
            <Link href={"/login"} className="btn btn--primary">
              Back
            </Link>
          </div>
        </BoxForm>
      );
  }
}
