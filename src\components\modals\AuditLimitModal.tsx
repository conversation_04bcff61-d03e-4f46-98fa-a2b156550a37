"use client";
import Modal from "@/ui/Modal";
import Link from "next/link";
import sessionService from "@/services/sessionService";

type AuditLimitModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function AuditLimitModal({
  isOpen,
  onClose,
}: AuditLimitModalProps) {
  const maxAudits = sessionService.getMaxFreeAudits();

  return (
    <Modal open={isOpen} onClose={onClose} title="Login Required" size="md">
      <div className="flex flex-col items-center p-3 sm:p-4">
        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2 sm:mb-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 sm:h-6 sm:w-6 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        </div>

        <h3 className="text-base sm:text-lg font-bold text-secondary mb-1.5 sm:mb-2 text-center">
          Free Audit Limit Reached
        </h3>

        <p className="text-center text-secondary text-xs sm:text-sm mb-3 sm:mb-4 px-1 leading-relaxed">
          You've used all {maxAudits} free audits for this session. Login or
          create an account for unlimited access.
        </p>

        <div className="flex flex-col w-full gap-1.5 sm:gap-2">
          <Link
            href="/signin"
            className="btn btn--primary w-full text-center text-sm sm:text-base py-2 sm:py-3"
          >
            Login
          </Link>
          <Link
            href="/signup"
            className="btn btn--secondary w-full text-center text-sm sm:text-base py-2 sm:py-3"
          >
            Create Account
          </Link>
        </div>
      </div>
    </Modal>
  );
}
