import { CallingIcon } from "@/ui/icons/communication";
import Image from "next/image";

export default function AboutPic() {
  return (
    <div className="w-full flex-1 lg:w-auto flex items-center justify-center lg:justify-start">
      <div className="w-full min-[530px]:w-auto lg:w-[631px] flex justify-start relative pl-4.5 lg:pl-[58px] pb-6 lg:pb-[50px]">
        {/* <Image
          src={"/images/about-lg-pic.png"}
          alt="About Us"
          width={466}
          height={293}
          className="w-[294px] lg:w-[466px]"
        />
        <Image
          src={"/images/about-subtrac.svg"}
          alt="About Us subtrac"
          width={177}
          height={152}
          className="w-[111px] h-[95px] lg:w-[177px] lg:h-[152px] absolute top-[176px] left-[268px] lg:top-[280px] lg:left-[453px]"
        /> */}
        <Image
          src={"/images/aboutUsPic.png"}
          alt="About Us pic"
          width={1146}
          height={863}
          className="w-full"
        />
        <Image
          src={"/images/about-weekly-overview.svg"}
          alt="About Us weekly overview"
          width={234}
          height={274}
          className="w-[147px] h-[172px] sm:w-[234px] sm:h-[274px] absolute -top-[31px] right-0 lg:-top-[50px] lg:left-[417px] floating-animate"
        />
        <CallDetails />
      </div>
    </div>
  );
}

function CallDetails() {
  return (
    <div className="w-auto bg-white p-2.5 sm:p-4 rounded-[5px] sm:rounded-lg shadow-[0_4px_4px_0_rgba(0,0,0,0.1)] floating-animate absolute top-[109px] sm:top-[173px] left-[-11px] sm:left-0">
      <div className="flex items-center gap-1 sm:gap-2.5 mb-1 sm:mb-2">
        <div className="w-[21px] h-[21px] sm:w-[34px] sm:h-[34px] flex items-center justify-center bg-secondary/10 text-secondary rounded-[5px] sm:rounded-lg">
          <CallingIcon className="w-[11px] h-[11px] sm:w-4.5 sm:h-4.5" />
        </div>
        <div className="text-[10px] sm:text-base font-semibold text-secondary">
          Let's connect
        </div>
      </div>
      <div className="text-[8px] sm:text-sm text-secondary">
        we are here for you
      </div>
    </div>
  );
}
