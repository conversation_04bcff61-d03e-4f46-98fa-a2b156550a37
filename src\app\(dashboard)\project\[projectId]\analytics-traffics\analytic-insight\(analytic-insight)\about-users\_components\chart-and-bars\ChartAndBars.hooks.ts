import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import type { ChartsAndBars } from "../../../types/ChartAndBars.types";
import { useProjectId } from "@/hooks/useProjectId";

const useChartAndBars = ({
  tab,
  domesticFilter,
  tabFilter,
}: {
  tab: string;
  domesticFilter: string;
  tabFilter: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["charts-and-bars", projectId, tab, domesticFilter, tabFilter],
    queryFn: async (): Promise<ChartsAndBars> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/about-users/chart-and-bars-section",
        {
          params: {
            projectId,
            tab,
            domesticFilter,
            tabFilter,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default useChartAndBars;
