"use client";

import { useEffect, useState } from "react";

type Props = {
  percentage: number;
  layout?: "horizontal" | "vertical";
  className?: string;
};

export default function ProgressPercent({
  percentage,
  layout = "horizontal",
  className,
}: Props) {
  const [percentageValue, setPercentageValue] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setPercentageValue(percentage);
    }, 100);

    return () => clearInterval(interval);
  }, [percentage]);

  return layout === "vertical" ? (
    <div
      style={{ height: `${percentageValue}%` }}
      className={`w-4 rounded-full bg-primary duration-700 ease-in-out ${className}`}
    ></div>
  ) : (
    <div
      style={{ width: `${percentageValue}%` }}
      className={`h-4 rounded-full bg-primary duration-700 ease-in-out ${className}`}
    ></div>
  );
}
