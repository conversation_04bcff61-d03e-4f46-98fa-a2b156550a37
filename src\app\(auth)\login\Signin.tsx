// app/signin/page.tsx
"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import EmailInput from "../components/input/EmailInput";
import PassInput from "../components/input/PassInput";
import KeepLoggedCheck from "../components/input/KeepLoggedCheck";
import Link from "next/link";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import BoxForm from "../components/BoxForm";
import GoogleLogin from "../components/GoogleLogin";
import profileService from "@/services/profileService";
import FaqsSettingProfile from "../components/FaqsSettingProfile";
import { useAuthStore } from "@/store/authStore";
import { showToast } from "@/lib/toast";
import {
  restoreAnalysisContext,
  hasPendingAnalysisAction,
} from "@/utils/requireLogin";

export default function SignInPage() {
  const [password, setPassword] = useState("");
  const [keepLoggedIn, setKeepLoggedIn] = useState(false);
  const [email, setEmail] = useState("");
  const [faqsAccordion, setFaqsAccordion] =
    useState<Record<number, string[]>>();
  const router = useRouter();
  const login = useAuthStore((state) => state.login);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);
  const clearError = useAuthStore((state) => state.clearErrors);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validation checks
    if (!email.trim()) {
      showToast.error("Please enter your email address");
      return;
    }

    if (!password) {
      showToast.error("Please enter your password");
      return;
    }

    try {
      const response = await login({ email, password });

      if (response) {
        showToast.success("Successfully logged in");
        const data = await profileService.getProfileSetting();

        if (data) {
          const ignoredKeys = [
            "first_name",
            "last_name",
            "phone_number",
            "current_avatar_id",
          ];

          const emptyFields = Object.entries(data).filter(([key, val]) => {
            if (ignoredKeys.includes(key)) return false;
            return Array.isArray(val) ? val.length === 0 : !val;
          });

          if (emptyFields.length) {
            const body = [
              data.business_type,
              data.company_size,
              data.user_role,
              data.offers_seo_services,
              data.help_areas,
              data.interested_features,
            ] as Record<number, string[]>;
            setFaqsAccordion(body);
          }
          if (emptyFields.length === 0) {
            // Check for enhanced analysis context restoration
            const analysisContextUrl = restoreAnalysisContext();

            if (analysisContextUrl) {
              console.log("Restoring analysis context:", analysisContextUrl);
              // Use window.location.href to preserve all URL parameters and state
              window.location.href = analysisContextUrl;
              return;
            }

            // Fallback to legacy redirect system
            const postLoginRedirect =
              sessionStorage.getItem("postLoginRedirect");
            const postLoginCallback =
              sessionStorage.getItem("postLoginCallback");

            if (postLoginRedirect) {
              sessionStorage.removeItem("postLoginRedirect");
              console.log("Redirecting to stored URL:", postLoginRedirect);
              // Use window.location.href for external URLs or complex URLs with query params
              window.location.href = postLoginRedirect;
              return;
            }

            if (postLoginCallback) {
              sessionStorage.removeItem("postLoginCallback");
              console.log(
                "Post-login callback detected, redirecting to my-projects"
              );
              // For callbacks, redirect to dashboard where the action can be retried
              router.push("/my-projects");
              return;
            }

            // Default redirect to dashboard
            console.log("No post-login redirect, going to my-projects");
            router.push("/my-projects");
          }
        }
      }
    } catch (err: any) {
      console.error("Login error:", err);
      showToast.error(err?.message || "Failed to log in. Please try again.");
    }
  };

  useEffect(() => {
    clearError();
  }, [clearError]);

  // Display error as toast when it changes
  useEffect(() => {
    if (error) {
      showToast.error(error);
    }
  }, [error]);

  if (faqsAccordion) {
    return <FaqsSettingProfile faqs={faqsAccordion} />;
  }

  return (
    <BoxForm title="Welcome Back">
      <div className="mb-1">
        <p className="text-gray-600 text-sm">
          Please enter your credentials to access your account
        </p>
      </div>

      <form
        onSubmit={handleSubmit}
        className="flex flex-col gap-4 w-full"
        autoComplete="off"
        method="post"
        action="javascript:void(0);"
        data-form-type="signin"
      >
        <EmailInput
          onChange={setEmail}
          placeholder="Enter your email address"
        />

        <PassInput onChange={setPassword} placeholder="Enter your password" />

        <div className="flex items-center justify-between flex-wrap gap-2">
          <KeepLoggedCheck
            keepLoggedIn={keepLoggedIn}
            onChange={setKeepLoggedIn}
          />
          <Link
            href="/forgot-password"
            className="text-sm text-primary hover:underline transition-all"
          >
            Forgot Password?
          </Link>
        </div>

        <ButtenSubmit
          text="Sign In"
          type="submit"
          textloading="Signing in..."
          isLoading={isLoading}
        />

        <div className="w-full flex items-center gap-4 my-1">
          <span className="w-full h-[1px] bg-gray-200"></span>
          <span className="text-gray-500 text-sm whitespace-nowrap">or</span>
          <span className="w-full h-[1px] bg-gray-200"></span>
        </div>

        <GoogleLogin />

        <div className="text-center text-sm text-gray-600">
          Don't have an account?{" "}
          <Link
            href="/signup"
            className="text-primary font-medium hover:underline"
          >
            Create Account
          </Link>
        </div>
      </form>
    </BoxForm>
  );
}
