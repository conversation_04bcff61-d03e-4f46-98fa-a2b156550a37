"use client";
import React from "react";
import BoxCreateProject from "./BoxCreateProject";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { FaCircleCheck } from "react-icons/fa6";
import { FaUsers } from "react-icons/fa";
import {
  ComputerDesktopIcon,
  Cog6ToothIcon,
  KeyIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";

import { useCreateProjectStore } from "@/store/createProjectStore";
import { useEditNavigation } from "@/hooks/useEditProject";
import { motion } from "framer-motion";

const menuSidebarBase = [
  {
    name: "Project information",
    step: "project-information",
    icon: <ComputerDesktopIcon className="w-6 h-6" />,
  },
  {
    name: "Search engines",
    step: "search-engines",
    icon: <Cog6ToothIcon className="w-6 h-6" />,
  },
  {
    name: "Keywords",
    step: "keywords",
    icon: <KeyIcon className="w-6 h-6" />,
  },
  {
    name: "Competitors",
    step: "competitors",
    icon: <FaUsers className="w-6 h-6" />,
  },
  {
    name: "Statistics and Analytics services",
    step: "analytics-services",
    icon: <ChartBarIcon className="w-6 h-6" />,
  },
];

export default function SidebarCreateProject() {
  const path = usePathname();

  // Store hooks
  const {
    projectInfo,
    searchEngineConfigs,
    keywords,
    competitors,
    canAccessStep,
    isStepComplete,
  } = useCreateProjectStore();

  // Edit navigation hook to get URLs with project_id parameter
  const { urls, buildUrl } = useEditNavigation();

  // Build menu items with proper URLs (including project_id in edit mode)
  const menuSidebar = menuSidebarBase.map((item) => {
    let url: string;
    switch (item.step) {
      case "project-information":
        url = urls.projectInformation;
        break;
      case "search-engines":
        url = urls.searchEngines;
        break;
      case "keywords":
        url = urls.keywords;
        break;
      case "competitors":
        url = urls.competitors;
        break;
      case "analytics-services":
        url = urls.analyticsServices;
        break;
      default:
        url = buildUrl(`/create-project/${item.step}`);
    }

    return {
      ...item,
      url,
    };
  });

  // Helper function to check if a step is accessible (using store's enhanced logic)
  const isStepAccessible = (step: string): boolean => {
    const stepUrl = `/create-project/${step}`;
    return canAccessStep(stepUrl);
  };

  // Helper function to check if a step is completed (using store's enhanced logic)
  const isStepCompleted = (step: string): boolean => {
    switch (step) {
      case "project-information":
        return isStepComplete("projectInformation") || !!projectInfo;
      case "search-engines":
        return (
          isStepComplete("searchEngines") || searchEngineConfigs.length > 0
        );
      case "keywords":
        return isStepComplete("keywords") || keywords.length > 0;
      case "competitors":
        return isStepComplete("competitors") || competitors.length > 0;
      case "analytics-services":
        return isStepComplete("analyticsServices"); // Can be completed when analytics are set up
      default:
        return false;
    }
  };

  return (
    <BoxCreateProject classPlus="flex flex-col h-full relative overflow-hidden">
      {/* Logo Section */}
      <div className="flex justify-center mb-8">
        <Link
          href={"/"}
          className="w-[120px] h-[48px] lg:w-[140px] lg:h-[55px] relative inline-block transition-transform duration-200 hover:scale-105"
        >
          <Image
            src="/images/appLogo.svg"
            alt="seo analyser logo"
            fill
            quality={100}
            className="w-full h-full object-contain"
            priority
          />
        </Link>
      </div>

      {/* Project Name Section */}
      <div className="mb-8 opacity-0 animate-fade-in">
        {projectInfo?.name && (
          <div className="text-center">
            <h2 className="text-base font-semibold text-[#344054] leading-tight tracking-tight">
              {projectInfo.name}
            </h2>
            <p className="text-sm text-[#344054] mt-1">Project Setup</p>
          </div>
        )}
        {!projectInfo?.name && (
          <div className="text-center">
            <h2 className="text-base font-semibold text-[#344054] leading-tight tracking-tight">
              Create Project
            </h2>
            <p className="text-sm text-[#344054] mt-1">Project Setup</p>
          </div>
        )}
      </div>

      {/* Navigation Steps */}
      <nav className="flex-1">
        <ul className="space-y-2">
          {menuSidebar.map((item, index) => {
            const isCompleted = isStepCompleted(item.step);
            const isActive =
              path === item.url ||
              path.includes(`/create-project/${item.step}`);
            const isAccessible = isStepAccessible(item.step);
            const isDisabled = !isAccessible;

            const LinkComponent = isDisabled ? "div" : Link;
            const linkProps = isDisabled ? {} : { href: item.url };

            return (
              <motion.li
                key={index}
                initial={{
                  opacity: 0,
                  x: -20,
                  willChange: "transform, opacity",
                }}
                animate={{
                  opacity: 1,
                  x: 0,
                  willChange: "auto",
                  transition: {
                    duration: 0.3,
                    delay: index * 0.1,
                    ease: [0.25, 0.1, 0.25, 1] as const,
                  },
                }}
                whileHover={
                  !isDisabled
                    ? {
                        scale: 1.01,
                        transition: { duration: 0.2 },
                      }
                    : {}
                }
                whileTap={
                  !isDisabled
                    ? {
                        scale: 0.99,
                        transition: { duration: 0.1 },
                      }
                    : {}
                }
              >
                <LinkComponent
                  {...linkProps}
                  className={`
                                        group relative flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200
                                        ${
                                          isActive
                                            ? "bg-primary/10 text-primary shadow-sm border border-primary/20"
                                            : isCompleted
                                            ? "text-primary hover:bg-primary/5"
                                            : isDisabled
                                            ? "text-gray-400 cursor-not-allowed"
                                            : "text-[#344054] hover:bg-gray-50 hover:text-[#344054]"
                                        }
                                    `}
                  onClick={(e) => {
                    if (isDisabled) {
                      e.preventDefault();
                    }
                  }}
                >
                  {/* Step Icon */}
                  <div
                    className={`
                                        flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200
                                        ${
                                          isActive
                                            ? "bg-primary text-white shadow-sm"
                                            : isCompleted
                                            ? "bg-primary/10 text-primary"
                                            : "bg-gray-100 text-gray-500 group-hover:bg-gray-200"
                                        }
                                    `}
                  >
                    {isCompleted ? (
                      <FaCircleCheck className="w-4 h-4" />
                    ) : (
                      <div
                        className={`${
                          isActive
                            ? "text-white"
                            : isCompleted
                            ? "text-primary"
                            : "text-gray-500"
                        }`}
                      >
                        {item.icon}
                      </div>
                    )}
                  </div>

                  {/* Step Content */}
                  <div className="flex-1 min-w-0">
                    <p
                      className={`
                                            text-sm leading-tight transition-colors duration-200
                                            ${
                                              isActive
                                                ? "text-primary font-bold"
                                                : isCompleted
                                                ? "text-primary font-medium"
                                                : "text-current font-medium"
                                            }
                                        `}
                    >
                      {item.name}
                    </p>
                    {isActive && (
                      <p className="text-xs text-primary/70 mt-0.5">
                        Current step
                      </p>
                    )}
                    {isCompleted && (
                      <p className="text-xs text-primary/70 mt-0.5">
                        Completed
                      </p>
                    )}
                  </div>

                  {/* Active Indicator */}
                  {isActive && (
                    <div className="absolute right-2 w-2 h-2 bg-primary rounded-full transition-all duration-200" />
                  )}

                  {/* Disabled Indicator */}
                  {isDisabled && (
                    <div className="ml-auto">
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  )}
                </LinkComponent>
              </motion.li>
            );
          })}
        </ul>
      </nav>

      {/* Progress Indicator */}
      <motion.div
        className="mt-8 pt-6 border-t border-gray-100"
        initial={{ opacity: 0, y: 20, willChange: "transform, opacity" }}
        animate={{
          opacity: 1,
          y: 0,
          willChange: "auto",
          transition: {
            duration: 0.4,
            delay: 0.6,
            ease: [0.25, 0.1, 0.25, 1] as const,
          },
        }}
      >
        <motion.div
          className="flex items-center justify-between mb-2"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            transition: { duration: 0.3, delay: 0.8 },
          }}
        >
          <span className="text-xs font-medium text-[#344054]">Progress</span>
          <motion.span
            className="text-xs font-semibold text-primary"
            animate={{
              scale: [1, 1.05, 1],
              transition: {
                duration: 0.5,
                delay: 1.0,
                ease: "easeInOut",
              },
            }}
          >
            {Math.round(
              ((menuSidebar.findIndex((item) => item.url === path) + 1) /
                menuSidebar.length) *
                100
            )}
            %
          </motion.span>
        </motion.div>
        <div className="w-full bg-gray-100 rounded-full h-2">
          <motion.div
            className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full"
            initial={{ width: 0, willChange: "width" }}
            animate={{
              width: `${
                ((menuSidebar.findIndex((item) => item.url === path) + 1) /
                  menuSidebar.length) *
                100
              }%`,
              willChange: "auto",
              transition: {
                duration: 0.8,
                delay: 0.9,
                ease: [0.25, 0.1, 0.25, 1] as const,
              },
            }}
          />
        </div>
      </motion.div>
    </BoxCreateProject>
  );
}
