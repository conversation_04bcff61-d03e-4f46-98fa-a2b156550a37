## Audience API

**Endpoint:**

`GET /api/dashboard/project/analytics-traffics/analytic-insight/audience`

---

## Query Parameters

| Name           | Type     | Required | Description                                                                   |
| -------------- | -------- | -------- | ----------------------------------------------------------------------------- |
| `audience`     | `string` | No       | Can be used to specify or filter audience-related data (not currently in use) |

---

## Acceptable Values for `audience`

_(Case-sensitive! Value ≠ value)_

(No specific values currently required)

---

## Response Schema

```ts

{
  cardTabs: {
    title: string,
    value: string,
    changeValue: string
  }[],
  lineChartData: {
    name: string,
    clicks: number,
    impressions: number
  }[],
  colors: {
    name: string,
    color: string
  }[],
  selectedLines: string[],
  cardsData: {
    [key: string]: {
      amount: number,
      growth: string
    }
  },
  lineChartCards: {
    title: string,
    bigNumber: string,
    smallNumber: string,
    data: {
      name: string,
      value: number
    }[]
  }[]
}


```

## Response Example

```json
{
  "cardTabs": [
    { "title": "Active Users", "value": "20.3K", "changeValue": "+21.2" },
    { "title": "New Users", "value": "10.3K", "changeValue": "+21.2" },
    { "title": "Returning Users", "value": "10.3K", "changeValue": "+21.2" },
    { "title": "Sessions", "value": "10.3K", "changeValue": "+21.2" },
    { "title": "Engaged Session", "value": "10.3K", "changeValue": "+21.2" },
    { "title": "Views", "value": "10.3K", "changeValue": "+21.2" },
    { "title": "Avg.Engaged time", "value": "10.3K", "changeValue": "+21.2" }
  ],
  "lineChartData": [
    { "name": "may 01", "clicks": 100, "impressions": 1000 },
    { "name": "may 02", "clicks": 120, "impressions": 1100 },
    { "name": "may 03", "clicks": 130, "impressions": 1150 },
    { "name": "may 04", "clicks": 90, "impressions": 980 },
    { "name": "may 05", "clicks": 150, "impressions": 1300 }
  ],
  "colors": [
    { "name": "clicks", "color": "#4F46E5" },
    { "name": "impressions", "color": "#10B981" }
  ],
  "selectedLines": ["clicks", "impressions"],
  "cardsData": {
    "clicks": { "amount": 150, "growth": "+5%" },
    "impressions": { "amount": 1300, "growth": "-2%" }
  },
  "lineChartCards": [
    {
      "title": "Engagement Rate",
      "bigNumber": "01m:09s",
      "smallNumber": "00s",
      "data": [
        { "name": "Jul 1", "value": 200 },
        { "name": "Jul 2", "value": 240 },
        { "name": "Jul 3", "value": 260 },
        { "name": "Jul 4", "value": 300 },
        { "name": "Jul 5", "value": 280 },
        { "name": "Jul 6", "value": 310 },
        { "name": "Jul 7", "value": 330 }
      ]
    },
    {
      "title": "Active Users Rate",
      "bigNumber": "3K",
      "smallNumber": "+3.2%",
      "data": [
        { "name": "Jul 1", "value": 200 },
        { "name": "Jul 2", "value": 240 },
        { "name": "Jul 3", "value": 260 },
        { "name": "Jul 4", "value": 300 },
        { "name": "Jul 5", "value": 280 },
        { "name": "Jul 6", "value": 310 },
        { "name": "Jul 7", "value": 330 }
      ]
    },
    {
      "title": "Returning Users Rate",
      "bigNumber": "12.4K",
      "smallNumber": "+3.2%",
      "data": [
        { "name": "Jul 1", "value": 200 },
        { "name": "Jul 2", "value": 240 },
        { "name": "Jul 3", "value": 260 },
        { "name": "Jul 4", "value": 300 },
        { "name": "Jul 5", "value": 280 },
        { "name": "Jul 6", "value": 310 },
        { "name": "Jul 7", "value": 330 }
      ]
    }
  ]
}
```

## Important Notes

- All numeric values in strings (e.g., `"20.3K"`, `"01m:09s"`) are formatted for presentation purposes.

- The `colors` array maps each data type (like `clicks`, `impressions`) to a specific hex color for visual representation.

- `cardTabs` and `lineChartCards` are intended for UI card components displaying summarized analytics.
