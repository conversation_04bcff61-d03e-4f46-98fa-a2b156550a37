"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { toast } from "react-hot-toast";
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  HeartIcon,
  BoltIcon,
  FireIcon,
} from "@heroicons/react/24/solid";

export default function ToastTestingPage() {
  // Test functions for different toast types
  const showSuccessToast = () => {
    toast.success("Project created successfully! Your SEO analysis is ready.");
  };

  const showErrorToast = () => {
    toast.error(
      "Failed to save project. Please check your internet connection and try again."
    );
  };

  const showLoadingToast = () => {
    toast.loading("Analyzing your website... This may take a few moments.");
  };

  const showLongErrorToast = () => {
    toast.error(
      "Authentication failed. Your session has expired. Please log in again to continue with your project creation. This is a longer error message to test the layout."
    );
  };

  const showCustomToast = () => {
    toast.custom((t) => (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md">
        <div className="flex items-start gap-3">
          <InformationCircleIcon className="w-6 h-6 text-blue-600 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="font-medium text-blue-900">Custom Information</h4>
            <p className="text-sm text-blue-700 mt-1">
              This is a custom toast with custom styling and layout.
            </p>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="text-blue-600 hover:text-blue-800"
          >
            ×
          </button>
        </div>
      </div>
    ));
  };

  const showPromiseToast = () => {
    const promise = new Promise((resolve, reject) => {
      setTimeout(() => {
        Math.random() > 0.5 ? resolve("Success!") : reject("Failed!");
      }, 2000);
    });

    toast.promise(promise, {
      loading: "Processing your request...",
      success: "Request completed successfully!",
      error: "Request failed. Please try again.",
    });
  };

  const showMultipleToasts = () => {
    toast.success("First notification");
    setTimeout(() => toast.error("Second notification"), 500);
    setTimeout(() => toast.loading("Third notification"), 1000);
  };

  const showWarningToast = () => {
    toast((t) => (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md">
        <div className="flex items-start gap-3">
          <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="font-medium text-yellow-900">Warning</h4>
            <p className="text-sm text-yellow-700 mt-1">
              Some keywords may not be available in your selected region.
            </p>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="text-yellow-600 hover:text-yellow-800"
          >
            ×
          </button>
        </div>
      </div>
    ));
  };

  const showFunToasts = () => {
    const messages = [
      {
        icon: SparklesIcon,
        message: "✨ Magic is happening!",
        color: "purple",
      },
      { icon: HeartIcon, message: "❤️ We love your project!", color: "pink" },
      {
        icon: BoltIcon,
        message: "⚡ Lightning fast analysis!",
        color: "yellow",
      },
      { icon: FireIcon, message: "🔥 Your SEO is on fire!", color: "red" },
    ];

    messages.forEach((item, index) => {
      setTimeout(() => {
        toast.success(item.message);
      }, index * 300);
    });
  };

  const dismissAllToasts = () => {
    toast.dismiss();
  };

  return (
    <div className="min-h-screen bg-[#F4F4F4] py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-[#344054] mb-2">
            Toast Notification Testing
          </h1>
          <p className="text-[#344054]/70">
            Test all different types of toast notifications with our custom
            implementation
          </p>
        </motion.div>

        {/* Test Buttons Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8"
        >
          {/* Basic Toast Types */}
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <h3 className="font-semibold text-[#344054] mb-4">Basic Types</h3>
            <div className="space-y-3">
              <button
                onClick={showSuccessToast}
                className="w-full btn btn--primary text-sm"
              >
                <CheckCircleIcon className="w-4 h-4" />
                Success Toast
              </button>
              <button
                onClick={showErrorToast}
                className="w-full bg-red-600 hover:bg-red-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors flex items-center justify-center gap-2"
              >
                <XCircleIcon className="w-4 h-4" />
                Error Toast
              </button>
              <button
                onClick={showLoadingToast}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors flex items-center justify-center gap-2"
              >
                Loading Toast
              </button>
            </div>
          </div>

          {/* Advanced Types */}
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <h3 className="font-semibold text-[#344054] mb-4">
              Advanced Types
            </h3>
            <div className="space-y-3">
              <button
                onClick={showLongErrorToast}
                className="w-full bg-red-600 hover:bg-red-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
              >
                Long Error Message
              </button>
              <button
                onClick={showCustomToast}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
              >
                Custom Toast
              </button>
              <button
                onClick={showWarningToast}
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
              >
                Warning Toast
              </button>
            </div>
          </div>

          {/* Special Actions */}
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <h3 className="font-semibold text-[#344054] mb-4">
              Special Actions
            </h3>
            <div className="space-y-3">
              <button
                onClick={showPromiseToast}
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
              >
                Promise Toast
              </button>
              <button
                onClick={showMultipleToasts}
                className="w-full bg-green-600 hover:bg-green-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
              >
                Multiple Toasts
              </button>
              <button
                onClick={showFunToasts}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
              >
                Fun Sequence
              </button>
            </div>
          </div>
        </motion.div>

        {/* Control Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg p-6 shadow-sm border"
        >
          <h3 className="font-semibold text-[#344054] mb-4">Controls</h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={dismissAllToasts}
              className="bg-gray-600 hover:bg-gray-700 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
            >
              Dismiss All Toasts
            </button>
            <Link
              href="/create-project"
              className="bg-primary hover:bg-primary/90 text-white rounded-lg py-2 px-4 text-sm font-medium transition-colors"
            >
              Back to Create Project
            </Link>
          </div>
        </motion.div>

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8"
        >
          <h3 className="font-semibold text-blue-900 mb-2">
            Testing Instructions
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Click any button to trigger different toast types</li>
            <li>• Test the custom close buttons by clicking the × icon</li>
            <li>• Notice the framer-motion animations and progress bars</li>
            <li>• Try multiple toasts to see stacking behavior</li>
            <li>• Check responsiveness on different screen sizes</li>
          </ul>
        </motion.div>
      </div>
    </div>
  );
}
