"use client";
import React, { useState, useRef, useEffect } from "react";
import { MdClose } from "react-icons/md";
import { FaPlus } from "react-icons/fa6";
import Image from "next/image";
import PaginationTable from "./PaginationTable";
import { AutoCompleteCountry } from "./AutoCompleteCountry";
import countriesData from "@/data/countries";
import { motion, AnimatePresence } from "framer-motion";
import * as yup from "yup";
import SelectSearchEngine from "./SelectSearchEngine";
import { ExcelUploader } from "./ExcelUploader";
import { Switch } from "../ui/switch";
import { HiPaperClip } from "react-icons/hi2";
import * as ExcelJS from "exceljs";
import { defaultCountries, defaultSearchEngines } from "@/utils/flagUtils";
const schema = yup.object({
  username: yup
    .string()
    .required("")
    .matches(
      /^(?:(?:https?:\/\/)?(?:www\.)?)?((([a-zA-Z0-9\u0600-\u06FF\-]+\.)+[a-zA-Z]{2,}|localhost|\d{1,3}(\.\d{1,3}){3})(:\d+)?)(\/.*)?$/,
      "Enter a valid domain like example.com"
    )
    .max(50, "Keywords must be at most 50 characters"),
});
export type FieldsCreateProject = {
  country?: {
    name: string;
    code: string;
    image: string;
  } | null;
  searchEngines?:
    | {
        name: string;
        image: string;
      }[]
    | null;
  language?: {
    name: string;
    code?: string;
  } | null;
  location?: {
    code?: string;
    name: string;
  } | null;
};

type ValueKeywordType = {
  id: string;
  name: string;
  location: string | null;
  language: string | null;
  country: string | null;
  searchEngine: string | null;
  suggestAi: boolean;
  Volume?: string;
};

type KeywordsInputType = {
  keywordsName?: string[];
  optionsList?: FieldsCreateProject;
  setData?: (val: any) => void;
  placeHolder?: string;
  isHaveSelector?: boolean;
  classPlusBox?: string;
  classPlusInput?: string;
  maxList?: number;
  isValidateUrl?: boolean;
  importButtonText?: string;
};

export default function KeywordsInput({
  maxList,
  isValidateUrl,
  isHaveSelector,
  keywordsName,
  classPlusInput,
  optionsList,
  setData,
  placeHolder,
  classPlusBox,
  importButtonText,
}: KeywordsInputType) {
  const [value, setValue] = useState<ValueKeywordType[]>([]);
  const [dataState, setDataState] = useState<FieldsCreateProject>({
    country: defaultCountries.australia,
    searchEngines: [defaultSearchEngines.google],
  });
  const [addInputValue, setAddInputValue] = useState("");
  const [autoFill, setAutoFill] = useState<boolean>(true);
  const [err, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const MAX_KEYWORDS = maxList || 750;
  const ITEMS_PER_PAGE = 6;

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return value.slice(startIndex, endIndex);
  };

  const handleAdd = async (inputValue?: string, bool?: boolean) => {
    const trimmed = inputValue || addInputValue.trim();
    if (!trimmed) return;
    if (trimmed.length > 50) {
      setError(`Keywords must be at most 50 characters`);
      return;
    }
    if (value.length >= MAX_KEYWORDS) {
      setError(`Maximum limit of ${MAX_KEYWORDS} keywords reached.`);
      return;
    }
    if (value.some((v) => v.name.toLowerCase() === trimmed.toLowerCase())) {
      setError(`"${trimmed}" has already been added.`);
      return;
    }
    if (isValidateUrl) {
      try {
        await schema.validate({ username: trimmed });
      } catch (err: any) {
        setError(err.message);
        return;
      }
    }
    let newItems: ValueKeywordType[] = [];
    if (isHaveSelector) {
      // When selectors are available, use the selected values from dataState
      newItems = [
        {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
          name: trimmed,
          searchEngine: dataState.searchEngines?.length
            ? dataState.searchEngines[0].image
            : "",
          location: optionsList?.location?.name || "",
          language: optionsList?.language?.code || "EN",
          country: dataState?.country?.image || "",
          suggestAi: bool || false,
          Volume: bool ? "21k" : "",
        },
      ];
    } else if (optionsList?.searchEngines?.length) {
      // When no selectors, use fixed values from optionsList
      newItems = optionsList.searchEngines.map((engine) => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
        name: trimmed,
        searchEngine: engine.image,
        location: optionsList.location?.name || "",
        language: optionsList.language?.code || "EN",
        country: optionsList.country?.image || "",
        suggestAi: bool || false,
        Volume: bool ? "21k" : "",
      }));
    } else {
      setError("The Search Engine field is required.");
      return;
    }
    setValue((prev) => [...prev, ...newItems].slice(0, MAX_KEYWORDS));
    setAddInputValue("");
    setError("");
  };

  const handlePaste = async (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (value.length === maxList) {
      setError(`Maximum limit of ${MAX_KEYWORDS} keywords reached.`);
      return;
    }
    const pasted = e.clipboardData.getData("text");
    const words = pasted
      .split(/\s+/)
      .map((w) => w.trim())
      .filter((w) => w.length > 0);

    let newItems: ValueKeywordType[] = [];
    if (isValidateUrl) {
      try {
        await schema.validate({ username: words.map((i) => i) });
      } catch (err: any) {
        setError("Enter a valid domain like example.com");
        return;
      }
    }
    const uniqueWords = words.filter(
      (w) => !value.some((v) => v.name.toLowerCase() === w.toLowerCase())
    );
    if (uniqueWords.length === 0) {
      setError(`All pasted keywords are already in the list.`);
      return;
    }
    if (isHaveSelector) {
      // When selectors are available, use the selected values from dataState
      newItems = uniqueWords.map((word) => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
        name: word,
        searchEngine: dataState.searchEngines?.length
          ? dataState.searchEngines[0].image
          : "",
        location: optionsList?.location?.name || "",
        language: optionsList?.language?.code || "EN",
        country: dataState?.country?.image || "",
        suggestAi: false,
      }));
    } else if (optionsList?.searchEngines?.length) {
      // When no selectors, use fixed values from optionsList
      newItems = optionsList.searchEngines.flatMap((engine) =>
        uniqueWords.map((word) => ({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
          name: word,
          searchEngine: engine.image,
          location: optionsList.location?.name || "",
          language: optionsList.language?.code || "EN",
          country: optionsList.country?.image || "",
          suggestAi: false,
        }))
      );
    } else {
      setError("The Search Engine field is required.");
      return;
    }
    setValue((prev) => [...prev, ...newItems].slice(0, MAX_KEYWORDS));
    setError("");
  };

  const handleDelete = (id: string) => {
    setError(``);
    setValue((prev) => {
      const newValue = prev.filter((item) => item.id !== id);
      const totalItemsAfterDelete = newValue.length;
      const totalPages = Math.ceil(totalItemsAfterDelete / ITEMS_PER_PAGE);
      if (currentPage > totalPages) {
        setCurrentPage(totalPages || 1);
      }
      return newValue;
    });
  };

  const handleFileImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    try {
      if (fileExtension === "xlsx" || fileExtension === "xls") {
        // Handle Excel files
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            const buffer = e.target?.result as ArrayBuffer;
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.load(buffer);

            const worksheet = workbook.worksheets[0];
            if (!worksheet) {
              setError("No worksheet found in the Excel file");
              return;
            }

            const importedItems: string[] = [];
            const headerRow = worksheet.getRow(1);
            let nameColumnIndex = -1;

            headerRow.eachCell((cell, colNumber) => {
              if (
                cell.value &&
                cell.value.toString().toLowerCase() === "name"
              ) {
                nameColumnIndex = colNumber;
              }
            });

            if (nameColumnIndex > 0) {
              worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) {
                  const cell = row.getCell(nameColumnIndex);
                  const value = cell.value;
                  if (
                    value &&
                    typeof value === "string" &&
                    value.trim().length > 0
                  ) {
                    importedItems.push(value.trim());
                  }
                }
              });
            } else {
              worksheet.eachRow((row) => {
                const cell = row.getCell(1);
                const value = cell.value;
                if (
                  value &&
                  typeof value === "string" &&
                  value.trim().length > 0
                ) {
                  importedItems.push(value.trim());
                }
              });
            }

            addItemsToTable(importedItems);
          } catch (err) {
            setError(
              "Error processing Excel file. Please check the file format."
            );
            console.error(err);
          }
        };

        reader.onerror = () => {
          setError("Failed to read Excel file. Please try again.");
        };

        reader.readAsArrayBuffer(file);
      } else {
        // Handle text and CSV files
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          if (!content) return;

          let importedItems: string[] = [];

          if (fileExtension === "csv") {
            importedItems = content
              .split("\n")
              .map((line) => {
                const firstColumn = line.split(",")[0];
                return firstColumn ? firstColumn.trim().replace(/"/g, "") : "";
              })
              .filter((line) => line.length > 0);
          } else {
            importedItems = content
              .split("\n")
              .map((line) => line.trim())
              .filter((line) => line.length > 0);
          }

          addItemsToTable(importedItems);
        };

        reader.onerror = () => {
          setError("Failed to read file. Please try again.");
        };

        reader.readAsText(file);
      }
    } catch (err) {
      setError("Error processing file. Please try again.");
      console.error(err);
    }

    event.target.value = "";
  };

  const addItemsToTable = (importedItems: string[]) => {
    importedItems.forEach((item) => {
      if (item.trim()) {
        handleAdd(item.trim(), false);
      }
    });
  };

  useEffect(() => {
    if (
      keywordsName?.length &&
      optionsList?.searchEngines?.length &&
      autoFill
    ) {
      keywordsName.forEach((value) => {
        handleAdd(value, true);
      });
    }
  }, [keywordsName]);

  useEffect(() => {
    if (optionsList?.searchEngines?.length) {
      setError("");
    }
  }, [optionsList]);

  useEffect(() => {
    if (setData) {
      const names = value.map((v) => v.name);
      setData(names);
    }
  }, [value]);

  return (
    <>
      <div className={"p-4 rounded-lg  " + classPlusBox}>
        <div className="flex items-center gap-4 mb-5">
          <button
            type="button"
            onClick={handleFileImport}
            title="Import from file (supports .txt, .csv, .xlsx, .xls)"
            className="flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary/85 rounded-md  hover:border-primary/50 transition-colors"
          >
            <HiPaperClip size={18} className="text-white" />
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept=".txt,.csv,.xlsx,.xls"
            onChange={handleFileChange}
            className="hidden"
          />
          <div className="relative flex-1 bg-white rounded-lg">
            <input
              ref={inputRef}
              type="text"
              value={addInputValue}
              onChange={(e) => setAddInputValue(e.target.value)}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAdd();
                }
                if (
                  e.key === "Backspace" &&
                  addInputValue === "" &&
                  value.length &&
                  addInputValue.length
                ) {
                  e.preventDefault();
                  const last = value[value.length - 1].name;
                  setAddInputValue(last);
                  setValue((prev) => prev.slice(0, -1));
                }
              }}
              onPaste={handlePaste}
              className="text-sm focus:ring-primary focus:border-primary p-3 w-full rounded-md transition-colors"
              placeholder={placeHolder || "Type or paste keywords..."}
            />
            <button
              type="submit"
              onClick={() => handleAdd()}
              disabled={!addInputValue.trim()}
              className="absolute z-20 focus:bg-primary/80 hover:bg-primary/80 right-3 w-8 h-8 flex items-center justify-center rounded-full bg-primary transform cursor-pointer text-white -translate-y-1/2 top-1/2 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              <FaPlus size={14} />
            </button>
            <span className="absolute -bottom-[21px] text-[12px] left-0 text-primary-red">
              {err}
            </span>
          </div>
          {isHaveSelector ? (
            <div className="flex gap-3 flex-shrink-0">
              <div className="w-14">
                <SelectSearchEngine
                  soloChoose
                  noNameSelect
                  classPlusButton="!min-w-0 w-14 h-12 px-3 py-2 justify-center border border-gray-200 hover:border-gray-300 transition-colors duration-200 rounded-lg shadow-sm"
                  setValue={(items) =>
                    setDataState((prev) => ({
                      ...prev,
                      searchEngines: items,
                    }))
                  }
                  value={dataState.searchEngines || []}
                />
              </div>
              <div className="w-14">
                <AutoCompleteCountry
                  size="Small"
                  noNameSelect
                  data={countriesData}
                  valueKey="code"
                  labelKey="name"
                  classPlusButton="!min-w-0 w-14 h-12 px-3 py-2 justify-center bg-white border border-gray-200 hover:border-gray-300 transition-colors duration-200 rounded-lg shadow-sm"
                  imageKey="image"
                  value={dataState?.country?.code || ""}
                  setValue={(item) =>
                    setDataState((prev) => ({ ...prev, country: item }))
                  }
                />
              </div>
            </div>
          ) : null}
          <div className="flex items-center gap-2">
            <Switch checked={autoFill} onCheckedChange={setAutoFill} />
            <span className="text-sm font-medium text-[#344054]">Suggest</span>
          </div>
        </div>
        {/* Keyword Box */}
        <div className="flex flex-col gap-1 mt-3 p-1 h-[330px] overflow-y-auto mb-2">
          <AnimatePresence mode="popLayout">
            {getCurrentPageItems().map((word, index) => (
              <motion.div
                key={word.id}
                initial={{ opacity: 0, scale: 0.95, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className={`bg-white shadow rounded-md py-2 px-3 flex justify-between items-center`}
              >
                <div className="flex gap-3 items-center">
                  <span>{(currentPage - 1) * ITEMS_PER_PAGE + index + 1}.</span>
                  <span className="mr-5 min-w-36">{word.name}</span>
                  {word.searchEngine && (
                    <figure className="py-1 px-2 bg-gray-200 rounded-full">
                      <Image
                        src={word.searchEngine}
                        alt="search engine"
                        width={20}
                        height={20}
                      />
                    </figure>
                  )}
                  {word.country && (
                    <figure className="py-1 px-2 bg-gray-200 rounded-full">
                      <Image
                        src={word.country}
                        alt="country"
                        width={20}
                        height={20}
                        className="rounded-sm"
                      />
                    </figure>
                  )}
                  {word.suggestAi && (
                    <span className="text-gray-400 text-xs">
                      ( Suggestion AI )
                    </span>
                  )}
                </div>
                <div className="flex gap-2 ml-2">
                  {word.Volume && (
                    <span className="text-sm text-gray-500 mr-3">
                      {word.Volume}
                    </span>
                  )}
                  <button
                    type="button"
                    title="Delete keyword"
                    onClick={() => handleDelete(word.id)}
                  >
                    <MdClose size={16} />
                  </button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
        {/* Pagination Box */}
        <div className="grid grid-cols-3 items-center">
          <div className="text-left">
            <span className="text-sm text-purple-800 font-medium bg-purple-100 px-3 py-1.5 rounded-lg">
              {value.length} of {MAX_KEYWORDS}
            </span>
          </div>
          <PaginationTable
            currentPage={currentPage || 1}
            limitPage={6}
            setCurrentPage={setCurrentPage}
            total={value.length}
          />
          <div className="text-right flex justify-end">
            {/* Import button moved to before input field */}
          </div>
        </div>
      </div>
    </>
  );
}
