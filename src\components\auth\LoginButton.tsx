"use client";
import { useAuth } from "@/providers/AuthProvider";
import UserProfileDropdown from "./UserProfileDropdown";
import { motion } from "framer-motion";
import Link from "next/link";

interface LoginButtonProps {
  className?: string;
  buttonText?: string;
}

export default function LoginButton({
  buttonText = "Login / Signup",
}: LoginButtonProps) {
  const { isAuthenticated, hasTokens, isHydrated } = useAuth();

  // Show profile dropdown only if we have tokens AND are hydrated
  // This ensures we don't show profile dropdown during SSR or before token check
  if (isHydrated && (isAuthenticated || hasTokens)) {
    return <UserProfileDropdown />;
  }

  // Show login button by default (optimistic UI)
  // This covers: not hydrated yet, no tokens, or not authenticated
  return (
    <Link href="/login">
      <motion.button
        className="bg-secondary/5 px-6 py-2.5 text-primary rounded-lg tracking-wide border font-bold border-primary relative overflow-hidden login-btn"
        whileHover={{
          scale: 1.005,
          backgroundColor: "var(--color-primary)",
          color: "white",
          boxShadow: "0 2px 6px rgba(145, 74, 196, 0.2)",
          transition: {
            duration: 0.2,
            ease: [0.25, 0.1, 0.25, 1.0],
          },
        }}
        whileTap={{
          scale: 0.98,
          boxShadow: "0 2px 6px rgba(145, 74, 196, 0.2)",
        }}
        animate={{
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.4,
            ease: "easeOut",
          },
        }} // Add a subtle pulse animation when the button appears
        variants={{
          pulse: {
            scale: [1, 1.02, 1],
            transition: {
              duration: 1.2,
              ease: "easeInOut",
              times: [0, 0.5, 1],
              repeat: 0,
              repeatDelay: 0,
            },
          },
        }}
        // Trigger the pulse animation after the initial animation completes
        onAnimationComplete={(definition) => {
          if (definition === "animate") {
            const button = document.querySelector(".login-btn");
            if (button) {
              button.classList.add("animate-pulse");
            }
          }
        }}
      >
        <motion.span
          className="relative z-20"
          initial={{ opacity: 1 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          {buttonText}
        </motion.span>

        {/* Background animation element */}
        <motion.div
          className="absolute inset-0 bg-primary rounded-lg"
          initial={{ opacity: 0, scale: 0 }}
          whileHover={{
            opacity: 1,
            scale: 1,
            transition: { duration: 0.3 },
          }}
          style={{ originX: 0.5, originY: 0.5 }}
        />
      </motion.button>
    </Link>
  );
}
