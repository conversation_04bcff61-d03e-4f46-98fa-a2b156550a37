import { create } from "zustand";
import { DateRange } from "react-day-picker";

// Helper function to get default date range (last 30 days)
const getDefaultDateRange = (): DateRange => {
  const today = new Date();
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(today.getDate() - 30);

  return {
    from: thirtyDaysAgo,
    to: today,
  };
};

interface DateRangeState {
  // Primary date range
  selectedRange: DateRange | undefined;
  setSelectedRange: (range: DateRange | undefined) => void;

  // Comparison date range (for the second calendar)
  comparisonRange: DateRange | undefined;
  setComparisonRange: (range: DateRange | undefined) => void;

  // Comparison mode enabled/disabled
  isComparisonEnabled: boolean;
  setComparisonEnabled: (enabled: boolean) => void;

  // Helper to get formatted dates for API calls
  getFormattedDates: () => {
    startDate: string | null;
    endDate: string | null;
    comparisonStartDate: string | null;
    comparisonEndDate: string | null;
  };

  // Reset all ranges
  resetRanges: () => void;
}

export const useDateRangeStore = create<DateRangeState>((set, get) => ({
  selectedRange: getDefaultDateRange(),
  setSelectedRange: (range) => set({ selectedRange: range }),

  comparisonRange: undefined,
  setComparisonRange: (range) => set({ comparisonRange: range }),

  isComparisonEnabled: false,
  setComparisonEnabled: (enabled) => set({ isComparisonEnabled: enabled }),

  getFormattedDates: () => {
    const state = get();

    const formatDate = (date: Date | undefined): string | null => {
      if (!date) return null;
      return date.toISOString().split("T")[0]; // YYYY-MM-DD format
    };

    return {
      startDate: formatDate(state.selectedRange?.from),
      endDate: formatDate(state.selectedRange?.to),
      comparisonStartDate: formatDate(state.comparisonRange?.from),
      comparisonEndDate: formatDate(state.comparisonRange?.to),
    };
  },

  resetRanges: () =>
    set({
      selectedRange: undefined,
      comparisonRange: undefined,
      isComparisonEnabled: false,
    }),
}));
