interface DateHolderProps {
  date: Date | number;
}

const DateHolder: React.FC<DateHolderProps> = ({ date }) => {
  // Convert Unix timestamp to Date object if needed
  const dateObj = typeof date === "number" ? new Date(date * 1000) : date;

  return (
    <div className="px-2 lg:px-4 py-2 w-fit bg-white text-secondary rounded-lg text-[10px] lg:text-[12px]">
      {dateObj.toLocaleString("default", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      })}
    </div>
  );
};

export default DateHolder;
