import React from "react";

interface StripeIconProps {
  className?: string;
}

const StripeIcon: React.FC<StripeIconProps> = ({ className = "w-5 h-5 text-white" }) => {
  return (
    <svg
      className={className}
      viewBox="0 0 14 20"
      // We don't need fill="none" here as we're setting the fill on the path
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.29203 5.68C5.29203 4.86306 5.96121 4.54832 7.07047 4.54832C8.88099 4.58636 10.6578 5.04563 12.2605 5.88984V0.961612C10.6097 0.310501 8.8489 -0.015795 7.07466 0.000587659C2.82623 0.000587659 0 2.22199 0 5.9318C0 11.7161 7.95341 10.7943 7.95341 13.2871C7.95341 14.2509 7.11518 14.5656 5.94445 14.5656C4.20792 14.5656 1.988 13.8536 0.230512 12.887V17.8754C2.03466 18.6571 3.97865 19.0635 5.94445 19.07C10.2977 19.07 13.2901 16.9115 13.2901 13.1598C13.2664 6.91661 5.29203 8.02731 5.29203 5.68Z"
        fill="currentColor" // Use currentColor to inherit the text color
      />
    </svg>
  );
};

export default StripeIcon;

