"use client";
import React, { useState } from "react";
import FeedbackCard from "./resultsBox/FeedbackCard";

type Props = {
  title: string;
  description: string;
  btnLink?: string;
  feedback?: {
    title: string;
    description: string;
  };
};
export default function ResultText({
  description,
  title,
  btnLink,
  feedback = {
    title: "Your Page could be better",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae congue mauris rhoncus aenean vel elit scelerisque purus viverra accumsan in nisl nisi Arcu cursus vitae purus viverra ",
  },
}: Props) {
  const [showDetails, setShowDetails] = useState(false);
  return (
    <div className="w-full flex flex-col p-4 rounded-lg border border-light-gray">
      <div className="w-full flex flex-col gap-2">
        <h5 className="text-secondary font-semibold">{title}</h5>
        <p className="text-sm text-secondary/60">{description}</p>
      </div>
      <div className="overflow-hidden">
        {btnLink && (
          <div
            className={`${
              showDetails ? "max-h-screen mt-4" : "max-h-0"
            } transition-all duration-400 ease-in-out`}
          >
            <FeedbackCard
              title={feedback.title}
              link={btnLink}
              description={feedback.description}
            />
          </div>
        )}
      </div>
      {btnLink && (
        <div className="flex justify-end mt-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="btn btn--outline-light"
          >
            Show More Details
          </button>
        </div>
      )}
    </div>
  );
}
