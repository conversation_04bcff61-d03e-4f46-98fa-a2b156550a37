/**
 * @jest-environment jsdom
 */

import React from "react";
import { render } from "@testing-library/react";
import { ToastProvider } from "@/lib/ToastProvider";
import { Toaster } from "react-hot-toast";

// Mock the useEffect hook
jest.mock("react", () => {
  const originalReact = jest.requireActual("react");
  return {
    ...originalReact,
    useEffect: jest.fn((callback) => callback()),
  };
});

// Mock react-hot-toast components
jest.mock("react-hot-toast", () => ({
  Toaster: jest.fn(() => (
    <div data-testid="mock-toaster">Toaster Component</div>
  )),
  toast: {
    dismiss: jest.fn(),
  },
}));

// Mock the CloseButton component
jest.mock("../lib/CloseButton", () => ({
  CloseButton: ({ toastId }: { toastId: string }) => (
    <button data-testid="mock-close-button" data-toast-id={toastId}>
      Close
    </button>
  ),
}));

describe("ToastProvider", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the Toaster component", () => {
    const { getByTestId } = render(<ToastProvider />);
    expect(getByTestId("mock-toaster")).toBeInTheDocument();
  });

  it("configures Toaster with correct options", () => {
    render(<ToastProvider />);

    expect(Toaster).toHaveBeenCalledWith(
      expect.objectContaining({
        position: "top-center",
        toastOptions: expect.objectContaining({
          duration: 4000,
          success: expect.any(Function),
          error: expect.any(Function),
          loading: expect.any(Function),
        }),
      }),
      expect.anything()
    );
  });
});
