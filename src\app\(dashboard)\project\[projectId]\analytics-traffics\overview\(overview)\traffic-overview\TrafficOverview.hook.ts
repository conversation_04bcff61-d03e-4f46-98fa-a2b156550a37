import AXIOS from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import type { BarsData } from "../../../types/HorizontalBars.types";
import { useProjectId } from "@/hooks/useProjectId";

const useTrafficOverview = (trafficOverviewTitle: string) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["bar-chart-data", projectId, trafficOverviewTitle],
    queryFn: async (): Promise<BarsData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/overview/traffic-overview",
        {
          params: {
            projectId,
            trafficOverview: trafficOverviewTitle,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default useTrafficOverview;
