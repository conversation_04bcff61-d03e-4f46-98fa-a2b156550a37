import { TableDataRequest } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { NextResponse } from "next/server";

const GSC_TABLE_DATA: TableDataRequest = {
  tableData: {
    tableHeadings: [
      "WEB PAGES",
      "CLICKS",
      "IMPRESSION",
      "CTR",
      "AVG POSITION",
      "BOUNCE RATE",
      "EXIT RATE",
    ],

    tableBody: [
      [
        { value: "URL" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "URL" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "URL" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "URL" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
    ],
  },
  pagination: { totalPages: 20, initialPage: 1 },
};

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function GET() {
  await delay(1000);
  return NextResponse.json(GSC_TABLE_DATA);
}
