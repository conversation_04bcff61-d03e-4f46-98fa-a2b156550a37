export { default as SearchEngineConfigurationStep } from "./SearchEngineConfigurationStep";
export { default as KeywordsInputStep } from "./KeywordsInputStep";
export { default as KeywordsList } from "./KeywordsList";
export { useFileImport } from "./useFileImport";

// Types
export interface KeywordTableRow {
  id: string;
  keyword: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
  volume: string;
  numericVolume?: number; // Numeric value for sorting
  configId: string;
  isSuggested?: boolean; // Optional flag to indicate if keyword was AI-suggested
}

export interface SearchEngineConfig {
  id: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
}
