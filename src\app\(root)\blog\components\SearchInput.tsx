"use client";

import { SearchIcon } from "@/ui/icons/action";
import { useRouter, usePathname } from "next/navigation";
import { useState, useEffect, useRef } from "react";

interface SearchInputProps {
  defaultValue?: string;
}

const SearchInput = ({ defaultValue = "" }: SearchInputProps) => {
  const [query, setQuery] = useState(defaultValue);
  const router = useRouter();
  const pathname = usePathname();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
  };

  // Auto-fetch when query changes
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set a new timeout to navigate after 500ms of inactivity
    timeoutRef.current = setTimeout(() => {
      // If query is empty, navigate to base blog URL to show all posts
      if (query.trim() === "") {
        // Only navigate if we're currently on a search page
        if (defaultValue) {
          // If we're on a category page, preserve the category path
          if (pathname.includes("/blog/") && pathname !== "/blog") {
            // Extract the category from the pathname
            const pathParts = pathname.split("/");
            if (pathParts.length >= 3) {
              router.push(`/blog/${pathParts[2]}`);
              return;
            }
          }
          router.push("/blog");
        }
      }
      // Otherwise, if query has content and is different from defaultValue
      else if (query !== defaultValue) {
        const params = new URLSearchParams();
        params.set("q", query);

        // Always redirect search to the main blog page
        router.push(`/blog?${params.toString()}`);
      }
    }, 500);

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [query, router, pathname, defaultValue]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // If query is empty, navigate to base blog URL to show all posts
    if (query.trim() === "") {
      // Only navigate if we're currently on a search page
      if (defaultValue) {
        // If we're on a category page, preserve the category path
        if (pathname.includes("/blog/") && pathname !== "/blog") {
          // Extract the category from the pathname
          const pathParts = pathname.split("/");
          if (pathParts.length >= 3) {
            router.push(`/blog/${pathParts[2]}`);
            return;
          }
        }
        router.push("/blog");
      }
    } else {
      // If query has content, always navigate to main blog page with search query
      const params = new URLSearchParams();
      params.set("q", query);
      router.push(`/blog?${params.toString()}`);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="bg-[#eae3f0] py-2 px-4 rounded-lg flex gap-2 items-center">
        <input
          type="text"
          className="w-full focus-visible:outline-0 placeholder:text-light-gray-3 active:outline-0 bg-transparent"
          placeholder="Search"
          value={query}
          onChange={handleInputChange}
        />
        <button
          type="submit"
          className="bg-primary text-white p-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center"
          aria-label="Search"
        >
          <SearchIcon className="text-white w-5 h-5" />
        </button>
      </div>
    </form>
  );
};

export default SearchInput;
