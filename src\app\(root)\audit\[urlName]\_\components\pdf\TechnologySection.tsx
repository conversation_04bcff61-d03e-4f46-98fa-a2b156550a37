"use client";
import React from "react";
import { TechSEOAnalysis } from "@/types/seoAnalyzerTypes";
import { SectionHeader, DataRow, RecommendationCard } from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";
import { TechnologyIcon } from "./TechnologyIcon";

export interface TechnologySectionProps {
  technologyData: TechSEOAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
  onTechnologyIconLoad?: (index: number, success: boolean) => void;
}

export const TechnologySection: React.FC<TechnologySectionProps> = ({
  technologyData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
  onTechnologyIconLoad,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "seoanalyser.com.au"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoTech")}
        onLogoError={() => onImageError?.("sectionLogoTech")}
        sectionId="technology-details"
      />

      <SectionHeader
        title="Technology Review Audit"
        brandName={brand_name}
        brandWebsite={brand_website}
        scoreGrade={technologyData.total_score}
        showScore={!!technologyData.total_score}
      />

      {/* Technologies Detected */}
      {technologyData.technologies &&
        technologyData.technologies.technologies && (
          <div className="mb-8">
            <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h3 className="font-bold text-gray-800 text-lg">
                  Technologies Detected
                </h3>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {technologyData.technologies.technologies
                  .slice(0, 12)
                  .map((tech, index) => (
                    <div
                      key={index}
                      className="flex flex-col items-center p-4 border border-gray-200/60 rounded-xl bg-gradient-to-b from-gray-50/50 to-white shadow-sm hover:shadow-md hover:border-primary/30 transition-all duration-200"
                    >
                      <div className="w-10 h-10 mb-3 flex items-center justify-center">
                        <TechnologyIcon
                          name={tech.name}
                          index={index}
                          onImageLoad={onTechnologyIconLoad}
                        />
                      </div>
                      <span
                        className="text-xs text-center text-gray-700 font-semibold truncate w-full leading-tight"
                        title={tech.name}
                      >
                        {tech.name}
                      </span>
                    </div>
                  ))}
              </div>

              {technologyData.technologies.technologies.length > 12 && (
                <div className="mt-4 p-3 bg-primary/5 rounded-lg border border-primary/20">
                  <p className="text-sm text-primary font-medium text-center">
                    ... and{" "}
                    {technologyData.technologies.technologies.length - 12} more
                    technologies detected
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Web Server Analysis */}
        {technologyData.web_server && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Web Server Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Server"
                value={technologyData.web_server.server || "Unknown"}
              />
              <DataRow
                label="Status"
                value={
                  technologyData.web_server.pass ? "✓ Good" : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${technologyData.web_server.score || 0}/5`}
              />
            </div>
            {technologyData.web_server.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {technologyData.web_server.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* DNS Analysis */}
        {technologyData.dns_servers && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">DNS Audit</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="DNS Status"
                value={
                  technologyData.dns_servers.pass ? "✓ Good" : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${technologyData.dns_servers.score || 0}/5`}
              />
            </div>
            {technologyData.dns_servers.nameservers &&
              technologyData.dns_servers.nameservers.length > 0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    DNS Servers:
                  </h4>
                  <div className="space-y-1">
                    {technologyData.dns_servers.nameservers
                      .slice(0, 3)
                      .map((server, index) => (
                        <p
                          key={index}
                          className="text-sm text-gray-600 bg-gray-50 p-2 rounded"
                        >
                          {server}
                        </p>
                      ))}
                  </div>
                </div>
              )}
          </div>
        )}

        {/* Robots Meta Analysis */}
        {technologyData.robots_meta && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Robots Meta Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Robots Status"
                value={
                  !technologyData.robots_meta.noindex
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${technologyData.robots_meta.score || 0}/5`}
              />
            </div>
            {technologyData.robots_meta.description && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-700 mb-2">Description:</h4>
                <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  {technologyData.robots_meta.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* SSL Analysis */}
        {technologyData.ssl_enabled !== undefined && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                SSL Certificate Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="SSL Status"
                value={
                  technologyData.ssl_enabled ? "✓ Enabled" : "⚠ Not Enabled"
                }
              />
            </div>
          </div>
        )}

        {/* Charset Analysis */}
        {technologyData.charset && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Character Set Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Charset"
                value={technologyData.charset.charset || "Unknown"}
              />
              <DataRow
                label="Source"
                value={technologyData.charset.source || "Unknown"}
              />
              <DataRow
                label="Standard"
                value={
                  technologyData.charset.is_standard
                    ? "✓ Yes"
                    : "⚠ Non-standard"
                }
              />
              <DataRow
                label="Score"
                value={`${technologyData.charset.score || 0}/5`}
              />
            </div>
          </div>
        )}

        {/* Server IP Analysis */}
        {technologyData.server_ip && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Server IP Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Primary IP"
                value={
                  technologyData.server_ip.ip ||
                  technologyData.server_ip.ip_address ||
                  "Unknown"
                }
              />
              <DataRow
                label="Location"
                value={technologyData.server_ip.location || "Unknown"}
              />
              <DataRow
                label="ISP"
                value={technologyData.server_ip.isp || "Unknown"}
              />
              <DataRow
                label="Score"
                value={`${technologyData.server_ip.score || 0}/5`}
              />
            </div>

            {/* All Server IPs - matching main audit component */}
            {technologyData.server_ip.all_ips &&
              technologyData.server_ip.all_ips.length > 0 && (
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <h5 className="font-medium text-gray-700 mb-2">
                    All Server IPs:
                  </h5>
                  <div className="grid grid-cols-1 gap-1">
                    {technologyData.server_ip.all_ips.map((ip, index) => (
                      <div
                        key={index}
                        className="p-2 bg-gray-50 rounded text-sm font-mono text-gray-700"
                      >
                        {ip}
                      </div>
                    ))}
                  </div>
                  {technologyData.server_ip.all_ips.length > 5 && (
                    <p className="text-xs text-gray-500 mt-2">
                      Showing{" "}
                      {Math.min(5, technologyData.server_ip.all_ips.length)} of{" "}
                      {technologyData.server_ip.all_ips.length} IPs
                    </p>
                  )}
                </div>
              )}
          </div>
        )}
      </div>

      {/* Technology Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          Technology Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* SSL Recommendations */}
          {technologyData.ssl_enabled !== undefined &&
            !technologyData.ssl_enabled && (
              <RecommendationCard
                recommendation={{
                  text: "Enable SSL certificate to secure your website and improve SEO rankings. HTTPS is a ranking factor and builds user trust.",
                  priority: "High",
                }}
              />
            )}

          {/* DNS Recommendations */}
          {technologyData.dns_servers?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.dns_servers.recommendation}
            />
          )}

          {/* Web Server Recommendations */}
          {technologyData.web_server?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.web_server.recommendation}
            />
          )}

          {/* Robots Meta Recommendations */}
          {technologyData.robots_meta?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.robots_meta.recommendation}
            />
          )}

          {/* Server IP Recommendations */}
          {technologyData.server_ip?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.server_ip.recommendation}
            />
          )}

          {/* Technologies Recommendations */}
          {technologyData.technologies?.recommendations &&
            technologyData.technologies.recommendations.length > 0 &&
            technologyData.technologies.recommendations.map((rec, index) => (
              <RecommendationCard key={index} recommendation={rec} />
            ))}

          {/* General Technology Recommendation if no specific ones */}
          {!technologyData.ssl_enabled &&
            !technologyData.dns_servers?.recommendation &&
            !technologyData.web_server?.recommendation &&
            !technologyData.robots_meta?.recommendation &&
            !technologyData.server_ip?.recommendation &&
            (!technologyData.technologies?.recommendations ||
              technologyData.technologies.recommendations.length === 0) && (
              <RecommendationCard
                recommendation={{
                  text: "Your website's technology stack appears to be well configured. Continue monitoring for security updates and performance optimizations.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
