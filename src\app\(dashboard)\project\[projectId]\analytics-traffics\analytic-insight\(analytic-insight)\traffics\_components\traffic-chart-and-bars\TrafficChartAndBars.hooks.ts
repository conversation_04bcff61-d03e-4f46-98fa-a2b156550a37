import AXIOS from "@/lib/axios";
import type { ChartsAndBars } from "../../../types/ChartAndBars.types";
import { useGoogleAnalyticsAPI } from "@/hooks/useAnalyticsAPI";

const useTrafficChartAndBars = ({
  tab,
  domesticFilter,
}: {
  tab: string;
  domesticFilter: string;
}) => {
  return useGoogleAnalyticsAPI<ChartsAndBars>(
    ["charts-and-bars", tab, domesticFilter],
    async () => {
      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/traffics/traffics-chart-and-bars-section",
        {
          params: {
            tab,
            domesticFilter,
          },
        }
      );
      return data;
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
    }
  );
};

export default useTrafficChartAndBars;
