"use client";
import * as React from "react";
import { IoIosArrowDown } from "react-icons/io";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import Image from "next/image";
import { BsExclamationCircle } from "react-icons/bs";
import TooltipPortal from "../ui/TooltipPortal";
import { Checkbox } from "../shared/CheckBox";
import { AutoComplete } from "../shared/AutoComplete";

type AutoCompleteType<T = any> = {
  placeHoldre?: string;
  title?: string;
  guideText?: string;
  data: T[];
  valueKey: keyof T;
  labelKey: keyof T;
  imageKey?: keyof T;
  value: string;
  setValue: (val: T) => void;
  classPlusButton?: string;
  classNameBox?: string;
  size?: "Small" | "Medium";
  isLanguage?: boolean;
  valueLang?: string;
  classDiv?: string;
  setValueLang?: (item: T | null) => void;
  noNameSelect?: boolean;
};

function AutoCompleteCountryComponent<T extends Record<string, any>>({
  placeHoldre,
  title,
  guideText,
  data,
  valueKey,
  labelKey,
  imageKey,
  value,
  setValue,
  classPlusButton,
  classNameBox,
  isLanguage,
  valueLang,
  setValueLang,
  classDiv,
  size = "Medium",
  noNameSelect,
}: AutoCompleteType<T>) {
  const [open, setOpen] = React.useState(false);
  const [googleLanguage, setGoogleLanguage] = React.useState<any[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [debouncedQuery, setDebouncedQuery] = React.useState("");
  const [displayCount, setDisplayCount] = React.useState(50);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);

  // Memoize selected item to prevent unnecessary recalculations
  const selectedItem = React.useMemo(() => {
    return data.find((item) => item[valueKey] === value);
  }, [value, data, valueKey]);

  // Debounce search query for better performance
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 200);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Memoize filtered data to improve search performance with infinite scroll
  const filteredData = React.useMemo(() => {
    if (!debouncedQuery.trim()) {
      // Show items based on displayCount for infinite scroll
      return data.slice(0, displayCount);
    }

    const query = debouncedQuery.toLowerCase();
    const filtered = data.filter((item) =>
      String(item[labelKey]).toLowerCase().includes(query)
    );

    // For search results, show more items (up to 100)
    return filtered.slice(0, Math.min(100, filtered.length));
  }, [data, debouncedQuery, labelKey, displayCount]);

  // Reset display count when search query changes
  React.useEffect(() => {
    if (debouncedQuery.trim()) {
      setDisplayCount(50); // Reset to initial count when searching
    }
  }, [debouncedQuery]);

  // Load more items function
  const loadMoreItems = React.useCallback(() => {
    if (isLoadingMore || debouncedQuery.trim()) return;

    setIsLoadingMore(true);
    // Simulate loading delay for better UX
    setTimeout(() => {
      setDisplayCount((prev) => Math.min(prev + 50, data.length));
      setIsLoadingMore(false);
    }, 200);
  }, [isLoadingMore, debouncedQuery, data.length]);

  // Handle scroll to load more items
  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const isNearBottom = scrollHeight - scrollTop <= clientHeight + 50;

      if (
        isNearBottom &&
        !isLoadingMore &&
        !debouncedQuery.trim() &&
        displayCount < data.length
      ) {
        loadMoreItems();
      }
    },
    [loadMoreItems, isLoadingMore, debouncedQuery, displayCount, data.length]
  );

  // Optimize setValue handler
  const handleSelect = React.useCallback(
    (item: T) => {
      setValue(item);
      setOpen(false); // This will trigger handleOpenChange which resets search
    },
    [setValue]
  );

  // Optimize search handler
  const handleSearch = React.useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Reset search when popover opens
  const handleOpenChange = React.useCallback((isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      setSearchQuery("");
      setDebouncedQuery("");
      setDisplayCount(50); // Reset display count when closing
    }
  }, []);
  React.useEffect(() => {
    if (isLanguage) {
      import("@/data/googleLanguage").then((mod) =>
        setGoogleLanguage(mod.default || mod)
      );
    }
  }, []);
  return (
    <div className={`w-full ${classNameBox}`}>
      {/* Title + Tooltip */}
      {title ? (
        <div className="flex mb-2 items-center gap-2 text-gray-600">
          <span className="text-sm lg:text-base">{title}</span>
          {guideText && (
            <TooltipPortal width="xl" content={<span>{guideText}</span>}>
              <BsExclamationCircle />
            </TooltipPortal>
          )}
        </div>
      ) : null}

      <div
        className={
          "flex w-full bg-white rounded-lg transition-colors duration-200 overflow-hidden " +
          classDiv
        }
      >
        {/* Popover اصلی برای انتخاب کشور */}
        <Popover open={open} onOpenChange={handleOpenChange}>
          <PopoverTrigger asChild>
            <div className="flex h-[45px] w-full">
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className={`w-full focus:!border rounded-none shadow-none h-full justify-between border-none ${
                  classPlusButton?.includes("bg-white")
                    ? "bg-white"
                    : "bg-gray-100"
                } placeholder:text-[var(--color-border-input)] ${
                  size === "Small" || noNameSelect
                    ? "px-3 py-2 justify-center"
                    : "px-4 py-3"
                } ${classPlusButton}`}
              >
                {selectedItem ? (
                  <div
                    className={`flex items-center ${
                      noNameSelect ? "justify-center" : "gap-2"
                    }`}
                  >
                    {imageKey && (
                      <img
                        src={selectedItem[imageKey]}
                        alt="flag"
                        width={size === "Small" || noNameSelect ? 28 : 24}
                        height={size === "Small" || noNameSelect ? 21 : 18}
                        className="flex-shrink-0 rounded-sm object-cover"
                        loading="lazy"
                      />
                    )}
                    {size === "Small" || noNameSelect ? null : (
                      <span className="cutline cutline-1 truncate">
                        {selectedItem[labelKey]}
                      </span>
                    )}
                  </div>
                ) : (
                  placeHoldre || "Select..."
                )}
                {size !== "Small" && !noNameSelect && (
                  <IoIosArrowDown className="opacity-50 ml-2" />
                )}
              </Button>
            </div>
          </PopoverTrigger>
          <PopoverContent
            align="start"
            sideOffset={4}
            className="p-0 min-w-64 max-h-[300px]"
            style={{ width: "var(--radix-popover-trigger-width)" }}
          >
            <Command className="p-1" shouldFilter={false}>
              <CommandInput
                placeholder="Search countries..."
                className="h-9"
                value={searchQuery}
                onValueChange={handleSearch}
              />
              <CommandList
                className="max-h-[250px] overflow-auto"
                onScroll={handleScroll}
              >
                <CommandEmpty>
                  {searchQuery
                    ? "No countries found."
                    : "Start typing to search..."}
                </CommandEmpty>
                <CommandGroup>
                  {filteredData.map((item) => (
                    <CommandItem
                      key={String(item[valueKey])}
                      value={String(item[valueKey])}
                      className="w-full flex items-center justify-start cursor-pointer hover:bg-gray-100 transition-colors duration-100"
                      onSelect={() => handleSelect(item)}
                    >
                      <div className="mr-2">
                        <Checkbox checked={value === item[valueKey]} />
                      </div>
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        {imageKey && (
                          <img
                            src={item[imageKey]}
                            alt={String(item[labelKey])}
                            width={20}
                            height={20}
                            className="flex-shrink-0 rounded-sm"
                            loading="lazy"
                          />
                        )}
                        <span className="truncate">{item[labelKey]}</span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
                {/* Loading indicator */}
                {isLoadingMore && (
                  <div className="p-3 text-xs text-gray-500 text-center border-t">
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                      Loading more...
                    </div>
                  </div>
                )}
                {/* Show progress indicator */}
                {!searchQuery && !isLoadingMore && (
                  <div className="p-2 text-xs text-gray-500 text-center border-t">
                    {displayCount < data.length ? (
                      <>
                        Showing {displayCount} of {data.length} countries.
                        Scroll down for more.
                      </>
                    ) : (
                      <>Showing all {data.length} countries.</>
                    )}
                  </div>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {isLanguage && valueLang && setValueLang && (
          <div className="w-28 border-l-2 border-[var(--color-border-input)] rounded-r-lg overflow-hidden">
            <AutoComplete
              classValue="py-2 px-3"
              classPlusButton="!bg-gray-100 border-none shadow-none rounded-none"
              valueKey="name"
              value={valueLang}
              dataSelector={googleLanguage}
              setValue={setValueLang}
              placeHoldre="Language"
              option={(item) => <div>{item?.code}</div>}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export const AutoCompleteCountry = React.memo(AutoCompleteCountryComponent) as <
  T extends Record<string, any>
>(
  props: AutoCompleteType<T>
) => JSX.Element;
