## [NAME] API

**Endpoint:**

`Get /api/dashboard/project/analytics-traffics/analytic-insight/about-users/chart-and-bars-section`

---

## Query Parameters

| Name          | Type     | Required | Description                     |

| ------------- | -------- | -------- | ------------------------------- |

| `[paramName]` | `[type]` | Yes/No   | [What this param actually does] |

---

## Acceptable Values for `[paramName]`

_(Case-sensitive! `Value` ≠ `value`)_

- `[Value 1]`

- `[Value 2]`

- `[Value 3]`

- ...

---

## Response Schema

```ts

{

  [key1]: [type],

  [key2]: [

    {

      [nestedKey1]: [type],

      [nestedKey2]: [

        { value: [type], color: [type] },

        { value: [type], color: [type] }

      ]

    }

  ]

}

```

```json
{
  "[key1]": 123,

  "[key2]": [
    {
      "[nestedKey1]": "Example Label",

      "[nestedKey2]": [
        { "value": 1000, "color": "bg-primary" },

        { "value": 2000, "color": "bg-primary-orange" }
      ]
    }
  ]
}
```

## Important Notes

- Colors are Tailwind utility class names used for bar styling.

- Colors can be `bg-primary` , `bg-primary-orange` or `bg-[#HEXCODE]`

- The number of bars and barData entries may vary depending on the query.
