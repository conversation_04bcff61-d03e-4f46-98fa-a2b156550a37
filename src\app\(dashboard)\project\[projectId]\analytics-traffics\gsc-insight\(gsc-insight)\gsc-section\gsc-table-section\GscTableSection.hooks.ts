import AXIOS from "@/lib/axios";
import { TableDataRequest } from "../../../../_components/data-table/DataTable.types";
import { useGoogleSearchConsoleAPI } from "@/hooks/useAnalyticsAPI";

const useGscTable = ({
  page,
  filterBy,
}: {
  page: number;
  filterBy: string;
}) => {
  return useGoogleSearchConsoleAPI<TableDataRequest>(
    ["gsc-table-data", page, filterBy],
    async () => {
      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/gsc-insight/gsc-section/gsc-table",
        {
          params: {
            page,
            filterBy,
          },
        }
      );
      return data;
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
    }
  );
};

export default useGscTable;
