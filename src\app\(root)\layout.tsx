import type { Metada<PERSON> } from "next";
// import { Nunito_Sans } from "next/font/google";
import "../globals.css";
import Navbar from "@/components/navbar/Navbar";
import Footer from "@/components/footer/Footer";
import nunitoSansFont from "@/constants/localFont";
import Providers from "./Providers";
import CookieConsentModal from "@/components/cookie-consent/CookieConsentModal";
import ConditionalAnalytics from "@/components/analytics/ConditionalAnalytics";

// const nunitoSans = Nunito_Sans({
//   variable: "--font-nunito-sans",
//   subsets: ["latin"],
//   weight: ["200", "300", "400", "500", "600", "700", "800", "900"],
// });

export const metadata: Metadata = {
  title: "SEO Analyser",
  description:
    "Our all-in-one SEO Analyser uncovers hidden issues, delivers actionable insights, and helps you rank higher, faster.",
  other: {
    "Content-Security-Policy":
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://www.google.com https://www.gstatic.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: http:; font-src 'self' data:; connect-src 'self' https://seoanalyser.com.au https://www.google-analytics.com; frame-src 'self' https://www.google.com;",
    "X-Frame-Options": "SAMEORIGIN",
    "X-Content-Type-Options": "nosniff",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1.0,
};
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Conditional Google Analytics - only loads with user consent */}
        <ConditionalAnalytics />
      </head>
      <body
        className={`${nunitoSansFont.variable} font-[family-name:var(--font-nunito-sans)] antialiased relative root-page`}
      >
        <Providers>
          <Navbar />
          {children}
          <Footer />
          <CookieConsentModal />
        </Providers>
      </body>
    </html>
  );
}
