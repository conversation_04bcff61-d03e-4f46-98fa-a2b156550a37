"use client";

import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { useState, useEffect } from "react";
import { SearchIcon } from "@/ui/icons/action/SearchIcon";
import {
  ChartIcon,
  RobotIcon,
  MonitorIcon,
  StarBoldIcon,
  DocumentCheckIcon,
  BookIcon,
} from "@/ui/icons/general";

export default function NotFound() {
  const [isVisible, setIsVisible] = useState(false);
  const [floatingElements, setFloatingElements] = useState<
    Array<{
      id: number;
      x: number;
      y: number;
      icon: React.ReactNode;
      delay: number;
    }>
  >([]);

  // Initialize floating elements
  useEffect(() => {
    setIsVisible(true);

    const icons = [
      <ChartIcon key="chart" className="w-6 h-6 text-primary/60" />,
      <RobotIcon key="robot" className="w-6 h-6 text-primary/60" />,
      <MonitorIcon key="monitor" className="w-6 h-6 text-primary/60" />,
      <StarBoldIcon key="star" className="w-6 h-6 text-primary/60" />,
      <DocumentCheckIcon key="doc" className="w-6 h-6 text-primary/60" />,
      <BookIcon key="book" className="w-6 h-6 text-primary/60" />,
    ];

    const elements = Array.from({ length: 8 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      icon: icons[i % icons.length],
      delay: Math.random() * 2,
    }));

    setFloatingElements(elements);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1.0],
      },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [-5, 5, -5],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  const searchIconVariants = {
    initial: { scale: 1, rotate: 0 },
    animate: {
      scale: [1, 1.1, 1],
      rotate: [0, 10, -10, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
    hover: {
      scale: 1.2,
      rotate: 15,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  const numberVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      color: "var(--color-primary)",
      transition: {
        duration: 0.2,
      },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 relative overflow-hidden">
      {/* Floating Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {floatingElements.map((element) => (
          <motion.div
            key={element.id}
            className="absolute"
            style={{
              left: `${element.x}%`,
              top: `${element.y}%`,
            }}
            variants={floatingVariants}
            animate="animate"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: element.delay }}
          >
            {element.icon}
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
        >
          {/* 404 Numbers with Search Icon */}
          <motion.div
            className="flex items-center justify-center gap-4 mb-8"
            variants={itemVariants}
          >
            <motion.span
              className="text-8xl sm:text-9xl lg:text-[12rem] font-bold text-secondary/20 select-none"
              variants={numberVariants}
              whileHover="hover"
            >
              4
            </motion.span>

            <motion.div
              className="relative"
              variants={searchIconVariants}
              initial="initial"
              animate="animate"
              whileHover="hover"
            >
              <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-primary/10 rounded-full flex items-center justify-center border-4 border-primary/20">
                <SearchIcon className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 text-primary" />
              </div>

              {/* Animated search ripples */}
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-primary/30"
                animate={{
                  scale: [1, 1.5, 2],
                  opacity: [0.5, 0.2, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeOut",
                }}
              />
            </motion.div>

            <motion.span
              className="text-8xl sm:text-9xl lg:text-[12rem] font-bold text-secondary/20 select-none"
              variants={numberVariants}
              whileHover="hover"
            >
              4
            </motion.span>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            className="text-4xl sm:text-5xl lg:text-6xl font-bold text-secondary mb-6"
            variants={itemVariants}
          >
            <motion.span
              className="inline-block"
              animate={{
                color: [
                  "var(--color-secondary)",
                  "var(--color-primary)",
                  "var(--color-secondary)",
                ],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              Page Not Found
            </motion.span>
          </motion.h1>

          {/* Subtitle with typing effect */}
          <motion.div
            className="text-lg sm:text-xl text-secondary/70 mb-8 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.8 }}
            >
              Oops! Our SEO crawler couldn't find this page.
              <br />
              <motion.span
                className="text-primary font-semibold"
                animate={{
                  opacity: [0.7, 1, 0.7],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                But don't worry, we'll help you get back on track!
              </motion.span>
            </motion.p>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            variants={itemVariants}
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/"
                className="btn btn--primary px-8 py-4 text-lg font-bold shadow-lg"
              >
                <motion.span
                  animate={{
                    x: [0, 2, 0],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  🏠 Back to Home
                </motion.span>
              </Link>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/pricing"
                className="btn btn--outline px-8 py-4 text-lg font-bold shadow-lg"
              >
                <motion.span
                  animate={{
                    x: [0, -2, 0],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                >
                  🚀 Explore Plans
                </motion.span>
              </Link>
            </motion.div>
          </motion.div>

          {/* Fun SEO Tips */}
          <motion.div
            className="mt-12 p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-primary/20 max-w-2xl mx-auto"
            variants={itemVariants}
            whileHover={{
              scale: 1.02,
              boxShadow: "0 10px 30px rgba(145, 74, 196, 0.1)",
            }}
            transition={{ duration: 0.3 }}
          >
            <motion.h3
              className="text-xl font-bold text-primary mb-4"
              animate={{
                scale: [1, 1.02, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              💡 SEO Tip While You're Here
            </motion.h3>
            <motion.p
              className="text-secondary/80"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2, duration: 0.8 }}
            >
              404 errors can hurt your SEO! Make sure to set up proper redirects
              and create a helpful 404 page (like this one) to keep users
              engaged and search engines happy.
            </motion.p>
          </motion.div>
        </motion.div>
      </div>

      {/* Animated Background Gradient */}
      <motion.div
        className="absolute inset-0 opacity-30 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 20% 50%, rgba(145, 74, 196, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 50%, rgba(145, 74, 196, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 50%, rgba(145, 74, 196, 0.1) 0%, transparent 50%)",
          ],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  );
}
