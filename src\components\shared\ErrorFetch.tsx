import React from 'react'

export default function ErrorFetch({ text, nameError, isError, children }: { nameError?: string, text?: string, isError: boolean, children: React.ReactNode }) {
    return (isError ?
        <div className='px-4 py-2 mb-4 bg-primary-red/10 border border-primary-red/20 rounded-md'>
            {text || `Failed to load ${nameError||''} information. Please try again later.`}
        </div>
        : children
    )
}
