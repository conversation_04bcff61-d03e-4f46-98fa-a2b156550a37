/**
 * Utility functions for handling country flag images
 * Ensures consistent flag URL generation across the application
 */

/**
 * Generate the correct CDN URL for a country flag
 * @param countryCode - The ISO country code (e.g., "AU", "US", "GB")
 * @returns The complete CDN URL for the flag image
 */
export function getFlagImageUrl(countryCode: string): string {
  // Ensure country code is uppercase for consistency
  const code = countryCode.toUpperCase();
  return `https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/${code}.svg`;
}

/**
 * Create a country object with the correct flag URL
 * @param name - Country name
 * @param code - ISO country code
 * @returns Country object with correct flag URL
 */
export function createCountryObject(name: string, code: string) {
  return {
    name,
    code: code.toUpperCase(),
    image: getFlagImageUrl(code),
  };
}

/**
 * Default country objects with correct flag URLs
 */
export const defaultCountries = {
  australia: createCountryObject("Australia", "AU"),
  unitedStates: createCountryObject("United States", "US"),
  unitedKingdom: createCountryObject("United Kingdom", "GB"),
  canada: createCountryObject("Canada", "CA"),
  germany: createCountryObject("Germany", "DE"),
  france: createCountryObject("France", "FR"),
  italy: createCountryObject("Italy", "IT"),
  spain: createCountryObject("Spain", "ES"),
  japan: createCountryObject("Japan", "JP"),
  china: createCountryObject("China", "CN"),
  india: createCountryObject("India", "IN"),
  brazil: createCountryObject("Brazil", "BR"),
  russia: createCountryObject("Russia", "RU"),
};

/**
 * Validate and fix flag URLs in country data
 * @param countries - Array of country objects
 * @returns Array of country objects with corrected flag URLs
 */
export function validateAndFixFlagUrls(
  countries: Array<{ name: string; code: string; image?: string }>
) {
  return countries.map((country) => ({
    ...country,
    image: country.image?.includes("cdn.jsdelivr.net")
      ? country.image
      : getFlagImageUrl(country.code),
  }));
}

/**
 * Check if a flag URL is using the correct CDN format
 * @param url - The flag image URL to check
 * @returns True if using correct CDN format, false otherwise
 */
export function isValidFlagUrl(url: string): boolean {
  return (
    url.includes(
      "https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/"
    ) && url.endsWith(".svg")
  );
}

/**
 * Fix any incorrect flag URLs in location data
 * @param locations - Array of location objects with potential flag URLs
 * @returns Array with corrected flag URLs
 */
export function fixLocationFlagUrls(
  locations: Array<{ code: string; [key: string]: any }>
) {
  return locations.map((location) => ({
    ...location,
    image: getFlagImageUrl(location.code),
  }));
}

/**
 * Generate the correct URL for a search engine image
 * @param searchEngineName - The search engine name (e.g., "Google", "Bing")
 * @returns The complete URL for the search engine image
 */
export function getSearchEngineImageUrl(searchEngineName: string): string {
  return `/images/create-project/${searchEngineName.toLowerCase()}.svg`;
}

/**
 * Create a search engine object with the correct image URL
 * @param name - Search engine name
 * @returns Search engine object with correct image URL
 */
export function createSearchEngineObject(name: string) {
  return {
    name,
    image: getSearchEngineImageUrl(name),
  };
}

/**
 * Default search engine objects with correct image URLs
 */
export const defaultSearchEngines = {
  google: createSearchEngineObject("Google"),
  bing: createSearchEngineObject("Bing"),
  yahoo: createSearchEngineObject("Yahoo"),
  duckduckgo: createSearchEngineObject("DuckDuckGo"),
  chatgpt: { name: "ChatGPT", image: "/images/create-project/openai.svg" }, // Special case for ChatGPT/OpenAI
};
