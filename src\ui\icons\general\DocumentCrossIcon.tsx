import { SVGProps } from "react";

export function DocumentCrossIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="64"
      height="65"
      viewBox="0 0 64 65"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M25.3333 31.1777L38.6667 44.5111M38.6667 31.1777L25.3333 44.5111M34.8571 7.25578V18.7333C34.8571 21.6788 37.2449 24.0666 40.1904 24.0666H51.5985M34.8571 7.25578C33.9486 7.20307 32.9968 7.17773 32 7.17773C16.7059 7.17773 12 13.1385 12 32.5111C12 51.8836 16.7059 57.8444 32 57.8444C47.2941 57.8444 52 51.8836 52 32.5111C52 29.3499 51.8747 26.5459 51.5985 24.0666M34.8571 7.25578L51.5985 24.0666"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
