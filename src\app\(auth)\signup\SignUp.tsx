"use client";

import { useEffect, useState } from "react";
import EmailInput from "../components/input/EmailInput";
import PassInput from "../components/input/PassInput";
import FirstNameInput from "../components/input/FirstNameInput";
import LastNameInput from "../components/input/LastNameInput";
import KeepLoggedCheck from "../components/input/KeepLoggedCheck";
import TermsCheck from "../components/input/TermsCheck";
import Link from "next/link";
import GoogleLogin from "../components/GoogleLogin";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import { OtpVerificationForm } from "@/components/auth";
import BoxForm from "../components/BoxForm";
import FaqsSettingProfile from "../components/FaqsSettingProfile";
import PasswordStrengthIndicator from "@/components/auth/PasswordStrengthIndicator";
import { useAuthStore } from "@/store/authStore";
import { showToast } from "@/lib/toast";

export default function SignUpPage() {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [keepLoggedIn, setKeepLoggedIn] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [step, setStep] = useState("signup");
  const register = useAuthStore((state) => state.register);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);
  const clearError = useAuthStore((state) => state.clearErrors);

  // Password strength validation function
  const getPasswordStrength = (
    password: string
  ): {
    score: number;
    requirements: {
      minLength: boolean;
      hasUppercase: boolean;
      hasLowercase: boolean;
      hasNumber: boolean;
      hasSpecialChar: boolean;
    };
  } => {
    const requirements = {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    };

    const score = Object.values(requirements).filter(Boolean).length;

    return { score, requirements };
  };

  const validatePassword = (password: string): boolean => {
    const { requirements } = getPasswordStrength(password);
    return Object.values(requirements).every(Boolean);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      // Form validation
      if (!firstName.trim()) {
        showToast.error("Please enter your first name");
        return;
      }

      if (!lastName.trim()) {
        showToast.error("Please enter your last name");
        return;
      }

      if (!email.trim()) {
        showToast.error("Please enter your email address");
        return;
      }

      if (!password) {
        showToast.error("Please enter a password");
        return;
      }

      if (!confirmPassword) {
        showToast.error("Please confirm your password");
        return;
      }

      if (!termsAccepted) {
        showToast.error(
          "You must accept the Terms of Service and Privacy Policy"
        );
        return;
      }

      // Enhanced password validation
      if (!validatePassword(password)) {
        const { requirements } = getPasswordStrength(password);
        const missingRequirements = [];

        if (!requirements.minLength) {
          missingRequirements.push("at least 8 characters");
        }
        if (!requirements.hasUppercase) {
          missingRequirements.push("one uppercase letter");
        }
        if (!requirements.hasLowercase) {
          missingRequirements.push("one lowercase letter");
        }
        if (!requirements.hasNumber) {
          missingRequirements.push("one number");
        }
        if (!requirements.hasSpecialChar) {
          missingRequirements.push("one special character");
        }

        showToast.error(
          `Password must contain: ${missingRequirements.join(", ")}`
        );
        return;
      }

      if (password !== confirmPassword) {
        showToast.error("Passwords do not match");
        return;
      }

      const body = {
        email: email,
        password: password,
        confirm_password: confirmPassword,
        first_name: firstName,
        last_name: lastName,
      };

      const response = await register(body);

      // Check if registration was successful
      if (response.success === true) {
        showToast.success("Account created successfully!");
        setStep("otp");
        clearError();
      }
    } catch (err: any) {
      console.error("Registration error:", err);
      showToast.error(
        err?.message || "Failed to create account. Please try again."
      );
    }
  };

  useEffect(() => {
    clearError();
  }, [clearError]);

  // Display error as toast when it changes
  useEffect(() => {
    if (error) {
      showToast.error(error);
    }
  }, [error]);

  // Handle successful email verification
  const handleVerificationSuccess = () => {
    showToast.success("Email verified successfully!");
    setStep("verify");
  };

  switch (step) {
    case "signup":
      return (
        <BoxForm title="Create Account">
          <div className="mb-1">
            <p className="text-gray-600 text-sm">
              Sign up to get started with our SEO tools
            </p>
          </div>

          <form
            onSubmit={handleSubmit}
            autoComplete="off"
            method="post"
            className="flex flex-col gap-4 w-full"
            action="javascript:void(0);"
            data-form-type="signup"
          >
            {/* First Name and Last Name in two-column grid */}
            <div className="grid grid-cols-2 gap-4">
              <FirstNameInput
                onChange={setFirstName}
                placeholder="Enter your first name"
              />
              <LastNameInput
                onChange={setLastName}
                placeholder="Enter your last name"
              />
            </div>

            <EmailInput
              onChange={setEmail}
              placeholder="Enter your email address"
            />

            <PassInput onChange={setPassword} placeholder="Create a password" />

            <PassInput
              confirmPassword
              onChange={setConfirmPassword}
              placeholder="Confirm your password"
            />

            {/* Password Strength Indicator */}
            {password && (
              <PasswordStrengthIndicator
                password={password}
                confirmPassword={confirmPassword}
                requirements={getPasswordStrength(password).requirements}
                score={getPasswordStrength(password).score}
              />
            )}

            <div className="flex flex-col gap-3">
              <KeepLoggedCheck
                keepLoggedIn={keepLoggedIn}
                onChange={setKeepLoggedIn}
              />

              <TermsCheck
                accepted={termsAccepted}
                onChange={setTermsAccepted}
              />
            </div>

            <ButtenSubmit
              text="Create Account"
              textloading="Creating account..."
              isLoading={isLoading}
              type="submit"
            />

            <div className="w-full flex items-center gap-4 my-1">
              <span className="w-full h-[1px] bg-gray-200"></span>
              <span className="text-gray-500 text-sm whitespace-nowrap">
                or
              </span>
              <span className="w-full h-[1px] bg-gray-200"></span>
            </div>

            <GoogleLogin />

            <div className="text-center text-sm text-gray-600">
              Already have an account?{" "}
              <Link
                href="/login"
                className="text-primary font-medium hover:underline"
              >
                Sign In
              </Link>
            </div>
          </form>
        </BoxForm>
      );

    case "otp":
      return (
        <BoxForm title="Verify Your Email">
          <OtpVerificationForm
            email={email}
            onSuccess={handleVerificationSuccess}
            onResendCode={() => {
              showToast.info("Verification code resent to your email");
            }}
          />
        </BoxForm>
      );

    case "verify":
      return (
        <BoxForm title="Email Verified">
          <div className="flex flex-col gap-4">
            <div className="p-4 rounded-lg bg-green-50 border border-green-100">
              <h2 className="text-green-600 font-medium mb-2">
                Verification Successful
              </h2>
              <p className="text-sm text-green-700">
                Your email has been successfully verified. You can now continue
                using your account without any restrictions.
              </p>
            </div>
            <ButtenSubmit
              text="Continue to Setup"
              onClick={() => {
                setStep("faqs");
                showToast.info("Let's complete your profile setup");
              }}
            />
          </div>
        </BoxForm>
      );

    case "faqs":
      return <FaqsSettingProfile />;

    default:
      return <div>Something went wrong</div>;
  }
}
