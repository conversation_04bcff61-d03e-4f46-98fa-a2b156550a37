type Props = {
  message: string;
  open: boolean;
  className?: string;
};
export default function TooltipBottom({ message, open, className }: Props) {
  return (
    <div
      className={`${
        open ? "opacity-100 visible" : "opacity-0 invisible"
      } ${className} p-4 rounded-lg top-[calc(100%+16px)] shadow-md bg-white text-primary-red text-sm font-semibold absolute duration-200 before:absolute before:rounded-sm before:-top-1.5 before:left-6 before:w-4 before:h-4 before:rotate-45 before:bg-white`}
    >
      {message}
    </div>
  );
}
