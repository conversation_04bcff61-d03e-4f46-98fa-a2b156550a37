"use client";
import React, { useEffect, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import profileService from "@/services/profileService";
import dashbordService from "@/services/dashboardServices";
import { ProfileHeader, ProfileModal } from "./components";

type FormProfileType = {
  first_name: string;
  last_name: string;
  phone_number: string;
};

const profileSchema = yup.object().shape({
  first_name: yup
    .string()
    .required("First name is required")
    .matches(/^[a-zA-Z\s\-']+$/, "Invalid characters in first name")
    .min(2)
    .max(50),
  last_name: yup
    .string()
    .required("Last name is required")
    .matches(/^[a-zA-Z\s\-']+$/, "Invalid characters in last name")
    .min(2)
    .max(50),
  phone_number: yup
    .string()
    .required("Phone number is required")
    .matches(/^(\+?\d{1,3})?\d{9,15}$/, "Invalid phone number format"),
});

export default function Profile() {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FormProfileType>({
    resolver: yupResolver(profileSchema),
  });

  const [open, setOpen] = useState<boolean>(false);
  const [choice, setChoice] = useState<Record<number, string[]>>({});
  const [selectAvatar, setSelectAvatar] = useState<string>("");
  const [errText, setErrText] = useState<string[]>([]);
  const queryClient = useQueryClient();

  // Queries
  const {
    data: dataUserProfile,
    isFetching: fetchUser,
    isError: isErrorProfile,
  } = useQuery({
    queryKey: ["Profile"],
    queryFn: profileService.getUserProfile,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });

  const {
    data: profileSetting,
    isFetching: fetchProfile,
    isError: isErrorSetting,
  } = useQuery({
    queryKey: ["ProfileSetting"],
    queryFn: profileService.getProfileSetting,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
    enabled: !!open,
  });

  const {
    data: dataAvatars,
    isError: isErrorAvatar,
    isFetching: fetchAvatar,
  } = useQuery({
    queryKey: ["GetAvatars"],
    queryFn: dashbordService.getPreset,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
    enabled: !!open,
  });

  // Helper function
  const extractInvalidChoice = (message: string): string => {
    const match = message.match(/"\[\s*'([^']+)'/);
    return match ? `${match[1]} is not a valid choice.` : message;
  };

  // Mutation
  const { mutate: handleUpdateProfile, isPending } = useMutation({
    mutationFn: (form: FormProfileType) => {
      const body = {
        business_type: choice[0] || "",
        company_size: choice[1] || "",
        user_role: choice[2] || "",
        offers_seo_services: choice[3] || "",
        help_areas: choice[4] || [],
        interested_features: choice[5] || [],
        avatar_id: selectAvatar,
        ...form,
      };
      return dashbordService.updateProfile(body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["Profile"] });
      queryClient.invalidateQueries({ queryKey: ["ProfileSetting"] });
      setOpen(false);
    },
    onError: (err: any) => {
      console.log(err);

      if (err.response?.data?.detail) {
        setErrText([err.response.data.detail]);
      } else {
        const finalMessages: string[] = [];
        for (const key in err.response?.data) {
          const messages: string[] = err.response?.data[key];
          messages.forEach((msg) => {
            finalMessages.push(extractInvalidChoice(msg));
          });
        }
        setErrText(finalMessages);
      }
    },
  });

  // Effects
  useEffect(() => {
    if (profileSetting) {
      const body = [
        profileSetting.business_type,
        profileSetting.company_size,
        profileSetting.user_role,
        profileSetting.offers_seo_services,
        profileSetting.help_areas,
        profileSetting.interested_features,
      ] as Record<number, string[]>;
      setSelectAvatar(profileSetting.current_avatar_id);
      setValue("first_name", profileSetting.first_name);
      setValue("last_name", profileSetting.last_name);
      setValue("phone_number", profileSetting.phone_number);
      setChoice(body);
    }
  }, [profileSetting, setValue]);

  return (
    <>
      <ProfileHeader
        dataUserProfile={dataUserProfile}
        fetchUser={fetchUser}
        isErrorProfile={isErrorProfile}
        onEditClick={() => setOpen((prev) => !prev)}
      />

      <ProfileModal
        open={open}
        onClose={() => setOpen((prev) => !prev)}
        fetchProfile={fetchProfile}
        fetchAvatar={fetchAvatar}
        isErrorAvatar={isErrorAvatar}
        isErrorSetting={isErrorSetting}
        dataAvatars={dataAvatars}
        dataUserProfile={dataUserProfile}
        selectAvatar={selectAvatar}
        setSelectAvatar={setSelectAvatar}
        register={register}
        errors={errors}
        choice={choice}
        setChoice={setChoice}
        errText={errText}
        handleSubmit={handleSubmit}
        handleUpdateProfile={handleUpdateProfile}
        isPending={isPending}
      />
    </>
  );
}
