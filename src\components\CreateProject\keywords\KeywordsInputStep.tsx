"use client";

import React from "react";
import { AnimatedElement } from "@/components/CreateProject/PageTransition";
import { FaPlus } from "react-icons/fa6";
import { HiPaperClip } from "react-icons/hi2";
import { Switch } from "@/components/ui/switch";

interface KeywordsInputStepProps {
  keywordInput: string;
  setKeywordInput: (value: string) => void;
  autoFill: boolean;
  setAutoFill: (value: boolean) => void;
  selectedConfigIds: string[];
  onAddKeyword: () => void;
  onFileImport: () => void;
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function KeywordsInputStep({
  keywordInput,
  setKeywordInput,
  autoFill,
  setAutoFill,
  selectedConfigIds,
  onAddKeyword,
  onFileImport,
  fileInputRef,
  onFileChange,
}: KeywordsInputStepProps) {
  return (
    <>
      <div className="flex items-center justify-between gap-4 mb-5 w-full">
        <button
          type="button"
          onClick={onFileImport}
          title="Import keywords from file (supports .txt, .csv, .xlsx, .xls)"
          className="flex items-center justify-center w-10 h-10 bg-primary  rounded-md hover:bg-primary/85 hover:border-primary/50 transition-colors"
        >
          <HiPaperClip size={19} className="text-white" />
        </button>
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt,.csv,.xlsx,.xls"
          onChange={onFileChange}
          className="hidden"
        />
        <div className="relative flex-1 bg-white rounded-lg">
          <input
            type="text"
            value={keywordInput}
            onChange={(e) => setKeywordInput(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && onAddKeyword()}
            placeholder="Enter a keyword..."
            className="text-sm focus:ring-primary focus:border-primary p-3 w-full rounded-md transition-colors"
          />
          <button
            type="button"
            onClick={onAddKeyword}
            disabled={!keywordInput.trim() || selectedConfigIds.length === 0}
            className="absolute z-20 focus:bg-primary/80 hover:bg-primary/80 right-3 w-8 h-8 flex items-center justify-center rounded-full bg-primary transform cursor-pointer text-white -translate-y-1/2 top-1/2 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            <FaPlus size={14} />
          </button>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Switch checked={autoFill} onCheckedChange={setAutoFill} />
          <span className="text-sm font-medium text-[#344054]">Suggest</span>
        </div>
      </div>
    </>
  );
}
