"use client";
import React from "react";

/* ================================== STYLE ================================= */
import "./RangePickerCalendar.css";

/* ============================ REACT DAY PICKER ============================ */
import { DayPicker } from "react-day-picker";

/* ================================== ICONS ================================= */
import { IoMdArrowDropleft } from "react-icons/io";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* =============================== COMPONENTS =============================== */
import Dropdown from "@/components/ui/Dropdown";

/* ================================== TYPES ================================= */
import type { RangePickerCalendarProps } from "./RangePickerCalendar.types";
import { LuInfo } from "react-icons/lu";

/* ================================== MAIN ================================== */
const RangePickerCalendar = ({
  title,
  monthDropdown,
  yearDropdown,
  className,
  classNames,
  month,
  setMonth,
  selected,
  setSelected,
  disabled,
  selectedColor,
}: RangePickerCalendarProps) => {
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div
      className={cn(
        "rounded-lg p-3 w-fit space-y-2 flex flex-col items-end relative overflow-hidden h-full min-w-[320px]",
        className
      )}
    >
      <div
        className={cn(
          "flex items-center justify-between w-[280px]",
          classNames?.header
        )}
      >
        <button
          onClick={() =>
            setMonth(
              (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1)
            )
          }
        >
          <IoMdArrowDropleft className="text-xl bg-gray-200 rounded-sm text-secondary p-0.5" />
        </button>

        {/* ============================= MONTH DROPDOWN ============================= */}
        {monthDropdown && (
          <Dropdown className="bg-white w-[115px]">
            <Dropdown.Button
              className={cn(
                "w-full rounded-t-md bg-white",
                classNames?.dropdownButton
              )}
            >
              {monthDropdown.options[month.getMonth()]}
            </Dropdown.Button>
            <Dropdown.Options className="h-32 overflow-y-auto overflow-x-hidden bg-white shadow-md">
              {monthDropdown.options.map((option, index) => (
                <Dropdown.Option
                  onClick={() => {
                    const monthIndex = monthDropdown.options.indexOf(
                      option as string
                    );
                    if (monthIndex !== -1) {
                      setMonth(new Date(month.getFullYear(), monthIndex, 1));
                    }
                  }}
                  key={index}
                  className={cn(classNames?.dropdownOption)}
                >
                  {option}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        )}
        {/* ============================= YEARS DROPDOWN ============================= */}
        {yearDropdown && (
          <Dropdown className="bg-white w-[120px]">
            <Dropdown.Button
              className={cn(
                "w-full rounded-t-md bg-white",
                classNames?.dropdownButton
              )}
            >
              {month.getFullYear()}
            </Dropdown.Button>
            <Dropdown.Options className="h-32 overflow-y-auto overflow-x-hidden bg-white shadow-md">
              {yearDropdown.options.map((option, index) => (
                <Dropdown.Option
                  key={index}
                  onClick={() => {
                    const selectedYear = option as number;
                    setMonth(new Date(selectedYear, month.getMonth(), 1));
                  }}
                  className={cn(classNames?.dropdownOption)}
                >
                  {option}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        )}
        <button
          onClick={() =>
            setMonth(
              (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1)
            )
          }
        >
          <IoMdArrowDropleft className="text-xl bg-gray-200 rounded-sm text-secondary p-0.5 scale-[-1]" />
        </button>
      </div>

      {/* ================================ CALENDAR ================================ */}

      <DayPicker
        month={month}
        formatters={{
          formatWeekdayName: (day) =>
            day.toLocaleDateString("en-US", { weekday: "short" }),
        }}
        mode="range"
        selected={selected}
        onSelect={setSelected}
        className="text-secondary"
        disabled={disabled}
        classNames={{
          selected: cn(
            "text-white last:rounded-r-full first:rounded-l-full px-1 py-1 text-sm",
            selectedColor
          ),
          range_start: cn(
            "!rounded-l-full text-white px-1 py-1 text-sm",
            selectedColor
          ),
          range_end: cn(
            "!rounded-r-full mb-[2px] text-white px-1 py-1 text-sm",
            selectedColor
          ),
          range_middle: cn("text-white px-1 py-1 text-sm", selectedColor),
          day: "rounded-none border-0 px-1 py-1 text-center text-sm",
          weekday: "text-center text-xs font-medium p-1",
          nav: "hidden",
          dropdown: "",
          month: "rounded-md overflow-hidden",
          month_grid: "border-separate border-spacing-y-2",
          outside: "text-muted-foreground opacity-50",
        }}
        showOutsideDays
        components={{
          CaptionLabel: ({}) => (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 mb-3 text-sm font-semibold text-muted-foreground opacity-80 ">
                {month.toLocaleDateString("en-US", {
                  month: "short",
                  year: "numeric",
                })}
              </div>
              {selected && (selected.from || selected.to) && (
                <div
                  className={cn(
                    `mb-2 px-2 text-xs text-primary font-semibold text-right w-fit`,
                    selectedColor?.replace("bg", "text")
                  )}
                >
                  {selected.from &&
                    selected.to &&
                    `${selected.from.toLocaleDateString("en-US", {
                      month: "short",
                      day: "2-digit",
                      year: "numeric",
                    })} - ${selected.to.toLocaleDateString("en-US", {
                      month: "short",
                      day: "2-digit",
                      year: "numeric",
                    })}`}
                  {selected.from &&
                    !selected.to &&
                    selected.from.toLocaleDateString("en-US", {
                      month: "short",
                      day: "2-digit",
                      year: "numeric",
                    })}
                  {!selected.from &&
                    selected.to &&
                    selected.to.toLocaleDateString("en-US", {
                      month: "short",
                      day: "2-digit",
                      year: "numeric",
                    })}
                </div>
              )}
            </div>
          ),
        }}
      />
      {disabled && (
        <div className="absolute top-0 right-0 w-full h-full bg-white/50 z-40" />
      )}
    </div>
  );
};

/**
 * MultiDayPickerCalendar – A calendar where the user can select up to 7 individual days (not a range).
 * Props:
 *   selected: Date[]
 *   setSelected: (dates: Date[]) => void
 *   ...other props as needed
 */
export const MultiDayPickerCalendar = ({
  title,
  monthDropdown,
  yearDropdown,
  className,
  classNames,
  month,
  setMonth,
  selected,
  setSelected,
  disabled,
  selectedColor,
}: Omit<RangePickerCalendarProps, "selected" | "setSelected"> & {
  selected: Date[];
  setSelected: (dates: Date[]) => void;
}) => {
  // Handler to enforce max 7 days
  const handleSelect = (dates: Date[] | undefined) => {
    if (!dates) return setSelected([]);
    if (dates.length > 7) return;
    setSelected(dates);
  };

  return (
    <div
      className={cn(
        "rounded-lg p-3 w-fit space-y-2 flex flex-col items-end relative overflow-hidden h-full min-w-[320px]",
        className
      )}
    >
      <div
        className={cn(
          "flex items-center justify-between w-[280px]",
          classNames?.header
        )}
      >
        <button
          onClick={() =>
            setMonth(
              (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1)
            )
          }
        >
          <IoMdArrowDropleft className="text-xl bg-gray-200 rounded-sm text-secondary p-0.5" />
        </button>
        {monthDropdown && (
          <Dropdown className="bg-white w-[115px]">
            <Dropdown.Button
              className={cn(
                "w-full rounded-t-md bg-white",
                classNames?.dropdownButton
              )}
            >
              {monthDropdown.options[month.getMonth()]}
            </Dropdown.Button>
            <Dropdown.Options className="h-32 overflow-y-auto overflow-x-hidden bg-white shadow-md">
              {monthDropdown.options.map((option, index) => (
                <Dropdown.Option
                  onClick={() => {
                    const monthIndex = monthDropdown.options.indexOf(
                      option as string
                    );
                    if (monthIndex !== -1) {
                      setMonth(new Date(month.getFullYear(), monthIndex, 1));
                    }
                  }}
                  key={index}
                  className={cn(classNames?.dropdownOption)}
                >
                  {option}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        )}
        {yearDropdown && (
          <Dropdown className="bg-white w-[120px]">
            <Dropdown.Button
              className={cn(
                "w-full rounded-t-md bg-white",
                classNames?.dropdownButton
              )}
            >
              {month.getFullYear()}
            </Dropdown.Button>
            <Dropdown.Options className="h-32 overflow-y-auto overflow-x-hidden bg-white shadow-md">
              {yearDropdown.options.map((option, index) => (
                <Dropdown.Option
                  key={index}
                  onClick={() => {
                    const selectedYear = option as number;
                    setMonth(new Date(selectedYear, month.getMonth(), 1));
                  }}
                  className={cn(classNames?.dropdownOption)}
                >
                  {option}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        )}
        <button
          onClick={() =>
            setMonth(
              (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1)
            )
          }
        >
          <IoMdArrowDropleft className="text-xl bg-gray-200 rounded-sm text-secondary p-0.5 scale-[-1]" />
        </button>
      </div>
      <DayPicker
        month={month}
        formatters={{
          formatWeekdayName: (day) =>
            day.toLocaleDateString("en-US", { weekday: "short" }),
        }}
        mode="multiple"
        selected={selected}
        onSelect={handleSelect}
        className="text-secondary"
        disabled={disabled}
        classNames={{
          selected: cn(
            "text-white rounded-full px-1 py-1 text-sm",
            selectedColor
          ),
          day: "rounded-full border-0 px-1 py-1 text-center text-sm",
          weekday: "text-center text-xs font-medium p-1",
          nav: "hidden",
          dropdown: "",
          month: "rounded-md overflow-hidden",
          month_grid: "border-separate border-spacing-y-2",
          outside: "text-muted-foreground opacity-50",
        }}
        showOutsideDays
        components={{
          CaptionLabel: ({}) => (
            <div className="flex flex-col gap-2 items-start justify-between text-[11px]">
              <span className="text-xs">
                you only can choose between 1 to 7 days
              </span>
              <div className="flex items-center gap-4 text-sm font-semibold text-muted-foreground opacity-80 ">
                {month.toLocaleDateString("en-US", {
                  month: "short",
                  year: "numeric",
                })}
              </div>
            </div>
          ),
        }}
      />
      {disabled && (
        <div className="absolute top-0 right-0 w-full h-full bg-white/50 z-40" />
      )}
    </div>
  );
};
export default RangePickerCalendar;
