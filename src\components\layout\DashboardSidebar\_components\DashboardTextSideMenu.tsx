"use client";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { usePathname } from "next/navigation";

/* =============================== COMPONENTS =============================== */
import DropdownSideMenuItem from "./DropdownSideMenuItem";

/* ================================== ICONS ================================= */
import { IoChevronDown } from "react-icons/io5";

/* ============================== FRAMER MOTION ============================= */
import { motion, AnimatePresence } from "framer-motion";

/* ================================== TYPES ================================= */
import type { TDashboardTextSideMenuProps } from "../Types";

/* ========================================================================== */
const DashboardTextSideMenu = ({
  title,
  subMenus,
  openMenu,
  onClick,
  themeColor,
  href,
}: TDashboardTextSideMenuProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const pathname = usePathname();

  const activeIndex = subMenus?.findIndex((submenu) =>
    pathname?.startsWith(href)
  );
  const [indexPosition, setIndexPosition] = useState({
    top:
      typeof activeIndex === "number" && activeIndex >= 0
        ? activeIndex * 40
        : 0,
    height: 40,
  });

  const activeItemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (activeItemRef.current)
      setIndexPosition({
        top: activeItemRef.current.offsetTop,
        height: activeItemRef.current.offsetHeight,
      });
  }, [activeIndex]);

  const renderedSubMenus = useMemo(
    () =>
      subMenus?.map((submenu, index) => (
        <DropdownSideMenuItem
          themeColor={themeColor}
          key={submenu.id}
          title={submenu.title}
          href={submenu.href}
          ref={index === activeIndex ? activeItemRef : null}
        />
      )),
    [subMenus, activeIndex, themeColor]
  );
  const isActive = typeof activeIndex === "number" && activeIndex >= 0;
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-[190px]">
      <div
        style={{
          color: isActive ? themeColor : "#344054",
        }}
        onClick={onClick}
        className={`flex justify-between items-center px-3 py-3.5 cursor-pointer`}
      >
        <span className="text-sm">{title}</span>
        {subMenus && (
          <IoChevronDown
            className="transition-all duration-300"
            style={{ rotate: openMenu ? "180deg" : "0deg" }}
          />
        )}
      </div>

      <AnimatePresence initial={false}>
        {openMenu && (
          <motion.div
            key="submenu"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.25, ease: "easeInOut" }}
            className="overflow-hidden relative"
          >
            {renderedSubMenus}

            {typeof activeIndex === "number" &&
              activeIndex >= 0 &&
              indexPosition.height > 0 && (
                <motion.div
                  layout
                  className="w-[180px] h-[40px] rounded-md absolute left-1"
                  initial={false}
                  animate={{
                    top: indexPosition.top,
                    height: indexPosition.height,
                  }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  style={{
                    backgroundColor: themeColor || "#ff00ff",
                    opacity: 0.1,
                  }}
                />
              )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default DashboardTextSideMenu;
