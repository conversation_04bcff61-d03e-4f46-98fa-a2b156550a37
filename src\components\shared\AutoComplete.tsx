"use client";

import * as React from "react";
import { IoIosArrowDown } from "react-icons/io";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { BsExclamationCircle } from "react-icons/bs";
import TooltipPortal from "../ui/TooltipPortal";
import { FaRegSquare, FaRegSquareCheck } from "react-icons/fa6";
import { Checkbox } from "./CheckBox";

type AutoCompleteType<T> = {
  dataSelector: T[];
  valueKey: keyof T;
  title?: string;
  guideText?: string;
  placeHoldre?: string;
  option?: (item: T) => React.ReactNode;
  value: string;
  classPlusButton?: string;
  classNameBox?: string;
  setValue: (item: T | null) => void;
  classValue?: string;
  disabled?: boolean;
  hideDropdownIcon?: boolean;
};

function AutoCompleteComponent<T extends Record<string, any>>({
  dataSelector,
  valueKey,
  title,
  guideText,
  placeHoldre,
  option,
  value,
  setValue,
  classPlusButton,
  classValue,
  classNameBox,
  disabled = false,
  hideDropdownIcon = false,
}: AutoCompleteType<T>) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [debouncedQuery, setDebouncedQuery] = React.useState("");
  const [displayCount, setDisplayCount] = React.useState(30);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);

  // Memoize selected item
  const selectedItem = React.useMemo(
    () => dataSelector.find((item) => item[valueKey] === value) || null,
    [dataSelector, valueKey, value]
  );

  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 150);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Memoize filtered data with infinite scroll support
  const filteredData = React.useMemo(() => {
    if (!debouncedQuery.trim()) {
      return dataSelector.slice(0, displayCount); // Show items based on displayCount
    }

    const query = debouncedQuery.toLowerCase();
    const filtered = dataSelector.filter((item) =>
      String(item[valueKey]).toLowerCase().includes(query)
    );

    return filtered.slice(0, Math.min(50, filtered.length)); // Show more search results
  }, [dataSelector, debouncedQuery, valueKey, displayCount]);

  // Reset display count when search query changes
  React.useEffect(() => {
    if (debouncedQuery.trim()) {
      setDisplayCount(30); // Reset to initial count when searching
    }
  }, [debouncedQuery]);

  // Load more items function
  const loadMoreItems = React.useCallback(() => {
    if (isLoadingMore || debouncedQuery.trim()) return;

    setIsLoadingMore(true);
    // Simulate loading delay for better UX
    setTimeout(() => {
      setDisplayCount((prev) => Math.min(prev + 30, dataSelector.length));
      setIsLoadingMore(false);
    }, 200);
  }, [isLoadingMore, debouncedQuery, dataSelector.length]);

  // Handle scroll to load more items
  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const isNearBottom = scrollHeight - scrollTop <= clientHeight + 50;

      if (
        isNearBottom &&
        !isLoadingMore &&
        !debouncedQuery.trim() &&
        displayCount < dataSelector.length
      ) {
        loadMoreItems();
      }
    },
    [
      loadMoreItems,
      isLoadingMore,
      debouncedQuery,
      displayCount,
      dataSelector.length,
    ]
  );

  // Optimize handlers
  const handleSelect = React.useCallback(
    (item: T) => {
      setValue(item);
      setOpen(false);
    },
    [setValue]
  );

  const handleSearch = React.useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleOpenChange = React.useCallback((isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      setSearchQuery("");
      setDebouncedQuery("");
      setDisplayCount(30); // Reset display count when closing
    }
  }, []);

  return (
    <div className={`w-full ${classNameBox}`}>
      {title ? (
        <div className="flex mb-2  items-center gap-2 text-gray-600">
          <span className="text-sm lg:text-base">{title}</span>
          {guideText ? (
            <TooltipPortal width="xl" content={<span>{guideText}</span>}>
              <BsExclamationCircle />
            </TooltipPortal>
          ) : null}
        </div>
      ) : null}
      <Popover
        open={open && !disabled}
        onOpenChange={disabled ? undefined : handleOpenChange}
      >
        <PopoverTrigger asChild className={disabled ? "" : "hover:!bg-white"}>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open && !disabled}
            disabled={disabled}
            className={`w-full placeholder:text-[var(--color-border-input)] h-[45px] rounded-lg justify-between border-none shadow-none ${
              disabled
                ? "!bg-gray-100 !text-gray-400 cursor-not-allowed"
                : classPlusButton?.includes("bg-white")
                ? "!bg-white"
                : "!bg-gray-100"
            } ${classPlusButton}`}
          >
            <span
              className={`cutline cutline-1 ${
                disabled ? "text-gray-400" : ""
              } ${classValue}`}
            >
              {selectedItem
                ? option
                  ? option(selectedItem)
                  : selectedItem[valueKey]
                : placeHoldre || "Select ..."}
            </span>
            {!hideDropdownIcon && (
              <IoIosArrowDown
                className={`opacity-50 ${disabled ? "text-gray-400" : ""}`}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          sideOffset={4}
          className="p-0 min-w-36"
          style={{ width: "var(--radix-popover-trigger-width)" }}
        >
          <Command className="p-1" shouldFilter={false}>
            <CommandInput
              placeholder="Search..."
              className="h-9"
              value={searchQuery}
              onValueChange={handleSearch}
            />
            <CommandList
              className="max-h-[200px] overflow-auto"
              onScroll={handleScroll}
            >
              <CommandEmpty>
                {searchQuery ? "No items found." : "Start typing to search..."}
              </CommandEmpty>
              <CommandGroup>
                {filteredData.map((item) => (
                  <CommandItem
                    key={item[valueKey]}
                    value={item[valueKey]}
                    onSelect={() => handleSelect(item)}
                    className="w-full flex items-center justify-start cursor-pointer hover:bg-gray-100 transition-colors duration-100"
                  >
                    <div className="mr-2">
                      <Checkbox checked={value === item[valueKey]} />
                    </div>
                    <div className="flex items-center gap-1 w-full min-w-0">
                      <span className="truncate">
                        {option ? option(item) : item[valueKey]}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
              {/* Loading indicator */}
              {isLoadingMore && (
                <div className="p-3 text-xs text-gray-500 text-center border-t">
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    Loading more...
                  </div>
                </div>
              )}
              {/* Show progress indicator */}
              {!searchQuery && !isLoadingMore && (
                <div className="p-2 text-xs text-gray-500 text-center border-t">
                  {displayCount < dataSelector.length ? (
                    <>
                      Showing {displayCount} of {dataSelector.length} items.
                      Scroll down for more.
                    </>
                  ) : (
                    <>Showing all {dataSelector.length} items.</>
                  )}
                </div>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export const AutoComplete = React.memo(AutoCompleteComponent) as <
  T extends Record<string, any>
>(
  props: AutoCompleteType<T>
) => JSX.Element;
