import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    // Only ignore build errors in development for faster iteration
    ignoreBuildErrors: process.env.NODE_ENV === "development",
  },
  images: {
    domains: [
      "seoanalyser.com.au",
      "dev.seoanalyser.com.au",
      "cdn.simpleicons.org",
      "cdn.jsdelivr.net",
    ],
  },
  // CORS headers with device ID support
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Requested-With, Content-Type, Authorization, X-Device-ID",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
