"use client";
import React, { forwardRef, useState } from "react";
import Image from "next/image";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import { Button } from "@/components/ui/button";

/* ================================== ICONS ================================= */
import googleAnalyticsLogo from "@/../public/images/create-project/google-analytics.svg";
import googleLogo from "@/../public/images/create-project/google.svg";
import { FiCalendar } from "react-icons/fi";
import ShareAndSettings from "@/ui/ShareAndSettings";

/* ========================================================================== */
type GoogleConnectedHeaderProps = {
  onDateRangeClick: () => void;
  title: string;
  gaStatus?: any;
  gscStatus?: any;
  isGoogleAnalyticsConnected?: () => boolean;
  isGoogleSearchConsoleConnected?: () => boolean;
  connectGA?: () => void;
  connectGSC?: () => void;
  gaConnecting?: boolean;
  gscConnecting?: boolean;
};

const GoogleConnectedHeader = forwardRef<
  HTMLDivElement,
  GoogleConnectedHeaderProps
>(
  (
    {
      onDateRangeClick,
      title,
      gaStatus,
      gscStatus,
      isGoogleAnalyticsConnected,
      isGoogleSearchConsoleConnected,
      connectGA,
      connectGSC,
      gaConnecting = false,
      gscConnecting = false,
    },
    ref
  ) => {
    /* ========================================================================== */
    /*                                  CONSTANTS                                 */
    /* ========================================================================== */
    // Use the passed connection status functions or fallback to false
    const googleAnalyticsConnected = isGoogleAnalyticsConnected
      ? isGoogleAnalyticsConnected()
      : false;
    const googleConsoleConnected = isGoogleSearchConsoleConnected
      ? isGoogleSearchConsoleConnected()
      : false;

    /* ========================================================================== */
    /*                                   RENDER                                   */
    /* ========================================================================== */
    return (
      <Card className="flex flex-col xl:flex-row gap-2 w-full h-fit justify-between">
        <div className="flex lg:flex-col gap-2 justify-between">
          <div className="flex justify-between">
            <h3 className="font-extrabold text-xl text-secondary">{title}</h3>
          </div>
          <div className="text-xs" ref={ref}>
            <Button
              variant={"secondary"}
              className="bg-gray-200 text-secondary hover:text-white"
              onClick={onDateRangeClick}
            >
              <FiCalendar />

              <span style={{ userSelect: "none" }}>Select Date Range</span>
            </Button>
          </div>
        </div>
        <div className="flex flex-col items-end gap-2">
          <ShareAndSettings />
          <div className="w-full flex flex-col lg:flex-row justify-end gap-4">
            <Button
              variant={"secondary"}
              className={
                googleAnalyticsConnected
                  ? "text-secondary hover:text-white"
                  : "text-white"
              }
              style={
                googleAnalyticsConnected
                  ? { backgroundColor: "#F4F4F4" }
                  : undefined
              }
              onClick={() => {
                if (!googleAnalyticsConnected && connectGA) {
                  connectGA();
                }
              }}
              disabled={gaConnecting || googleAnalyticsConnected}
            >
              <Image src={googleAnalyticsLogo} alt={"Google Analytics Logo"} />
              <span>
                {gaConnecting
                  ? "Connecting..."
                  : googleAnalyticsConnected
                  ? "Connected"
                  : "Connect Google Analytics"}
              </span>
            </Button>
            <Button
              variant={"secondary"}
              className={
                googleConsoleConnected
                  ? "text-secondary hover:text-white"
                  : "text-white"
              }
              style={
                googleConsoleConnected
                  ? { backgroundColor: "#F4F4F4" }
                  : undefined
              }
              onClick={() => {
                if (!googleConsoleConnected && connectGSC) {
                  connectGSC();
                }
              }}
              disabled={gscConnecting || googleConsoleConnected}
            >
              <Image src={googleLogo} alt={"Google Logo"} />
              <span>
                {gscConnecting
                  ? "Connecting..."
                  : googleConsoleConnected
                  ? "Connected"
                  : "Connect Google Search Console"}
              </span>
            </Button>
          </div>
        </div>
      </Card>
    );
  }
);

GoogleConnectedHeader.displayName = "GoogleConnectedHeader";

export default GoogleConnectedHeader;
