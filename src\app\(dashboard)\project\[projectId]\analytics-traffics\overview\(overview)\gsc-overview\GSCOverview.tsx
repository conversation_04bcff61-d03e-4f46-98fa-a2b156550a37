"use client";
import React, { useEffect, useMemo, useState } from "react";

/* ================================ API CALLS =============================== */
import { useGSCOverview } from "./GSCOverview.hook";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import GSCLineChart from "../../../analytic-insight/_components/line-chart/LineChart";
import CheckboxCard from "./_components/CheckboxCard";
import DateRange from "../../../_components/date-range/DateRange";
import { Button } from "@/components/ui/button";
import NoData from "../../../analytic-insight/_components/NoData";
import Skeleton from "react-loading-skeleton";
import LineChartSkeleton from "../../../_components/line-chart-skeleton/LineChartSkeleton";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";

/* ========================================================================== */
const GSCOverview = ({
  dropdownContent,
}: {
  dropdownContent?: React.ReactNode;
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { data: gscOverviewData, isLoading: gscOverviewIsLoading } =
    useGSCOverview();
  const [selectedLines, setSelectedLines] = useState<string[]>([]);

  /* ================================== HOOKS ================================= */
  const coloredKeys = useMemo(() => {
    const colors = ["#FF00C3", "#00BBEC", "#31D37A", "#3C0866", "#F57D37"];

    if (!gscOverviewData) return [];
    const keys = Object.keys(gscOverviewData.lineChartData[0]).filter(
      (k) => k !== "name",
    );
    return keys.map((key, i) => ({
      name: key,
      color: colors[i % colors.length],
    }));
  }, [gscOverviewData]);

  useEffect(() => {
    if (coloredKeys.length > 0) {
      setSelectedLines(coloredKeys.map((item) => item.name));
    }
  }, [coloredKeys]);

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handleToggleCheck = (title: string) => {
    setSelectedLines((prev) =>
      prev.includes(title)
        ? prev.filter((item) => item !== title)
        : [...prev, title],
    );
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (!gscOverviewData && !gscOverviewIsLoading)
    return <NoData title="GSC Overview" />;

  return (
    <Card className="space-y-4">
      <div className="flex justify-between items-center">
        <Title>GSC overview</Title>
        {dropdownContent}
      </div>
      <DateRange />
      {
        <div className="flex overflow-x-auto justify-between gap-2 mb-9">
          {gscOverviewIsLoading ? (
            <div className="flex justify-between w-full mt-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <Skeleton
                  key={index}
                  height={72}
                  width={150}
                  style={{ borderRadius: "10px" }}
                />
              ))}
            </div>
          ) : (
            coloredKeys.map(({ name, color }, index) => {
              if (gscOverviewData)
                if (!name.includes("dotted_"))
                  return (
                    <CheckboxCard
                      title={name}
                      color={color}
                      key={index}
                      selected={selectedLines}
                      cardsData={gscOverviewData.cardsData[name] ?? ""}
                      onToggleCheck={() => handleToggleCheck(name)}
                    />
                  );
            })
          )}
        </div>
      }
      <AnimatePresence mode="wait">
        {gscOverviewIsLoading ? (
          <motion.div
            key="line-chart-skeleton"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full"
          >
            <LineChartSkeleton />
          </motion.div>
        ) : (
          gscOverviewData && (
            <motion.div
              key="line-chart"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <GSCLineChart
                lineChartData={gscOverviewData!.lineChartData}
                colors={coloredKeys}
                selectedLines={selectedLines}
                cardsData={gscOverviewData!.cardsData}
              />
            </motion.div>
          )
        )}
      </AnimatePresence>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link href={"/project/analytics-traffics/gsc-insight?tab=gsc"}>
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default GSCOverview;
