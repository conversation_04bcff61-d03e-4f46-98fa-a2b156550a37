import React from "react";
import { IoClose } from "react-icons/io5";
import Card from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import toast from "react-hot-toast";

interface SharePopupProps {
  onClose: () => void;
}

const SharePopup: React.FC<SharePopupProps> = ({ onClose }) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText("https://seoanalyser/guestlink");
      toast.success("Copied to clipboard");
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <Card className="relative mx-auto w-[660px] max-w-[1000px]">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <span className="text-secondary font-medium text-sm">Share</span>
        <button
          onClick={onClose}
          className="text-secondary hover:text-secondary/80 transition-colors"
        >
          <IoClose size={20} />
        </button>
      </div>

      {/* Title */}
      <p className="text-[18px] font-bold text-secondary text-center mb-2">
        Your sharing URL
      </p>

      {/* Description */}
      <p className="text-[16px] text-secondary/80 text-center mb-6">
        Copy and paste the link below into emails, chats or browsers
      </p>

      {/* URL Section */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-secondary">
          Guest link URL
        </label>
        <div className="flex justify-between items-center border border-gray-200 rounded-lg p-4">
          <span className="text-sm text-secondary truncate mr-2">
            https://seoanalyser/guestlink
          </span>
          <Button
            onClick={handleCopy}
            variant="default"
            size="sm"
            className="shrink-0"
          >
            Copy
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SharePopup;
