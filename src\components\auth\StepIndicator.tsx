"use client";
import React, { ReactNode } from "react";
import {
  UserIcon,
  VerifyIcon,
  TagIcon,
  DownloadIcon,
  CrownIcon,
  CheckIcon,
} from "@/ui/icons/general";
import { CreditCardIcon } from "@/ui/icons/action";

type Step = {
  number: number;
  label: string;
  icon?: ReactNode;
};

type StepIndicatorProps = {
  steps: Step[];
  currentStep: number;
  flowType: "register" | "login";
};

export default function StepIndicator({
  steps,
  currentStep,
  flowType,
}: StepIndicatorProps) {
  // Function to get the appropriate icon based on step label
  const getStepIcon = (label: string, number: number) => {
    switch (label.toLowerCase()) {
      case "register":
      case "login":
        return <UserIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "verify":
        return <VerifyIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "white label":
      case "white label setting":
        return <TagIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "select plan":
        return <CrownIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "payment":
      case "billing":
        return <CreditCardIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "download":
      case "generate pdf":
        return <DownloadIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "share":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-4 h-4 md:w-5 md:h-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        );
      case "authentication":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-4 h-4 md:w-5 md:h-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
          </svg>
        );
      default:
        return <span className="text-lg md:text-xl">{number}</span>;
    }
  };

  return (
    <div className="w-full mb-4 md:mb-6">
      <div className="flex items-center justify-between relative">
        {/* Dashed line connecting all steps */}
        <div className="absolute top-[18px] md:top-[22px] left-0 right-0 h-[2px] border-t-2 border-dashed border-gray-300 z-0"></div>

        {steps.map((step, index) => {
          const isActive = step.number === currentStep;
          const isPast = step.number < currentStep;
          const isLast = index === steps.length - 1;

          return (
            <div key={step.number} className="flex flex-col items-center z-10">
              {/* Step circle with icon */}
              <div
                className={`flex items-center justify-center w-9 h-9 md:w-11 md:h-11 rounded-full text-xs md:text-sm font-bold mb-2 md:mb-3 transition-all duration-300 ${
                  isActive
                    ? "bg-primary text-white shadow-md shadow-primary/30"
                    : isPast
                    ? "bg-white text-primary border-2 border-primary"
                    : "bg-white text-gray-400 border-2 border-gray-300"
                }`}
              >
                {isPast ? (
                  <CheckIcon className="w-4 h-4 md:w-5 md:h-5 text-primary" />
                ) : (
                  getStepIcon(step.label, step.number)
                )}
              </div>

              {/* Step label */}
              <span
                className={`text-xs md:text-sm text-center font-medium transition-all duration-300 ${
                  isActive
                    ? "text-primary font-bold"
                    : isPast
                    ? "text-primary"
                    : "text-gray-500"
                }`}
              >
                {step.label}
              </span>
            </div>
          );
        })}
      </div>

      {/* Flow type indicator */}
      <div className="mt-3 md:mt-5 text-center">
        <span className="text-xs md:text-sm text-gray-600 font-medium">
          {flowType === "register" ? "Registration Flow" : "Login Flow"}
        </span>
      </div>
    </div>
  );
}
