import { ComponentProps } from "react";
import CustomCheckbox from "./CustomCheckbox";
import Link from "next/link";

type TermsCheckProps = {
  onChange: (value: boolean) => void;
  accepted: boolean;
} & Omit<ComponentProps<"input">, "onChange">;

const TermsCheck: React.FC<TermsCheckProps> = ({ accepted, onChange }) => {
  return (
    <div className="flex flex-col">
      <CustomCheckbox
        id="termsAccepted"
        label={
          <span className="inline-flex flex-wrap">
            I agree to the{" "}
            <Link
              href="/terms-and-conditions"
              className="text-primary hover:underline mx-1"
            >
              Terms of Service
            </Link>
            and{" "}
            <Link
              href="/privacy-policy"
              className="text-primary hover:underline mx-1"
            >
              Privacy Policy
            </Link>
          </span>
        }
        checked={accepted}
        onChange={onChange}
        required
      />
    </div>
  );
};

export default TermsCheck;
