import { SVGProps } from "react";

export function ImageIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="65"
      height="65"
      viewBox="0 0 65 65"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.2143 15.2461C21.3136 15.2459 20.4217 15.4231 19.5894 15.7675C18.7572 16.112 18.0009 16.6171 17.3639 17.2538C16.7268 17.8905 16.2214 18.6465 15.8765 19.4786C15.5316 20.3107 15.354 21.2025 15.3538 22.1032C15.3535 23.004 15.5307 23.8959 15.8752 24.7281C16.2197 25.5604 16.7247 26.3166 17.3615 26.9537C17.9982 27.5907 18.7542 28.0962 19.5863 28.4411C20.4183 28.786 21.3102 28.9636 22.2109 28.9638C24.03 28.9643 25.7747 28.2421 27.0613 26.9561C28.348 25.6701 29.071 23.9257 29.0715 22.1067C29.0719 20.2876 28.3497 18.5428 27.0638 17.2562C25.7778 15.9696 24.0334 15.2465 22.2143 15.2461ZM18.7858 22.1032C18.7858 21.1939 19.147 20.3219 19.79 19.6789C20.4329 19.0359 21.305 18.6747 22.2143 18.6747C23.1236 18.6747 23.9957 19.0359 24.6387 19.6789C25.2817 20.3219 25.6429 21.1939 25.6429 22.1032C25.6429 23.0126 25.2817 23.8846 24.6387 24.5276C23.9957 25.1706 23.1236 25.5318 22.2143 25.5318C21.305 25.5318 20.4329 25.1706 19.79 24.5276C19.147 23.8846 18.7858 23.0126 18.7858 22.1032Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.0717 8.38867C14.7984 8.38867 12.6182 9.29173 11.0108 10.8992C9.4033 12.5066 8.50024 14.6868 8.50024 16.9601V47.8172C8.50024 50.0905 9.4033 52.2707 11.0108 53.8782C12.6182 55.4856 14.7984 56.3887 17.0717 56.3887H47.9288C50.2021 56.3887 52.3823 55.4856 53.9897 53.8782C55.5972 52.2707 56.5002 50.0905 56.5002 47.8172V16.9601C56.5002 14.6868 55.5972 12.5066 53.9897 10.8992C52.3823 9.29173 50.2021 8.38867 47.9288 8.38867H17.0717ZM53.0717 16.9601V40.2401L42.2717 29.4401C41.9502 29.1187 41.5142 28.9382 41.0597 28.9382C40.6051 28.9382 40.1691 29.1187 39.8477 29.4401L16.3962 52.8915C15.1554 52.7294 14.0158 52.1219 13.1896 51.1821C12.3633 50.2423 11.9067 49.0343 11.9048 47.783V16.9258C11.9048 15.5618 12.4467 14.2537 13.4111 13.2893C14.3756 12.3248 15.6837 11.783 17.0477 11.783H47.9048C49.2688 11.783 50.5769 12.3248 51.5414 13.2893C52.5058 14.2537 53.0477 15.5618 53.0477 16.9258L53.0717 16.9601ZM47.9288 52.9601H21.2202L41.0717 33.1087L53.0717 45.1087V47.8275C53.0717 49.1915 52.5298 50.4996 51.5654 51.4641C50.6009 52.4286 49.2928 52.9704 47.9288 52.9704V52.9601Z"
        fill="currentColor"
      />
    </svg>
  );
}
