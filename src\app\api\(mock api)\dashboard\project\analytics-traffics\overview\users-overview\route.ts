import { NextResponse } from "next/server";
import type { UsersOverviewData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/users-overview/UsersOverview.type";

const usersOverviewData: UsersOverviewData = {
  leftMap: {
    USA: "20, 2000, 2",
    CAN: "50, 5000, 5",
    RUS: "50, 5000, 5",
    CHN: "20, 2000, 2",
    IND: "20, 2000, 2",
    BRA: "30, 3000, 3",
    IRN: "100, 10000, 10",
  },
  rightMap: {
    USA: "20, 2000, 2",
    CAN: "50, 5000, 5",
    RUS: "50, 5000, 5",
    CHN: "20, 2000, 2",
    IND: "20, 2000, 2",
    BRA: "30, 3000, 3",
    IRN: "100, 10000, 10",
  },
  progressbarData: [
    {
      title: "under 18",
      percentage: 50,
    },
    {
      title: "18-25",
      percentage: 20,
    },
    {
      title: "25-35",
      percentage: 90,
    },
    {
      title: "35-45",
      percentage: 80,
    },
    {
      title: "45-55",
      percentage: 20,
    },
    {
      title: "55-65",
      percentage: 60,
    },
    {
      title: "over 65",
      percentage: 50,
    },
  ],
};

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  return NextResponse.json(usersOverviewData);
}
