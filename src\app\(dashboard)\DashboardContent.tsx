"use client";

import { useAuth } from "@/providers/AuthProvider";
import { useEffect, Suspense, ReactNode, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import NavbarDashboard from "@/components/dashboard/NavbarDashboard";
import {
  fastRedirectIfNoAuth,
  hasAccessToken,
  getImmediateAuthStatus,
} from "@/utils/fastAuthCheck";
import ChartPopup from "./project/[projectId]/analytics-traffics/overview/(overview)/audience-overview/_components/ChartPopup";
interface DashboardContentProps {
  children: ReactNode;
}

export default function DashboardContent({ children }: DashboardContentProps) {
  const router = useRouter();
  const pathname = usePathname();
  const {
    isAuthenticated,
    refreshProfile,
    redirectToLogin,
    isHydrated,
    hasTokens,
  } = useAuth();

  // Check if current route is my-projects
  const isMyProjectsRoute = pathname === "/my-projects";

  // State to track if we've performed fast redirect
  const [hasPerformedFastRedirect, setHasPerformedFastRedirect] =
    useState(false);

  // IMMEDIATE token check and redirect - runs before any other logic
  useEffect(() => {
    // Perform immediate auth status check
    const authStatus = getImmediateAuthStatus();

    if (authStatus.shouldRedirect) {
      console.log("Fast redirect: No tokens found for protected route");
      router.replace("/");
      setHasPerformedFastRedirect(true);
      return;
    }

    // If we have tokens but are on a protected route, allow component to continue
    if (authStatus.isProtectedRoute && authStatus.hasTokens) {
      console.log("Fast check: Tokens found, allowing dashboard access");
    }
  }, [router, pathname]);

  // Enhanced authentication check for dashboard with stronger protection
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Fast path: if we have tokens, try a lightweight refresh first
        if (hasTokens) {
          const isValid = await refreshProfile(false); // Use cached if available
          if (isValid) return; // Auth is valid, no need to redirect
        }

        // Fallback: force refresh if no tokens or cached auth failed
        const isValid = await refreshProfile(true);
        if (!isValid) {
          // Use replace to prevent back button navigation to dashboard
          router.replace("/");
          return;
        }
      } catch (error) {
        console.error("Dashboard auth check failed:", error);
        // Use replace to prevent back button navigation to dashboard
        router.replace("/");
        return;
      }
    };

    // Check auth after hydration - be more strict about authentication
    if (!isHydrated) {
      return; // Don't run auth check until hydrated
    }

    // If no tokens at all, immediately redirect
    if (!hasTokens && !isAuthenticated) {
      router.replace("/");
      return;
    }

    // If not authenticated but has tokens, verify them
    if (!isAuthenticated) {
      checkAuth();
    }
  }, [isAuthenticated, refreshProfile, router, isHydrated, hasTokens]);

  // Early return if we've redirected to prevent any rendering
  if (hasPerformedFastRedirect) {
    return null;
  }

  // Fast check: if no tokens at all, don't render anything (redirect will happen)
  if (!hasAccessToken() && isHydrated) {
    return null;
  }

  // Show loading while hydrating
  if (!isHydrated) {
    const containerClass = isMyProjectsRoute
      ? "mx-auto h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5"
      : "mx-auto min-h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5";

    return (
      <div className={containerClass}>
        <div className="max-w-7xl mx-auto w-full">
          <div className="h-16 bg-white rounded-xl shadow animate-pulse flex-shrink-0" />
        </div>
        <div className="flex-1 max-w-7xl mx-auto w-full">
          <div className="h-full bg-white rounded-xl shadow animate-pulse" />
        </div>
      </div>
    );
  }

  // Show loading while authentication is being verified (but we have tokens)
  if (!isAuthenticated && hasAccessToken()) {
    const containerClass = isMyProjectsRoute
      ? "mx-auto h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5"
      : "mx-auto min-h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5";

    return (
      <div className={containerClass}>
        <div className="max-w-7xl mx-auto w-full">
          <div className="h-16 bg-white rounded-xl shadow animate-pulse flex-shrink-0" />
        </div>
        <div className="flex-1 min-h-0 max-w-7xl mx-auto w-full flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Verifying authentication...</p>
          </div>
        </div>
      </div>
    );
  }

  // Render dashboard content for authenticated users
  if (isMyProjectsRoute) {
    // For my-projects: use full available height without scrolling
    return (
      <div className="mx-auto flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5 h-full overflow-hidden">
        <div className="max-w-7xl mx-auto w-full flex-shrink-0">
          <Suspense
            fallback={
              <div className="h-16 bg-white rounded-xl shadow animate-pulse flex-shrink-0" />
            }
          >
            <NavbarDashboard />
          </Suspense>
        </div>
        <div className="flex-1 min-h-0 max-w-7xl mx-auto w-full overflow-hidden">
          {children}
        </div>
      </div>
    );
  }

  // For other routes: allow whole page to scroll when content overflows
  return (
    <div className="mx-auto min-h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5">
      <div className="max-w-8xl mx-auto w-full">
        <Suspense
          fallback={
            <div className="h-16 bg-white rounded-xl shadow animate-pulse flex-shrink-0" />
          }
        >
          <NavbarDashboard />
        </Suspense>
      </div>
      <ChartPopup />
      <div className="flex-1 max-w-8xl mx-auto w-full">{children}</div>
    </div>
  );
}
