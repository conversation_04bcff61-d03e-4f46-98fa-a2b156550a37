import { TableDataRequest } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { NextResponse } from "next/server";

const TRAFFIC_TABLE_DATA: TableDataRequest = {
  tableData: {
    tableHeadings: [
      "ORGANIC",
      "SESSIONS",
      "ENGAGED SESSIONS",
      "NEW USERS",
      "TOTAL USERS",
      "VIEWS",
      "ENGAGED TIME",
      "ENGAGED RATE",
      "EVENT COUNT",
      "CONVERSIONS",
    ],

    tableBody: [
      [
        { value: "Google" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Bing" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Yahoo" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Yandex" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
    ],
  },
  pagination: { totalPages: 20, initialPage: 1 },
};

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function GET() {
  await delay(1000);
  return NextResponse.json(TRAFFIC_TABLE_DATA);
}
