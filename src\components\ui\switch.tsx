"use client";

import * as React from "react";
import * as SwitchPrimitive from "@radix-ui/react-switch";
import { cn } from "@/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      data-slot="switch"
      className={cn(
        "peer data-[state=unchecked]:bg-[var(--color-pink-20)] data-[state=checked]:bg-[#914AC4] focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=checked]:bg-input/80 inline-flex h-6 w-12 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          "bg-white dark:bg-zinc-100 pointer-events-none block h-5 w-5 rounded-full ring-0 transition-transform translate-x-[2px] data-[state=checked]:translate-x-[26px]"
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
