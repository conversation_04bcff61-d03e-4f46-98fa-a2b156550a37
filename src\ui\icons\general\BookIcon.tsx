import { SVGProps } from "react";

export function BookIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4 8.6958C4 5.86737 4 4.45316 4.87868 3.57448C5.75736 2.6958 7.17157 2.6958 10 2.6958H14C16.8284 2.6958 18.2426 2.6958 19.1213 3.57448C20 4.45316 20 5.86737 20 8.6958V16.6958C20 19.5242 20 20.9384 19.1213 21.8171C18.2426 22.6958 16.8284 22.6958 14 22.6958H10C7.17157 22.6958 5.75736 22.6958 4.87868 21.8171C4 20.9384 4 19.5242 4 16.6958V8.6958Z"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        d="M19.8978 16.6958H7.89778C6.96781 16.6958 6.50282 16.6958 6.12132 16.798C5.08604 17.0754 4.2774 17.8841 4 18.9193"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        d="M8 7.6958H16"
        stroke="currentColor"
        strokeOpacity="0.35"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M8 11.1958H13"
        stroke="currentColor"
        strokeOpacity="0.35"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M19.5 19.6958H8"
        stroke="currentColor"
        strokeOpacity="0.35"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
