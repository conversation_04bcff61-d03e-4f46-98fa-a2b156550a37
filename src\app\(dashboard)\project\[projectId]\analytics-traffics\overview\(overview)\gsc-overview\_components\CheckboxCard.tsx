import React from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Checkbox from "@/components/ui/Checkbox";

import type { CheckboxCardProps } from "../GSCOverview.types";
import abbreviateNumber from "@/utils/abbreviateNumber";

/* ========================================================================== */
const CheckboxCard = ({
  title,
  color,
  cardsData,
  selected,
  onToggleCheck,
}: CheckboxCardProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="bg-[#F4F4F4] w-40 h-fit text-secondary flex items-start justify-center space-x-4 flex-col gap-2 p-3 rounded-lg mt-2">
      <div className="flex justify-between mr-0 w-full">
        <div className="flex gap-1 items-center">
          <div
            className="w-2 h-2 rounded-full -translate-y-[1px]"
            style={{ background: color }}
          />
          <label
            htmlFor="clicks"
            className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
          >
            {title.replace("_", " ")}
          </label>
        </div>
        <Checkbox
          id="clicks"
          checked={selected.includes(title)}
          onChange={onToggleCheck}
        />
      </div>
      <div className="space-x-2">
        <span className="text-lg font-extrabold">
          {abbreviateNumber(cardsData.amount)}
        </span>
        <span
          className={`text-xs ${
            cardsData.growth.includes("-")
              ? "text-primary-red"
              : cardsData.growth.includes("+")
                ? "text-primary-green"
                : "text-secondary"
          }`}
        >
          {cardsData.growth}
        </span>
      </div>
    </Card>
  );
};

export default CheckboxCard;
