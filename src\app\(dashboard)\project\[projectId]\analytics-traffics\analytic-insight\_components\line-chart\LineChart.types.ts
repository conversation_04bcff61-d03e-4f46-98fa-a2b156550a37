import {
  CardsData,
  GSCOverviewType,
} from "../../../overview/(overview)/gsc-overview/GSCOverview.types";

export type ColorMap = {
  name: string;
  color: string;
};

type BaseProps = Omit<GSCOverviewType, "cardsData" | "lineChartData"> & {
  className?: string;
  tooltipItemsClassName?: string;
};

type LoadedProps = {
  isLoading?: false | undefined;
  colors: ColorMap[];
  selectedLines: string[];
  cardsData: Record<string, CardsData>;
  lineChartData: Record<string, string | number>[];
};

type LoadingProps = {
  isLoading: true;
  colors?: ColorMap[];
  selectedLines?: string[];
  cardsData?: Record<string, CardsData>;
  lineChartData?: Record<string, string | number>[];
};

export type LineChartType = BaseProps & (LoadedProps | LoadingProps);
