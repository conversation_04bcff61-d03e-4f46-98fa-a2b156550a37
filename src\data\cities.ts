const allCities = {
    AC: [ // Ascension Island
        'Georgetown',
        'Two Boats Village',
        'Travellers Hill',
        'Green Mountain'
    ],
    AD: [ // Andorra
        'Andorra la Vella',
        'Escaldes-Engordany',
        'Encamp',
        '<PERSON> Lò<PERSON>',
        'La Massana',
        'Ordin<PERSON>',
        'Canillo'
    ],
    AE: [ // United Arab Emirates
        'Dubai',
        'Abu Dhabi',
        'Sharjah',
        'Al Ain',
        'Ajman',
        'Ras Al Khaimah',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>'
    ],
    AF: [ // Afghanistan
        'Kabul',
        'Kandahar',
        'Herat',
        'Mazar-i-Sharif',
        'Jalalabad',
        'Kunduz',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>'
    ],
    AG: [ // Antigua & Barbuda
        'Saint John\'s',
        'All Saints',
        'Liberta',
        'Parham',
        'Codrington',
        'Bolands',
        'Swetes'
    ],
    AI: [ // Anguilla
        'The Valley',
        'Blowing Point',
        'Sandy Ground',
        'West End',
        'Island Harbour',
        'The Quarter',
        'North Side'
    ],
    AL: [ // Albania
        'Tirana',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        'Elbasan',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        'Kor<PERSON><PERSON>',
        '<PERSON><PERSON>'
    ],
    // Continue with next set of countries...
    AM: [ // Armenia
        'Yerevan',
        'Gyumri',
        'Vanadzor',
        'Vagharshapat',
        'Abovyan',
        'Kapan',
        'Hrazdan',
        'Armavir'
    ],
    AO: [ // Angola
        'Luanda',
        'Huambo',
        'Lobito',
        'Benguela',
        'Kuito',
        'Malanje',
        'Lubango',
        'Namibe'
    ],
    AR: [ // Argentina
        'Buenos Aires',
        'Córdoba',
        'Rosario',
        'Mendoza',
        'La Plata',
        'Tucumán',
        'Mar del Plata',
        'Salta'
    ],
    AT: [ // Austria
        'Vienna',
        'Graz',
        'Linz',
        'Salzburg',
        'Innsbruck',
        'Klagenfurt',
        'Villach',
        'Wels'
    ],
    AU: [ // Australia
        'Sydney',
        'Melbourne',
        'Brisbane',
        'Perth',
        'Adelaide',
        'Gold Coast',
        'Canberra',
        'Newcastle'
    ], AW: [ // Aruba
        'Oranjestad',
        'San Nicolas',
        'Noord',
        'Santa Cruz',
        'Paradera',
        'Savaneta',
        'Palm Beach'
    ],
    AZ: [ // Azerbaijan
        'Baku',
        'Ganja',
        'Sumqayit',
        'Mingachevir',
        'Shirvan',
        'Nakhchivan',
        'Lankaran',
        'Shamakhi'
    ],
    BA: [ // Bosnia & Herzegovina
        'Sarajevo',
        'Banja Luka',
        'Tuzla',
        'Zenica',
        'Mostar',
        'Bihać',
        'Brčko',
        'Prijedor'
    ],
    BB: [ // Barbados
        'Bridgetown',
        'Speightstown',
        'Oistins',
        'Holetown',
        'Bathsheba',
        'Blackman',
        'Crane'
    ],
    BD: [ // Bangladesh
        'Dhaka',
        'Chittagong',
        'Khulna',
        'Rajshahi',
        'Sylhet',
        'Barisal',
        'Rangpur',
        'Comilla'
    ], BE: [ // Belgium
        'Brussels',
        'Antwerp',
        'Ghent',
        'Charleroi',
        'Liège',
        'Bruges',
        'Namur',
        'Leuven'
    ],
    BF: [ // Burkina Faso
        'Ouagadougou',
        'Bobo-Dioulasso',
        'Koudougou',
        'Banfora',
        'Ouahigouya',
        'Pouytenga',
        'Kaya',
        'Tenkodogo'
    ],
    BG: [ // Bulgaria
        'Sofia',
        'Plovdiv',
        'Varna',
        'Burgas',
        'Ruse',
        'Stara Zagora',
        'Pleven',
        'Sliven'
    ],
    BH: [ // Bahrain
        'Manama',
        'Riffa',
        'Muharraq',
        'Hamad Town',
        'A\'ali',
        'Isa Town',
        'Sitra',
        'Budaiya'
    ],
    BI: [ // Burundi
        'Bujumbura',
        'Gitega',
        'Muyinga',
        'Ruyigi',
        'Ngozi',
        'Rumonge',
        'Bururi',
        'Makamba'
    ],
    BJ: [ // Benin
        'Porto-Novo',
        'Cotonou',
        'Parakou',
        'Djougou',
        'Bohicon',
        'Kandi',
        'Abomey',
        'Natitingou'
    ],
    BM: [ // Bermuda
        'Hamilton',
        'St. George\'s',
        'Somerset Village',
        'Tucker\'s Town',
        'Flatts Village',
        'Bailey\'s Bay',
        'Southampton'
    ],
    BN: [ // Brunei
        'Bandar Seri Begawan',
        'Kuala Belait',
        'Tutong',
        'Bangar',
        'Seria',
        'Muara',
        'Mentiri',
        'Berakas'
    ],
    BO: [ // Bolivia
        'La Paz',
        'Santa Cruz',
        'Cochabamba',
        'Sucre',
        'Oruro',
        'Tarija',
        'Potosí',
        'Trinidad'
    ],
    BR: [ // Brazil
        'São Paulo',
        'Rio de Janeiro',
        'Brasília',
        'Salvador',
        'Fortaleza',
        'Belo Horizonte',
        'Manaus',
        'Curitiba'
    ],
    BS: [ // Bahamas
        'Nassau',
        'Freeport',
        'West End',
        'Marsh Harbour',
        'High Rock',
        'Andros Town',
        'George Town',
        'Matthew Town'
    ],
    BT: [ // Bhutan
        'Thimphu',
        'Phuntsholing',
        'Paro',
        'Punakha',
        'Gelephu',
        'Samdrup Jongkhar',
        'Bumthang',
        'Trashigang'
    ],
    BW: [ // Botswana
        'Gaborone',
        'Francistown',
        'Molepolole',
        'Maun',
        'Serowe',
        'Selibe Phikwe',
        'Kanye',
        'Mahalapye'
    ],
    BY: [ // Belarus
        'Minsk',
        'Gomel',
        'Mogilev',
        'Vitebsk',
        'Grodno',
        'Brest',
        'Bobruisk',
        'Baranovichi'
    ],
    BZ: [ // Belize
        'Belize City',
        'Belmopan',
        'San Ignacio',
        'Orange Walk',
        'Dangriga',
        'Corozal',
        'Punta Gorda',
        'San Pedro'
    ],
    CA: [ // Canada
        'Toronto',
        'Montreal',
        'Vancouver',
        'Calgary',
        'Ottawa',
        'Edmonton',
        'Quebec City',
        'Winnipeg'
    ],
    CD: [ // Congo - Kinshasa
        'Kinshasa',
        'Lubumbashi',
        'Mbuji-Mayi',
        'Kisangani',
        'Kananga',
        'Bukavu',
        'Goma',
        'Matadi'
    ],
    CF: [ // Central African Republic
        'Bangui',
        'Bimbo',
        'Berbérati',
        'Carnot',
        'Bambari',
        'Bouar',
        'Bossangoa',
        'Bangassou'
    ],
    CG: [ // Congo - Brazzaville
        'Brazzaville',
        'Pointe-Noire',
        'Dolisie',
        'Nkayi',
        'Impfondo',
        'Ouesso',
        'Sibiti',
        'Madingou'
    ], CH: [ // Switzerland
        'Zurich',
        'Geneva',
        'Basel',
        'Lausanne',
        'Bern',
        'Lucerne',
        'St. Gallen',
        'Lugano'
    ],
    CI: [ // Côte d'Ivoire
        'Abidjan',
        'Bouaké',
        'Yamoussoukro',
        'Daloa',
        'San-Pédro',
        'Korhogo',
        'Man',
        'Divo'
    ],
    CL: [ // Chile
        'Santiago',
        'Valparaíso',
        'Concepción',
        'Antofagasta',
        'Viña del Mar',
        'Temuco',
        'Rancagua',
        'Talca'
    ],
    CM: [ // Cameroon
        'Yaoundé',
        'Douala',
        'Garoua',
        'Bamenda',
        'Maroua',
        'Bafoussam',
        'Ngaoundéré',
        'Bertoua'
    ],
    CN: [ // China
        'Shanghai',
        'Beijing',
        'Guangzhou',
        'Shenzhen',
        'Chongqing',
        'Tianjin',
        'Chengdu',
        'Wuhan'
    ],
    CO: [ // Colombia
        'Bogotá',
        'Medellín',
        'Cali',
        'Barranquilla',
        'Cartagena',
        'Bucaramanga',
        'Pereira',
        'Santa Marta'
    ],
    CR: [ // Costa Rica
        'San José',
        'Alajuela',
        'Cartago',
        'Heredia',
        'Limón',
        'Puntarenas',
        'Liberia',
        'San Francisco'
    ],
    CU: [ // Cuba
        'Havana',
        'Santiago de Cuba',
        'Camagüey',
        'Holguín',
        'Guantánamo',
        'Santa Clara',
        'Cienfuegos',
        'Pinar del Río'
    ],
    CV: [ // Cape Verde
        'Praia',
        'Mindelo',
        'Santa Maria',
        'Assomada',
        'Espargos',
        'Tarrafal',
        'São Filipe',
        'Porto Novo'
    ],
    CY: [ // Cyprus
        'Nicosia',
        'Limassol',
        'Larnaca',
        'Famagusta',
        'Paphos',
        'Kyrenia',
        'Protaras',
        'Paralimni'
    ],
    CZ: [ // Czechia
        'Prague',
        'Brno',
        'Ostrava',
        'Pilsen',
        'Liberec',
        'Olomouc',
        'České Budějovice',
        'Hradec Králové'
    ],
    DE: [ // Germany
        'Berlin',
        'Hamburg',
        'Munich',
        'Cologne',
        'Frankfurt',
        'Stuttgart',
        'Düsseldorf',
        'Leipzig'
    ],
    DJ: [ // Djibouti
        'Djibouti City',
        'Ali Sabieh',
        'Dikhil',
        'Tadjoura',
        'Obock',
        'Arta',
        'Holhol',
        'Dorra'
    ],
    DK: [ // Denmark
        'Copenhagen',
        'Aarhus',
        'Odense',
        'Aalborg',
        'Frederiksberg',
        'Esbjerg',
        'Randers',
        'Kolding'
    ],
    DM: [ // Dominica
        'Roseau',
        'Portsmouth',
        'Marigot',
        'Grand Bay',
        'Castle Bruce',
        'Saint Joseph',
        'Berekua',
        'Wesley'
    ],
    DO: [ // Dominican Republic
        'Santo Domingo',
        'Santiago',
        'San Pedro de Macorís',
        'La Romana',
        'Puerto Plata',
        'San Francisco de Macorís',
        'La Vega',
        'San Cristóbal'
    ],
    DZ: [ // Algeria
        'Algiers',
        'Oran',
        'Constantine',
        'Annaba',
        'Blida',
        'Batna',
        'Djelfa',
        'Sétif'
    ],
    EC: [ // Ecuador
        'Quito',
        'Guayaquil',
        'Cuenca',
        'Machala',
        'Manta',
        'Portoviejo',
        'Ambato',
        'Esmeraldas'
    ],
    EE: [ // Estonia
        'Tallinn',
        'Tartu',
        'Narva',
        'Pärnu',
        'Kohtla-Järve',
        'Viljandi',
        'Rakvere',
        'Maardu'
    ], EG: [ // Egypt
        'Cairo',
        'Alexandria',
        'Giza',
        'Shubra El Kheima',
        'Port Said',
        'Suez',
        'Luxor',
        'Mansoura'
    ],
    ER: [ // Eritrea
        'Asmara',
        'Keren',
        'Massawa',
        'Assab',
        'Mendefera',
        'Barentu',
        'Dekemhare',
        'Teseney'
    ],
    ES: [ // Spain
        'Madrid',
        'Barcelona',
        'Valencia',
        'Seville',
        'Zaragoza',
        'Málaga',
        'Murcia',
        'Bilbao'
    ],
    ET: [ // Ethiopia
        'Addis Ababa',
        'Dire Dawa',
        'Mek\'ele',
        'Gondar',
        'Adama',
        'Hawassa',
        'Bahir Dar',
        'Jimma'
    ],
    FI: [ // Finland
        'Helsinki',
        'Espoo',
        'Tampere',
        'Vantaa',
        'Oulu',
        'Turku',
        'Jyväskylä',
        'Lahti'
    ],
    FJ: [ // Fiji
        'Suva',
        'Lautoka',
        'Nadi',
        'Labasa',
        'Levuka',
        'Ba',
        'Sigatoka',
        'Savusavu'
    ],
    FR: [ // France
        'Paris',
        'Marseille',
        'Lyon',
        'Toulouse',
        'Nice',
        'Nantes',
        'Strasbourg',
        'Bordeaux'
    ],
    GA: [ // Gabon
        'Libreville',
        'Port-Gentil',
        'Franceville',
        'Oyem',
        'Moanda',
        'Mouila',
        'Lambaréné',
        'Tchibanga'
    ],
    GB: [ // United Kingdom
        'London',
        'Birmingham',
        'Manchester',
        'Glasgow',
        'Liverpool',
        'Leeds',
        'Edinburgh',
        'Bristol'
    ],
    GD: [ // Grenada
        'St. George\'s',
        'Gouyave',
        'Grenville',
        'Victoria',
        'Sauteurs',
        'Hillsborough',
        'St. David\'s',
        'Becke Moui'
    ],
    GE: [ // Georgia
        'Tbilisi',
        'Batumi',
        'Kutaisi',
        'Rustavi',
        'Gori',
        'Zugdidi',
        'Poti',
        'Kobuleti'
    ],
    GH: [ // Ghana
        'Accra',
        'Kumasi',
        'Tamale',
        'Sekondi-Takoradi',
        'Sunyani',
        'Cape Coast',
        'Tema',
        'Koforidua'
    ],
    GI: [ // Gibraltar
        'Gibraltar',
        'Westside',
        'Eastside',
        'Catalan Bay',
        'Sandy Bay',
        'Europa Point',
        'North District'
    ],
    GL: [ // Greenland
        'Nuuk',
        'Sisimiut',
        'Ilulissat',
        'Qaqortoq',
        'Aasiaat',
        'Maniitsoq',
        'Tasiilaq',
        'Nanortalik'
    ],
    GM: [ // Gambia
        'Banjul',
        'Serekunda',
        'Brikama',
        'Bakau',
        'Farafenni',
        'Lamin',
        'Soma',
        'Gunjur'
    ],
    GN: [ // Guinea
        'Conakry',
        'Nzérékoré',
        'Kindia',
        'Kankan',
        'Guéckédou',
        'Boké',
        'Labé',
        'Mamou'
    ],
    GQ: [ // Equatorial Guinea
        'Malabo',
        'Bata',
        'Ebebiyin',
        'Aconibe',
        'Añisoc',
        'Luba',
        'Mongomo',
        'Evinayong'
    ],
    GR: [ // Greece
        'Athens',
        'Thessaloniki',
        'Patras',
        'Heraklion',
        'Larissa',
        'Volos',
        'Rhodes',
        'Ioannina'
    ],
    GT: [ // Guatemala
        'Guatemala City',
        'Mixco',
        'Villa Nueva',
        'Quetzaltenango',
        'Escuintla',
        'Huehuetenango',
        'Cobán',
        'San Juan Sacatepéquez'
    ],
    GW: [ // Guinea-Bissau
        'Bissau',
        'Bafatá',
        'Gabú',
        'Cacheu',
        'Bolama',
        'Catió',
        'Farim',
        'Bubaque'
    ],
    GY: [ // Guyana
        'Georgetown',
        'Linden',
        'New Amsterdam',
        'Anna Regina',
        'Bartica',
        'Skeldon',
        'Mabaruma',
        'Lethem'
    ], HK: [ // Hong Kong
        'Central and Western',
        'Wan Chai',
        'Kowloon City',
        'Tsuen Wan',
        'Sha Tin',
        'Tuen Mun',
        'Tai Po',
        'Sai Kung'
    ],
    HN: [ // Honduras
        'Tegucigalpa',
        'San Pedro Sula',
        'Choloma',
        'La Ceiba',
        'El Progreso',
        'Choluteca',
        'Comayagua',
        'Puerto Cortés'
    ],
    HR: [ // Croatia
        'Zagreb',
        'Split',
        'Rijeka',
        'Osijek',
        'Zadar',
        'Pula',
        'Slavonski Brod',
        'Dubrovnik'
    ],
    HT: [ // Haiti
        'Port-au-Prince',
        'Cap-Haïtien',
        'Carrefour',
        'Delmas',
        'Pétionville',
        'Gonaïves',
        'Saint-Marc',
        'Les Cayes'
    ],
    HU: [ // Hungary
        'Budapest',
        'Debrecen',
        'Szeged',
        'Miskolc',
        'Pécs',
        'Győr',
        'Nyíregyháza',
        'Kecskemét'
    ],
    ID: [ // Indonesia
        'Jakarta',
        'Surabaya',
        'Bandung',
        'Medan',
        'Semarang',
        'Makassar',
        'Palembang',
        'Tangerang'
    ],
    IE: [ // Ireland
        'Dublin',
        'Cork',
        'Limerick',
        'Galway',
        'Waterford',
        'Drogheda',
        'Dundalk',
        'Swords'
    ],
    IL: [ // Israel
        'Jerusalem',
        'Tel Aviv',
        'Haifa',
        'Rishon LeZion',
        'Petah Tikva',
        'Ashdod',
        'Netanya',
        'Beer Sheva'
    ],
    IN: [ // India
        'Mumbai',
        'Delhi',
        'Bangalore',
        'Hyderabad',
        'Chennai',
        'Kolkata',
        'Ahmedabad',
        'Pune'
    ],
    IQ: [ // Iraq
        'Baghdad',
        'Basra',
        'Mosul',
        'Erbil',
        'Najaf',
        'Karbala',
        'Sulaymaniyah',
        'Kirkuk'
    ],
    IR: [ // Iran
        'Tehran',
        'Mashhad',
        'Isfahan',
        'Karaj',
        'Shiraz',
        'Tabriz',
        'Qom',
        'Ahvaz'
    ],
    IS: [ // Iceland
        'Reykjavík',
        'Kópavogur',
        'Hafnarfjörður',
        'Akureyri',
        'Garðabær',
        'Mosfellsbær',
        'Keflavík',
        'Akranes'
    ],
    IT: [ // Italy
        'Rome',
        'Milan',
        'Naples',
        'Turin',
        'Florence',
        'Venice',
        'Bologna',
        'Genoa'
    ],
    JM: [ // Jamaica
        'Kingston',
        'Montego Bay',
        'Spanish Town',
        'Portmore',
        'May Pen',
        'Mandeville',
        'Saint Ann\'s Bay',
        'Ocho Rios'
    ],
    JO: [ // Jordan
        'Amman',
        'Zarqa',
        'Irbid',
        'Russeifa',
        'Al-Salt',
        'Madaba',
        'Aqaba',
        'Karak'
    ],
    JP: [ // Japan
        'Tokyo',
        'Yokohama',
        'Osaka',
        'Nagoya',
        'Sapporo',
        'Fukuoka',
        'Kobe',
        'Kyoto'
    ], KE: [ // Kenya
        'Nairobi',
        'Mombasa',
        'Kisumu',
        'Nakuru',
        'Eldoret',
        'Malindi',
        'Kitale',
        'Garissa'
    ],
    KG: [ // Kyrgyzstan
        'Bishkek',
        'Osh',
        'Jalal-Abad',
        'Karakol',
        'Tokmok',
        'Naryn',
        'Talas',
        'Kara-Balta'
    ],
    KH: [ // Cambodia
        'Phnom Penh',
        'Siem Reap',
        'Battambang',
        'Sihanoukville',
        'Kampong Cham',
        'Pursat',
        'Takeo',
        'Kampot'
    ],
    KR: [ // South Korea
        'Seoul',
        'Busan',
        'Incheon',
        'Daegu',
        'Daejeon',
        'Gwangju',
        'Suwon',
        'Ulsan'
    ],
    KW: [ // Kuwait
        'Kuwait City',
        'Al Ahmadi',
        'Hawalli',
        'Al Farwaniyah',
        'Al Jahra',
        'Sabah Al Salem',
        'Salmiya',
        'Fahaheel'
    ],
    KZ: [ // Kazakhstan
        'Almaty',
        'Nur-Sultan',
        'Shymkent',
        'Karaganda',
        'Aktobe',
        'Taraz',
        'Pavlodar',
        'Oskemen'
    ],
    LA: [ // Laos
        'Vientiane',
        'Pakse',
        'Luang Prabang',
        'Savannakhet',
        'Thakhek',
        'Phonsavan',
        'Vang Vieng',
        'Xieng Khouang'
    ],
    LB: [ // Lebanon
        'Beirut',
        'Tripoli',
        'Sidon',
        'Tyre',
        'Jounieh',
        'Zahle',
        'Byblos',
        'Baalbek'
    ],
    LK: [ // Sri Lanka
        'Colombo',
        'Kandy',
        'Galle',
        'Jaffna',
        'Negombo',
        'Trincomalee',
        'Batticaloa',
        'Anuradhapura'
    ],
    LR: [ // Liberia
        'Monrovia',
        'Gbarnga',
        'Buchanan',
        'Kakata',
        'Voinjama',
        'Harper',
        'Zwedru',
        'Robertsport'
    ],
    LS: [ // Lesotho
        'Maseru',
        'Teyateyaneng',
        'Mafeteng',
        'Hlotse',
        'Mohale\'s Hoek',
        'Quthing',
        'Butha-Buthe',
        'Mokhotlong'
    ],
    LT: [ // Lithuania
        'Vilnius',
        'Kaunas',
        'Klaipėda',
        'Šiauliai',
        'Panevėžys',
        'Alytus',
        'Marijampolė',
        'Utena'
    ],
    LU: [ // Luxembourg
        'Luxembourg City',
        'Esch-sur-Alzette',
        'Differdange',
        'Dudelange',
        'Ettelbruck',
        'Diekirch',
        'Wiltz',
        'Echternach'
    ],
    LV: [ // Latvia
        'Riga',
        'Daugavpils',
        'Liepāja',
        'Jelgava',
        'Jūrmala',
        'Ventspils',
        'Rēzekne',
        'Valmiera'
    ],
    LY: [ // Libya
        'Tripoli',
        'Benghazi',
        'Misrata',
        'Zawiya',
        'Tobruk',
        'Sabha',
        'Al Bayda',
        'Ajdabiya'
    ],
    MA: [ // Morocco
        'Casablanca',
        'Rabat',
        'Fez',
        'Marrakesh',
        'Tangier',
        'Agadir',
        'Meknes',
        'Oujda'
    ],
    MC: [ // Monaco
        'Monte Carlo',
        'La Condamine',
        'Monaco-Ville',
        'Fontvieille',
        'Moneghetti',
        'Larvotto',
        'La Rousse',
        'Saint Roman'
    ], MD: [ // Moldova
        'Chișinău',
        'Bălți',
        'Orhei',
        'Ungheni',
        'Cahul',
        'Hîncești',
        'Soroca',
        'Hînceşti'
    ],
    ME: [ // Montenegro
        'Podgorica',
        'Nikšić',
        'Herceg Novi',
        'Bar',
        'Budva',
        'Bijelo Polje',
        'Cetinje',
        'Kotor'
    ],
    MG: [ // Madagascar
        'Antananarivo',
        'Toamasina',
        'Antsirabe',
        'Fianarantsoa',
        'Mahajanga',
        'Toliara',
        'Antsiranana',
        'Ambovombe'
    ],
    MK: [ // North Macedonia
        'Skopje',
        'Bitola',
        'Kumanovo',
        'Prilep',
        'Tetovo',
        'Veles',
        'Štip',
        'Ohrid'
    ],
    ML: [ // Mali
        'Bamako',
        'Sikasso',
        'Mopti',
        'Ségou',
        'Kayes',
        'Koutiala',
        'Gao',
        'Timbuktu'
    ],
    MM: [ // Myanmar
        'Yangon',
        'Mandalay',
        'Naypyidaw',
        'Mawlamyine',
        'Bago',
        'Pathein',
        'Monywa',
        'Meiktila'
    ],
    MN: [ // Mongolia
        'Ulaanbaatar',
        'Erdenet',
        'Darkhan',
        'Choibalsan',
        'Ölgii',
        'Mörön',
        'Bayankhongor',
        'Zuunmod'
    ],
    MR: [ // Mauritania
        'Nouakchott',
        'Nouadhibou',
        'Kiffa',
        'Rosso',
        'Kaédi',
        'Zouérat',
        'Atar',
        'Néma'
    ],
    MT: [ // Malta
        'Valletta',
        'Birkirkara',
        'Qormi',
        'Mosta',
        'Żabbar',
        'San Ġwann',
        'Sliema',
        'Naxxar'
    ],
    MU: [ // Mauritius
        'Port Louis',
        'Beau Bassin-Rose Hill',
        'Vacoas-Phoenix',
        'Curepipe',
        'Quatre Bornes',
        'Mahébourg',
        'Goodlands',
        'Triolet'
    ],
    MV: [ // Maldives
        'Malé',
        'Fuvahmulah',
        'Hithadhoo',
        'Kulhudhuffushi',
        'Thinadhoo',
        'Naifaru',
        'Dhidhdhoo',
        'Fonadhoo'
    ],
    MW: [ // Malawi
        'Lilongwe',
        'Blantyre',
        'Mzuzu',
        'Zomba',
        'Kasungu',
        'Mangochi',
        'Karonga',
        'Salima'
    ],
    MX: [ // Mexico
        'Mexico City',
        'Guadalajara',
        'Monterrey',
        'Puebla',
        'Tijuana',
        'León',
        'Juárez',
        'Zapopan'
    ],
    MY: [ // Malaysia
        'Kuala Lumpur',
        'Johor Bahru',
        'Penang',
        'Ipoh',
        'Shah Alam',
        'Petaling Jaya',
        'Kuching',
        'Kota Kinabalu'
    ],
    MZ: [ // Mozambique
        'Maputo',
        'Matola',
        'Beira',
        'Nampula',
        'Chimoio',
        'Nacala',
        'Quelimane',
        'Tete'
    ],
    NA: [ // Namibia
        'Windhoek',
        'Walvis Bay',
        'Swakopmund',
        'Oshakati',
        'Rundu',
        'Katima Mulilo',
        'Otjiwarongo',
        'Keetmanshoop'
    ], NE: [ // Niger
        'Niamey',
        'Zinder',
        'Maradi',
        'Agadez',
        'Tahoua',
        'Dosso',
        'Diffa',
        'Arlit'
    ],
    NG: [ // Nigeria
        'Lagos',
        'Kano',
        'Ibadan',
        'Abuja',
        'Port Harcourt',
        'Benin City',
        'Kaduna',
        'Jos'
    ],
    NI: [ // Nicaragua
        'Managua',
        'León',
        'Masaya',
        'Granada',
        'Chinandega',
        'Matagalpa',
        'Estelí',
        'Juigalpa'
    ],
    NL: [ // Netherlands
        'Amsterdam',
        'Rotterdam',
        'The Hague',
        'Utrecht',
        'Eindhoven',
        'Tilburg',
        'Groningen',
        'Almere'
    ],
    NO: [ // Norway
        'Oslo',
        'Bergen',
        'Trondheim',
        'Stavanger',
        'Drammen',
        'Fredrikstad',
        'Kristiansand',
        'Tromsø'
    ],
    NP: [ // Nepal
        'Kathmandu',
        'Pokhara',
        'Lalitpur',
        'Bharatpur',
        'Biratnagar',
        'Birgunj',
        'Butwal',
        'Dharan'
    ],
    NZ: [ // New Zealand
        'Auckland',
        'Wellington',
        'Christchurch',
        'Hamilton',
        'Tauranga',
        'Napier-Hastings',
        'Dunedin',
        'Lower Hutt'
    ],
    OM: [ // Oman
        'Muscat',
        'Salalah',
        'Seeb',
        'Sohar',
        'Nizwa',
        'Sur',
        'Ibri',
        'Rustaq'
    ],
    PA: [ // Panama
        'Panama City',
        'San Miguelito',
        'David',
        'Arraiján',
        'Colón',
        'La Chorrera',
        'Santiago',
        'Chitré'
    ],
    PE: [ // Peru
        'Lima',
        'Arequipa',
        'Trujillo',
        'Chiclayo',
        'Piura',
        'Cusco',
        'Iquitos',
        'Huancayo'
    ],
    PG: [ // Papua New Guinea
        'Port Moresby',
        'Lae',
        'Mount Hagen',
        'Madang',
        'Goroka',
        'Kokopo',
        'Wewak',
        'Kimbe'
    ],
    PH: [ // Philippines
        'Manila',
        'Quezon City',
        'Davao',
        'Cebu',
        'Makati',
        'Taguig',
        'Pasig',
        'Cagayan de Oro'
    ],
    PK: [ // Pakistan
        'Karachi',
        'Lahore',
        'Islamabad',
        'Faisalabad',
        'Rawalpindi',
        'Multan',
        'Peshawar',
        'Quetta'
    ],
    PL: [ // Poland
        'Warsaw',
        'Kraków',
        'Łódź',
        'Wrocław',
        'Poznań',
        'Gdańsk',
        'Szczecin',
        'Katowice'
    ],
    PT: [ // Portugal
        'Lisbon',
        'Porto',
        'Amadora',
        'Braga',
        'Setúbal',
        'Coimbra',
        'Funchal',
        'Aveiro'
    ], PY: [ // Paraguay 
        'Asunción',
        'Ciudad del Este',
        'San Lorenzo',
        'Luque',
        'Capiatá',
        'Lambaré',
        'Fernando de la Mora',
        'Limpio'
    ],
    QA: [ // Qatar
        'Doha',
        'Al Wakrah',
        'Al Khor',
        'Al Rayyan',
        'Umm Salal',
        'Al Daayen',
        'Mesaieed',
        'Al Shamal'
    ],
    RO: [ // Romania
        'Bucharest',
        'Cluj-Napoca',
        'Timișoara',
        'Iași',
        'Constanța',
        'Craiova',
        'Brașov',
        'Galați'
    ],
    RS: [ // Serbia
        'Belgrade',
        'Novi Sad',
        'Niš',
        'Kragujevac',
        'Subotica',
        'Zrenjanin',
        'Pančevo',
        'Čačak'
    ],
    RU: [ // Russia
        'Moscow',
        'Saint Petersburg',
        'Novosibirsk',
        'Yekaterinburg',
        'Kazan',
        'Nizhny Novgorod',
        'Chelyabinsk',
        'Samara'
    ],
    RW: [ // Rwanda
        'Kigali',
        'Butare',
        'Gitarama',
        'Ruhengeri',
        'Gisenyi',
        'Byumba',
        'Cyangugu',
        'Kibuye'
    ],
    SA: [ // Saudi Arabia
        'Riyadh',
        'Jeddah',
        'Mecca',
        'Medina',
        'Dammam',
        'Taif',
        'Tabuk',
        'Buraidah'
    ],
    SB: [ // Solomon Islands
        'Honiara',
        'Auki',
        'Gizo',
        'Kirakira',
        'Tulagi',
        'Buala',
        'Taro Island',
        'Lata'
    ],
    SC: [ // Seychelles
        'Victoria',
        'Anse Boileau',
        'Beau Vallon',
        'Cascade',
        'Takamaka',
        'Anse Royale',
        'Port Glaud',
        'Grand\'Anse Mahé'
    ],
    SD: [ // Sudan
        'Khartoum',
        'Omdurman',
        'Nyala',
        'Port Sudan',
        'Kassala',
        'El Obeid',
        'Wad Medani',
        'El Fasher'
    ],
    SE: [ // Sweden
        'Stockholm',
        'Gothenburg',
        'Malmö',
        'Uppsala',
        'Västerås',
        'Örebro',
        'Linköping',
        'Helsingborg'
    ],
    SG: [ // Singapore
        'Central Area',
        'Tampines',
        'Jurong East',
        'Woodlands',
        'Sengkang',
        'Yishun',
        'Ang Mo Kio',
        'Bukit Merah'
    ],
    SI: [ // Slovenia
        'Ljubljana',
        'Maribor',
        'Celje',
        'Kranj',
        'Velenje',
        'Koper',
        'Novo Mesto',
        'Ptuj'
    ],
    SK: [ // Slovakia
        'Bratislava',
        'Košice',
        'Prešov',
        'Nitra',
        'Žilina',
        'Banská Bystrica',
        'Trnava',
        'Martin'
    ],
    SL: [ // Sierra Leone
        'Freetown',
        'Bo',
        'Kenema',
        'Makeni',
        'Koidu',
        'Port Loko',
        'Lunsar',
        'Pandebu'
    ],
    SN: [ // Senegal
        'Dakar',
        'Touba',
        'Thiès',
        'Rufisque',
        'Kaolack',
        'Mbour',
        'Saint-Louis',
        'Ziguinchor'
    ],
    SO: [ // Somalia
        'Mogadishu',
        'Hargeisa',
        'Bosaso',
        'Galkayo',
        'Berbera',
        'Kismayo',
        'Merca',
        'Jamaame'
    ],
    SR: [ // Suriname
        'Paramaribo',
        'Lelydorp',
        'Nieuw Nickerie',
        'Moengo',
        'Albina',
        'Mariënburg',
        'Groningen',
        'Brownsweg'
    ],
    SS: [ // South Sudan
        'Juba',
        'Wau',
        'Malakal',
        'Bor',
        'Yei',
        'Yambio',
        'Aweil',
        'Rumbek'
    ],
    SV: [ // El Salvador
        'San Salvador',
        'Santa Ana',
        'San Miguel',
        'Mejicanos',
        'Apopa',
        'Delgado',
        'Soyapango',
        'Nueva San Salvador'
    ],
    SY: [ // Syria
        'Damascus',
        'Aleppo',
        'Homs',
        'Latakia',
        'Hama',
        'Deir ez-Zor',
        'Al-Hasakah',
        'Raqqa'
    ],
    SZ: [ // Eswatini (Swaziland)
        'Mbabane',
        'Manzini',
        'Big Bend',
        'Malkerns',
        'Nhlangano',
        'Piggs Peak',
        'Siteki',
        'Lobamba'
    ],
    TD: [ // Chad
        'N\'Djamena',
        'Moundou',
        'Sarh',
        'Abéché',
        'Kelo',
        'Koumra',
        'Am Timan',
        'Bongor'
    ],
    TG: [ // Togo
        'Lomé',
        'Sokodé',
        'Kara',
        'Kpalimé',
        'Atakpamé',
        'Bassar',
        'Tsévié',
        'Aného'
    ],
    TH: [ // Thailand
        'Bangkok',
        'Nonthaburi',
        'Pak Kret',
        'Hat Yai',
        'Chiang Mai',
        'Pattaya',
        'Udon Thani',
        'Nakhon Ratchasima'
    ],
    TJ: [ // Tajikistan
        'Dushanbe',
        'Khujand',
        'Kulob',
        'Bokhtar',
        'Istaravshan',
        'Vahdat',
        'Tursunzoda',
        'Isfara'
    ],
    TM: [ // Turkmenistan
        'Ashgabat',
        'Türkmenabat',
        'Daşoguz',
        'Mary',
        'Balkanabat',
        'Bayramaly',
        'Türkmenbaşy',
        'Tejen'
    ],
    TN: [ // Tunisia
        'Tunis',
        'Sfax',
        'Sousse',
        'Ettadhamen',
        'Kairouan',
        'Bizerte',
        'Gabès',
        'Ariana'
    ],
    TR: [ // Turkey
        'Istanbul',
        'Ankara',
        'Izmir',
        'Bursa',
        'Antalya',
        'Adana',
        'Konya',
        'Gaziantep'
    ],
    TT: [ // Trinidad and Tobago
        'Port of Spain',
        'San Fernando',
        'Chaguanas',
        'Arima',
        'Point Fortin',
        'Tunapuna',
        'Sangre Grande',
        'Couva'
    ],
    TW: [ // Taiwan
        'Taipei',
        'Kaohsiung',
        'Taichung',
        'Tainan',
        'Hsinchu',
        'Keelung',
        'Chiayi',
        'Changhua'
    ],
    TZ: [ // Tanzania
        'Dar es Salaam',
        'Mwanza',
        'Zanzibar City',
        'Arusha',
        'Mbeya',
        'Morogoro',
        'Tanga',
        'Dodoma'
    ],
    UA: [ // Ukraine
        'Kyiv',
        'Kharkiv',
        'Odesa',
        'Dnipro',
        'Donetsk',
        'Zaporizhzhia',
        'Lviv',
        'Kryvyi Rih'
    ],
    UG: [ // Uganda
        'Kampala',
        'Gulu',
        'Lira',
        'Mbarara',
        'Jinja',
        'Mbale',
        'Masaka',
        'Entebbe'
    ],
    US: [ // United States
        'New York City',
        'Los Angeles',
        'Chicago',
        'Houston',
        'Phoenix',
        'Philadelphia',
        'San Antonio',
        'San Diego'
    ],
    UY: [ // Uruguay
        'Montevideo',
        'Salto',
        'Paysandú',
        'Las Piedras',
        'Rivera',
        'Maldonado',
        'Tacuarembó',
        'Melo'
    ],
    UZ: [ // Uzbekistan
        'Tashkent',
        'Namangan',
        'Samarkand',
        'Andijan',
        'Nukus',
        'Bukhara',
        'Qarshi',
        'Fergana'
    ],
    VA: [ // Vatican City
        'Vatican City'
    ],
    VE: [ // Venezuela
        'Caracas',
        'Maracaibo',
        'Valencia',
        'Barquisimeto',
        'Maracay',
        'Ciudad Guayana',
        'Barcelona',
        'Maturín'
    ],
    VN: [ // Vietnam
        'Ho Chi Minh City',
        'Hanoi',
        'Da Nang',
        'Haiphong',
        'Can Tho',
        'Bien Hoa',
        'Hue',
        'Nha Trang'
    ],
    YE: [ // Yemen
        'Sanaa',
        'Aden',
        'Taiz',
        'Al Hudaydah',
        'Ibb',
        'Dhamar',
        'Al Mukalla',
        'Hajjah'
    ],
    ZA: [ // South Africa
        'Johannesburg',
        'Cape Town',
        'Durban',
        'Pretoria',
        'Port Elizabeth',
        'Bloemfontein',
        'East London',
        'Pietermaritzburg'
    ],
    ZM: [ // Zambia
        'Lusaka',
        'Kitwe',
        'Ndola',
        'Kabwe',
        'Chingola',
        'Mufulira',
        'Livingstone',
        'Luanshya'
    ],
    ZW: [ // Zimbabwe
        'Harare',
        'Bulawayo',
        'Chitungwiza',
        'Mutare',
        'Gweru',
        'Epworth',
        'Kwekwe',
        'Kadoma'
    ]
}
export default allCities