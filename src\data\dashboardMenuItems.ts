export interface SubMenuItem {
  id: number;
  title: string;
  getHref: (projectId: string) => string;
}

export interface MenuItem {
  id: number;
  title: string;
  getHref: (projectId: string) => string;
  subMenus?: SubMenuItem[];
}

export const MENU_ITEMS: MenuItem[] = [
  {
    id: 1,
    title: "Project Overview",
    getHref: (projectId) => `/project/${projectId}/project-overview`,
  },
  {
    id: 2,
    title: "Keywords Ranking",
    getHref: (projectId) => `/project/${projectId}/keywords-ranking`,
    subMenus: [
      {
        id: 1,
        title: "dummy title",
        getHref: (projectId) => `/project/${projectId}/keywords-ranking/dummy1`,
      },
      {
        id: 2,
        title: "dummy title",
        getHref: (projectId) => `/project/${projectId}/keywords-ranking/dummy2`,
      },
    ],
  },
  {
    id: 3,
    title: "Website Audit",
    getHref: (projectId) => `/project/${projectId}/website-audit`,
    subMenus: [
      {
        id: 1,
        title: "dummy title",
        getHref: (projectId) => `/project/${projectId}/website-audit/dummy1`,
      },
      {
        id: 2,
        title: "dummy title",
        getHref: (projectId) => `/project/${projectId}/website-audit/dummy2`,
      },
    ],
  },
  {
    id: 4,
    title: "Analytics & traffics",
    getHref: (projectId) => `/project/${projectId}/analytics-traffics/overview`,
    subMenus: [
      {
        id: 1,
        title: "Overview",
        getHref: (projectId) =>
          `/project/${projectId}/analytics-traffics/overview`,
      },
      {
        id: 2,
        title: "Analytic insight",
        getHref: (projectId) =>
          `/project/${projectId}/analytics-traffics/analytic-insight`,
      },
      {
        id: 3,
        title: "GSC Insight",
        getHref: (projectId) =>
          `/project/${projectId}/analytics-traffics/gsc-insight`,
      },
      {
        id: 4,
        title: "SEO Potential",
        getHref: (projectId) =>
          `/project/${projectId}/analytics-traffics/seo-potential`,
      },
    ],
  },
  {
    id: 5,
    title: "Backlinks",
    getHref: (projectId) => `/project/${projectId}/backlinks`,
    subMenus: [
      {
        id: 1,
        title: "dummy title",
        getHref: (projectId) => `/project/${projectId}/backlinks/dummy1`,
      },
      {
        id: 2,
        title: "dummy title",
        getHref: (projectId) => `/project/${projectId}/backlinks/dummy2`,
      },
    ],
  },
  {
    id: 6,
    title: "Competitors",
    getHref: (projectId) =>
      `/project/${projectId}/competitors/competitor-analysis`,
    subMenus: [
      {
        id: 1,
        title: "Competitor Analysis",
        getHref: (projectId) =>
          `/project/${projectId}/competitors/competitor-analysis`,
      },
      {
        id: 2,
        title: "SERP Competitors",
        getHref: (projectId) =>
          `/project/${projectId}/competitors/serp-competitors`,
      },
      {
        id: 3,
        title: "Share Of Voice",
        getHref: (projectId) =>
          `/project/${projectId}/competitors/share-of-voice`,
      },
      {
        id: 4,
        title: "Market Analysis",
        getHref: (projectId) =>
          `/project/${projectId}/competitors/market-analysis`,
      },
    ],
  },
  {
    id: 7,
    title: "Suggestions",
    getHref: (projectId) => `/project/${projectId}/suggestions`,
  },
];
