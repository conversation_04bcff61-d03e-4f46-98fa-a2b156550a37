import { useRef, useState } from "react";
import * as ExcelJS from "exceljs";
import TooltipPortal from "../ui/TooltipPortal";
interface ExcelUploaderProps {
  onAddKeyword: (k: string, val: boolean) => Promise<void>;
  buttonText?: string;
}

export const ExcelUploader: React.FC<ExcelUploaderProps> = ({
  onAddKeyword,
  buttonText = "Import From",
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setError(null);
    if (!file) return;
    if (file.size > MAX_FILE_SIZE) {
      setError("The file size should not be more than 5 MB.");
      return;
    }
    setIsLoading(true);
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const buffer = e.target?.result as ArrayBuffer;
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(buffer);

        // Get the first worksheet
        const worksheet = workbook.worksheets[0];
        if (!worksheet) {
          throw new Error("No worksheet found in the file");
        }

        const keywords: string[] = [];

        // Try to find keywords in the worksheet
        // First, check if there's a 'name' column header
        const headerRow = worksheet.getRow(1);
        let nameColumnIndex = -1;

        headerRow.eachCell((cell, colNumber) => {
          if (cell.value && cell.value.toString().toLowerCase() === "name") {
            nameColumnIndex = colNumber;
          }
        });

        if (nameColumnIndex > 0) {
          // Extract keywords from the 'name' column
          worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > 1) {
              // Skip header row
              const cell = row.getCell(nameColumnIndex);
              const value = cell.value;
              if (
                value &&
                typeof value === "string" &&
                value.trim().length > 0
              ) {
                keywords.push(value.trim());
              }
            }
          });
        } else {
          // Extract keywords from the first column
          worksheet.eachRow((row, rowNumber) => {
            const cell = row.getCell(1);
            const value = cell.value;
            if (value && typeof value === "string" && value.trim().length > 0) {
              keywords.push(value.trim());
            }
          });
        }

        // Process keywords in batches
        const batchSize = 20;
        for (let i = 0; i < keywords.length; i += batchSize) {
          const batch = keywords.slice(i, i + batchSize);
          await Promise.all(
            batch.map((keyword) => onAddKeyword(keyword, false))
          );
          await new Promise((resolve) => setTimeout(resolve, 50)); // Short pause
        }
      } catch (err) {
        setError("Error processing the file. Please check the file.");
        console.error(err);
      } finally {
        setIsLoading(false);
        if (inputRef.current) {
          inputRef.current.value = "";
        }
      }
    };
    reader.onerror = () => {
      setError("Error reading file. Please try again.");
      setIsLoading(false);
    };
    reader.readAsArrayBuffer(file);
  };
  const openFilePicker = () => {
    inputRef.current?.click();
  };
  return (
    <div className="text-left flex justify-start">
      <div className="flex flex-col gap-1">
        <TooltipPortal
          width="lg"
          content="Only Excel files with `.xlsx` or `.xls` formats are accepted. Please avoid uploading other file types."
        >
          <div>
            <input
              placeholder="s"
              ref={inputRef}
              type="file"
              accept=".xlsx, .xls"
              onChange={handleFileChange}
              className="hidden"
            />
            <button
              type="button"
              onClick={openFilePicker}
              disabled={isLoading}
              className="text-sm text-white font-medium bg-primary hover:bg-primary/90 px-3 py-2 rounded-md transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Importing..." : buttonText}
            </button>
          </div>
        </TooltipPortal>
        {error && <div className="mt-2 text-primary-red text-sm">{error}</div>}
      </div>
    </div>
  );
};
