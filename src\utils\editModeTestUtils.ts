/**
 * Utility functions for testing edit mode functionality
 * These functions help verify that edit mode state management works correctly
 */

import { useCreateProjectStore } from "@/store/createProjectStore";

export interface EditModeTestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Test if edit mode state is properly maintained
 */
export function testEditModeState(projectId: string): EditModeTestResult {
  const store = useCreateProjectStore.getState();

  const isEditModeSet = store.isEditMode;
  const editProjectIdSet = store.editProjectId === projectId;

  if (!isEditModeSet) {
    return {
      success: false,
      message: "Edit mode is not set in store",
      details: {
        isEditMode: store.isEditMode,
        editProjectId: store.editProjectId,
      },
    };
  }

  if (!editProjectIdSet) {
    return {
      success: false,
      message: "Edit project ID does not match",
      details: { expected: projectId, actual: store.editProjectId },
    };
  }

  return {
    success: true,
    message: "Edit mode state is correctly maintained",
  };
}

/**
 * Test if step completion is properly set for edit mode
 */
export function testStepCompletion(): EditModeTestResult {
  const store = useCreateProjectStore.getState();
  const { stepCompletion, projectInfo } = store;

  if (!projectInfo?.id) {
    return {
      success: false,
      message: "No project loaded in store",
      details: { projectInfo },
    };
  }

  // In edit mode, project information should always be complete
  if (!stepCompletion.projectInformation) {
    return {
      success: false,
      message: "Project information step should be complete in edit mode",
      details: { stepCompletion },
    };
  }

  return {
    success: true,
    message: "Step completion is properly configured for edit mode",
    details: { stepCompletion },
  };
}

/**
 * Test if navigation URLs include project_id parameter
 */
export function testNavigationUrls(projectId: string): EditModeTestResult {
  // This would need to be called from within a component that uses useEditNavigation
  // For now, we'll test the URL building logic
  const buildUrl = (
    path: string,
    isEditMode: boolean,
    editProjectId: string
  ) => {
    if (!isEditMode) return path;
    const separator = path.includes("?") ? "&" : "?";
    return `${path}${separator}project_id=${editProjectId}`;
  };

  const testPaths = [
    "/create-project/project-information",
    "/create-project/search-engines",
    "/create-project/keywords",
    "/create-project/competitors",
    "/create-project/analytics-services",
  ];

  const results = testPaths.map((path) => {
    const url = buildUrl(path, true, projectId);
    const hasProjectId = url.includes(`project_id=${projectId}`);
    return { path, url, hasProjectId };
  });

  const failedUrls = results.filter((r) => !r.hasProjectId);

  if (failedUrls.length > 0) {
    return {
      success: false,
      message: "Some navigation URLs are missing project_id parameter",
      details: { failedUrls, allResults: results },
    };
  }

  return {
    success: true,
    message: "All navigation URLs properly include project_id parameter",
    details: { results },
  };
}

/**
 * Test if search engine image URLs are using the correct path
 */
export function testSearchEngineImageUrls(): EditModeTestResult {
  const store = useCreateProjectStore.getState();
  const { searchEngineConfigs, keywords } = store;

  const incorrectUrls: string[] = [];

  // Check search engine configs
  searchEngineConfigs.forEach((config, index) => {
    if (config.searchEngine?.image) {
      if (!config.searchEngine.image.includes("/images/create-project/")) {
        incorrectUrls.push(
          `Search Engine Config ${index + 1} (${config.searchEngine.name}): ${
            config.searchEngine.image
          }`
        );
      }
    }
  });

  // Check keywords
  keywords.forEach((keyword, index) => {
    keyword.searchEngineConfigs?.forEach((config, configIndex) => {
      if (config.searchEngine?.image) {
        if (!config.searchEngine.image.includes("/images/create-project/")) {
          incorrectUrls.push(
            `Keyword ${index + 1} Config ${configIndex + 1} (${
              config.searchEngine.name
            }): ${config.searchEngine.image}`
          );
        }
      }
    });
  });

  if (incorrectUrls.length > 0) {
    return {
      success: false,
      message: "Some search engine image URLs are not using the correct path",
      details: { incorrectUrls },
    };
  }

  return {
    success: true,
    message: "All search engine image URLs are using the correct path",
  };
}

/**
 * Test if flag URLs are using the correct CDN format
 */
export function testFlagUrls(): EditModeTestResult {
  const store = useCreateProjectStore.getState();
  const { searchEngineConfigs, keywords, competitors } = store;

  const incorrectUrls: string[] = [];

  // Check search engine configs
  searchEngineConfigs.forEach((config, index) => {
    if (config.country?.image) {
      if (!config.country.image.includes("cdn.jsdelivr.net")) {
        incorrectUrls.push(
          `Search Engine Config ${index + 1} (${config.country.code}): ${
            config.country.image
          }`
        );
      } else if (!config.country.image.includes(config.country.code)) {
        incorrectUrls.push(
          `Search Engine Config ${index + 1} (${
            config.country.code
          }): URL doesn't match country code - ${config.country.image}`
        );
      }
    }
  });

  // Check keywords
  keywords.forEach((keyword, index) => {
    keyword.searchEngineConfigs?.forEach((config, configIndex) => {
      if (config.country?.image) {
        if (!config.country.image.includes("cdn.jsdelivr.net")) {
          incorrectUrls.push(
            `Keyword ${index + 1} Config ${configIndex + 1} (${
              config.country.code
            }): ${config.country.image}`
          );
        } else if (!config.country.image.includes(config.country.code)) {
          incorrectUrls.push(
            `Keyword ${index + 1} Config ${configIndex + 1} (${
              config.country.code
            }): URL doesn't match country code - ${config.country.image}`
          );
        }
      }
    });
  });

  // Check competitors
  competitors.forEach((competitor, index) => {
    if (competitor.country?.image) {
      if (!competitor.country.image.includes("cdn.jsdelivr.net")) {
        incorrectUrls.push(
          `Competitor ${index + 1} (${
            competitor.country?.code || "unknown"
          }): ${competitor.country.image}`
        );
      } else if (
        competitor.country.code &&
        !competitor.country.image.includes(competitor.country.code)
      ) {
        incorrectUrls.push(
          `Competitor ${index + 1} (${
            competitor.country.code
          }): URL doesn't match country code - ${competitor.country.image}`
        );
      }
    }
  });

  if (incorrectUrls.length > 0) {
    return {
      success: false,
      message: "Some flag URLs are not using the correct CDN format",
      details: { incorrectUrls },
    };
  }

  return {
    success: true,
    message: "All flag URLs are using the correct CDN format",
  };
}

/**
 * Test if sidebar navigation URLs include project_id parameter
 */
export function testSidebarNavigationUrls(
  projectId: string
): EditModeTestResult {
  // Check if current URL has project_id
  const currentUrl = window.location.href;
  const hasProjectIdInCurrentUrl = currentUrl.includes(
    `project_id=${projectId}`
  );

  if (!hasProjectIdInCurrentUrl) {
    return {
      success: false,
      message: "Current URL is missing project_id parameter",
      details: { currentUrl, expectedProjectId: projectId },
    };
  }

  // Test sidebar link generation logic
  const buildUrl = (
    path: string,
    isEditMode: boolean,
    editProjectId: string
  ) => {
    if (!isEditMode) return path;
    const separator = path.includes("?") ? "&" : "?";
    return `${path}${separator}project_id=${editProjectId}`;
  };

  const sidebarSteps = [
    "project-information",
    "search-engines",
    "keywords",
    "competitors",
    "analytics-services",
  ];

  const results = sidebarSteps.map((step) => {
    const path = `/create-project/${step}`;
    const url = buildUrl(path, true, projectId);
    const hasProjectId = url.includes(`project_id=${projectId}`);
    return { step, path, url, hasProjectId };
  });

  const failedUrls = results.filter((r) => !r.hasProjectId);

  if (failedUrls.length > 0) {
    return {
      success: false,
      message: "Some sidebar navigation URLs are missing project_id parameter",
      details: { failedUrls, allResults: results },
    };
  }

  return {
    success: true,
    message:
      "All sidebar navigation URLs properly include project_id parameter",
    details: { results },
  };
}

/**
 * Test if project data is properly loaded and transformed
 */
export function testProjectDataLoading(): EditModeTestResult {
  const store = useCreateProjectStore.getState();
  const { projectInfo, searchEngineConfigs, keywords, competitors } = store;

  if (!projectInfo?.id) {
    return {
      success: false,
      message: "Project info is not loaded",
      details: { projectInfo },
    };
  }

  // Check if required fields are present
  const requiredFields = ["name", "domain", "color", "id"];
  const missingFields = requiredFields.filter(
    (field) => !projectInfo[field as keyof typeof projectInfo]
  );

  if (missingFields.length > 0) {
    return {
      success: false,
      message: "Project info is missing required fields",
      details: { missingFields, projectInfo },
    };
  }

  return {
    success: true,
    message: "Project data is properly loaded and transformed",
    details: {
      projectInfo,
      configsCount: searchEngineConfigs.length,
      keywordsCount: keywords.length,
      competitorsCount: competitors.length,
    },
  };
}

/**
 * Test if current step is preserved during edit mode
 */
export function testCurrentStepPreservation(
  expectedStep: string
): EditModeTestResult {
  const store = useCreateProjectStore.getState();
  const { currentStep, isEditMode } = store;

  if (!isEditMode) {
    return {
      success: false,
      message: "Not in edit mode",
      details: { isEditMode, currentStep },
    };
  }

  if (currentStep !== expectedStep) {
    return {
      success: false,
      message: "Current step does not match expected step",
      details: { expected: expectedStep, actual: currentStep },
    };
  }

  return {
    success: true,
    message: "Current step is properly preserved in edit mode",
  };
}

/**
 * Run all edit mode tests
 */
export function runAllEditModeTests(
  projectId: string,
  expectedStep?: string
): EditModeTestResult[] {
  const tests = [
    testEditModeState(projectId),
    testStepCompletion(),
    testNavigationUrls(projectId),
    testSidebarNavigationUrls(projectId),
    testSearchEngineImageUrls(),
    testFlagUrls(),
    testProjectDataLoading(),
  ];

  if (expectedStep) {
    tests.push(testCurrentStepPreservation(expectedStep));
  }

  return tests;
}

/**
 * Console logger for test results (development only)
 */
export function logTestResults(results: EditModeTestResult[]): void {
  // Only log in development environment
  if (process.env.NODE_ENV !== "development") {
    return;
  }

  console.group("🧪 Edit Mode Test Results");

  results.forEach((result, index) => {
    const icon = result.success ? "✅" : "❌";
    console.log(`${icon} Test ${index + 1}: ${result.message}`);

    if (result.details && !result.success) {
      console.log("   Details:", result.details);
    }
  });

  const passedTests = results.filter((r) => r.success).length;
  const totalTests = results.length;

  console.log(`\n📊 Summary: ${passedTests}/${totalTests} tests passed`);
  console.groupEnd();
}
