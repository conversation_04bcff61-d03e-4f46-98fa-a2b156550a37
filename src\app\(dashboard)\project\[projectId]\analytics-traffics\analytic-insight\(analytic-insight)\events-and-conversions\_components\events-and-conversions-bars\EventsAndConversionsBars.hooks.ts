import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import type { EventsAndConversionsResponse } from "./EventsAndConversions.types";
import { useProjectId } from "@/hooks/useProjectId";

const useEventsAndConversionsBars = ({
  tab,
  filter,
}: {
  tab: string;
  filter: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["event-bars", projectId, tab, filter],
    queryFn: async (): Promise<EventsAndConversionsResponse> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/events-and-conversions/events-and-conversions-bars",
        {
          params: {
            projectId,
            tab,
            filter,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default useEventsAndConversionsBars;
