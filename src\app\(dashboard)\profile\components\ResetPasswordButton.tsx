"use client";

import React from "react";
import { motion } from "framer-motion";
import { LockClosedIcon } from "@heroicons/react/24/outline";

interface ResetPasswordButtonProps {
  onClick: () => void;
  isLoading: boolean;
  variant?: "rounded-md" | "rounded-sm";
}

export default function ResetPasswordButton({
  onClick,
  isLoading,
  variant = "rounded-md",
}: ResetPasswordButtonProps) {
  return (
    <motion.button
      onClick={onClick}
      disabled={isLoading}
      className={`text-sm bg-primary text-white hover:bg-primary/90 transition-colors flex items-center gap-2 px-3 py-2 ${variant} disabled:opacity-50`}
      type="button"
      title="Reset Password"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      {/* Icon with loading animation */}
      <motion.div
        animate={isLoading ? { rotate: 360 } : { rotate: 0 }}
        transition={
          isLoading
            ? {
                duration: 1,
                repeat: Infinity,
                ease: "linear",
              }
            : { duration: 0.2 }
        }
      >
        {isLoading ? (
          <svg
            className="w-4 h-4"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ) : (
          <LockClosedIcon className="w-5 h-5" />
        )}
      </motion.div>

      {/* Text with fade animation */}
      <motion.span
        key={isLoading ? "loading" : "idle"}
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.2 }}
      >
        {isLoading ? "Sending..." : "Reset Password"}
      </motion.span>
    </motion.button>
  );
}
