"use client";
import React, { useEffect, useState } from "react";
import { usePathname, useParams } from "next/navigation";

/* =============================== COMPONENTS =============================== */
import LogoMenu from "./_components/LogoMenu";
import DashboardTextSideMenu from "./_components/DashboardTextSideMenu";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== DATA ================================== */
import { MENU_ITEMS } from "@/data/dashboardMenuItems";
import { cn } from "@/utils/cn";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ========================================================================== */
const DashboardSidebar = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const pathname = usePathname();
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [textMenuOpen, setTextMenuOpen] = useState(true);
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const displayInRouts = pathname?.startsWith("/project");
  const params = useParams();
  const projectId = params?.projectId as string;

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handleOpenMenu = (id: number) => {
    setOpenMenuId((prev) => (prev === id ? null : id));
  };

  const findMenuIdByPath = (path: string): number | null => {
    for (const item of MENU_ITEMS) {
      if (item.getHref(projectId as string) === path) {
        return item.id;
      }

      if (item.subMenus) {
        for (const sub of item.subMenus) {
          if (sub.getHref(projectId as string) === path) {
            return item.id;
          }
        }
      }
    }

    return null;
  };

  useEffect(() => {
    if (pathname) {
      setOpenMenuId(findMenuIdByPath(pathname));
    }
  }, []);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (!displayInRouts) return;
  return (
    <div className={`sticky top-8 hidden lg:flex h-fit z-20 w-fit`}>
      <div
        className={cn(
          `flex bg-white h-fit rounded-l-xl rounded-br-xl p-4 gap-4 z-20 transition-all duration-200`,
          textMenuOpen ? "" : "rounded-xl delay-300"
        )}
        onClick={() => setTextMenuOpen((prev) => !prev)}
      >
        <LogoMenu themeColor={themeColor} />
      </div>

      <motion.div
        className={`z-20 flex flex-col items-center left-32 bg-white h-fit rounded-r-xl ${
          textMenuOpen && "border-l"
        } overflow-hidden`}
        initial={false}
        animate={{ width: textMenuOpen ? 200 : 0 }}
        transition={
          textMenuOpen
            ? { stiffness: 300, damping: 30, delay: 0.15 }
            : { stiffness: 300, damping: 30 }
        }
      >
        {MENU_ITEMS.map((item) => (
          <DashboardTextSideMenu
            href={item.getHref(projectId as string)}
            onClick={() => handleOpenMenu(item.id)}
            openMenu={openMenuId === item.id}
            key={item.id}
            themeColor={themeColor}
            title={item.title}
            subMenus={item.subMenus?.map(sub => ({
              id: sub.id,
              title: sub.title,
              href: sub.getHref(projectId as string)
            }))}
          />
        ))}
      </motion.div>
    </div>
  );
};

export default DashboardSidebar;
