import { BookIcon } from "@/ui/icons/general";
import CustomerSlider from "./CustomerSlider";
import { CustomerType } from "./OurCustomerType";

export default function TopSlider({ data }: CustomerType) {
  // const items = Array(8).fill({
  //   label: "Lorem Ipsum",
  //   description: "Lorem ipsum dolor sit consectetur",
  //   icon: <BookIcon />,
  // });

  return (
    <div className="w-full relative">
      <div className="flex items-center gap-6 -translate-x-[100px] animate-[our-customer-animate-top_35s_linear_infinite]">
        {[...data].map((item, index) => (
          <CustomerSlider
            key={index}
            label={item.label}
            description={item.content}
            icon={<BookIcon />}
          />
        ))}
      </div>
    </div>
  );
}
