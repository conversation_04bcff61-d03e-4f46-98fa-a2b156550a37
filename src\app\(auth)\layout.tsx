import "@/app/globals.css";

import nunitoSansFont from "@/constants/localFont";
import TopBg from "./components/TopBg";
import { Metadata } from "next";
import { ToastProvider } from "@/lib/ToastProvider";

export const metadata: Metadata = {
  title: "SEO Analyser | Authentication",
  description:
    "Our all-in-one SEO Analyser uncovers hidden issues, delivers actionable insights, and helps you rank higher, faster.",
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="auth-layout no-scrollbar-gutter">
      <body
        className={`${nunitoSansFont.variable} font-[family-name:var(--font-nunito-sans)] antialiased min-h-screen bg-gray-50 flex flex-col auth-page no-scrollbar-gutter`}
      >
        <TopBg />
        <main className="flex-grow flex flex-col justify-center items-center relative z-10">
          {children}
        </main>
        <footer className="text-center py-3 text-xs text-gray-500 relative z-10">
          © {new Date().getFullYear()} SEO Analyser. All rights reserved.
        </footer>
        <ToastProvider />
      </body>
    </html>
  );
}
