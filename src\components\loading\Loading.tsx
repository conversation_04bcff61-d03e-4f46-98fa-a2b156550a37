"use client";

import { useEffect, useRef, useMemo } from "react";

const DinoLoader: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const gameStateRef = useRef({
    gameLoopStarted: false,
    imagesLoaded: 0,
    totalImages: 3,
  });

  const imageDataUrls = useMemo(
    () => ({
      dino:
        "data:image/svg+xml;base64," +
        btoa(`<svg width="32" height="20" viewBox="0 0 32 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M26.5 3.5C26.5 3.5 25 5.5 24.5 5.5C17 0.5 15 2 11 5.5C6.0424 10.8709 3.3311 11.3306 0 9.5C3.3674 12.5193 6.3146 12.462 12 8.5C13.0221 8.6164 13.951 9.9228 16 13.5C16.5 14.5 16 17.5 16 17.5C15.8805 18.507 16.4641 18.6846 17.4968 18.999L17.5 19L23 20C23.3635 19.7958 23.4302 19.6892 23.5 19.5C23.5 19 22.5 18.5 22.5 18.5C21.3147 18.0432 20.6257 17.9397 19.5 18C17.9942 17.5635 17.5 16.5 17.5 16.5C19 15 20.0578 9.915 20.0584 8.7829C20 8.3582 20 7.5 20 8C20 8 20.0585 8.4931 20.0584 8.7829C20.1194 9.2273 20.2443 9.7443 20.5 10C21 10.5 22 10.5 22 10.5C23.5 11 20.6579 13.5033 20.5 14C20.3421 14.4967 24.6169 13.5665 23.5 10.1702C25.8428 9.7119 27.4146 6.9042 28 6.5C29 6.5 30.5 7 31 7C31.5 7 31.6533 6.7006 31.5 6C31.4192 5.7052 30.7519 5.0507 28.7992 3.2413C28.7992 3.2413 30 0.730211 28.7992 0.730211C28.3725 0.997761 28.0076 1.2698 27.5 2C27.5 2.5 27.7413 3.2413 27.5 3C27.4598 2.5542 26.1791 0.31789 26 0.5C25 0.5 24.5 3 26.5 3.5Z" fill="#989898"/>
</svg>`),
      cloud:
        "data:image/svg+xml;base64," +
        btoa(`<svg xmlns="http://www.w3.org/2000/svg" width="200px" fill="#fff " height="120px" viewBox="0 -64.71 306.67 306.67">
<g id="Layer_2" data-name="Layer 2">
<g id="Layer_1-2" data-name="Layer 1">
<path fill="#ccc" d="M41.28,83.23c1.26-3.3,2.46-6.2,3.5-9.16,4-11.39,13-16.23,24-18.13,3.34-.58,6.85-.22,10.29-.26,1.94,0,3.88,0,6.38,0,.25-4.44.71-8.36.63-12.28-.12-5.51,2.1-9.82,5.47-14C106.7,10.54,126.84,1.49,150.4.08c11.91-.71,23.08,3.37,33.37,9.52,9.66,5.78,16.42,14.08,21.2,24.14.72,1.52,1.6,3,2.64,4.88,10.77-2.21,21.08-2,30,5.6a44.11,44.11,0,0,1,15.49,27.37c2.33,0,4.26,0,6.18,0,5.7.06,11.38-.22,17,1.34,8.39,2.3,15.06,6.66,19.71,14.09,6.67,10.63,11,21.94,10.65,34.73-.31,11.72-5,20.84-15,27.4a67.82,67.82,0,0,1-25.27,10.22,218.2,218.2,0,0,1-23.67,2.75,38,38,0,0,1-10.19-1.18c-4.74-1-9,0-13.36,1.87-7.58,3.28-15.13,6.66-22.88,9.49A70.68,70.68,0,0,1,181.39,176a120.42,120.42,0,0,1-33.33.09c-10.09-1.48-18.93-6.24-27.31-11.86-1.65-1.11-3.32-2.19-5.28-3.5-2.93,1.68-5.78,3.5-8.79,5-2.82,1.4-5.77,2.52-8.71,3.64a57.71,57.71,0,0,1-18.44,3.66c-9.45.38-18.84.75-28.17-1.66-22.79-5.86-39.45-19.08-48.67-40.85-3.94-9.29-3.27-19.06.18-28.51a17.64,17.64,0,0,1,3.66-5.71C14.28,88,23.7,83.12,35.33,83.23Zm74.85,64.11c5.7,6.59,13,10.5,20.82,13.3,11.57,4.11,23.62,4.44,35.66,3.24,10.28-1,20.41-3,29.54-8.32,3.21-1.87,6.57-3.5,9.78-5.37a10.77,10.77,0,0,1,8.24-1.24c15.54,3.57,30.82,1.14,45.89-2.59,6.88-1.71,13.84-3.7,19.79-8,4.81-3.49,8.72-7.76,9.77-13.63,1.31-7.34,1.88-14.8-2.24-21.72a39.64,39.64,0,0,1-2.82-6.26C288,90,282.66,86.4,276.33,84A38.16,38.16,0,0,0,268,82c-7.15-1-14.29-1.24-21.14,1.71-2.16.93-3.92.23-5.35-1.45s-1.78-3.6,0-5.33,1.52-3.6,1.32-5.6c-1-9.85-6-17.08-14.47-22-2.54-1.48-5.05-2.48-8.1-2.08-2.81.37-5.8-.14-8.49.56-2.93.77-5.62,2.49-8.24,3.72-3.39-1.06-4.78-3.19-5.2-6.38-.35-2.78-.65-5.82-2-8.21-5.85-10.59-14.1-18.42-25.84-22.6a53,53,0,0,0-31.18-1.54A69.85,69.85,0,0,0,101.9,35.59a23.63,23.63,0,0,0-5.79,12.27c-.53,4-.71,8-1.18,12-.61,5.13-3,7-8,6.62-3.7-.27-7.46-1-11.12-.63-13.51,1.25-20.39,6.81-23.27,18.92-.33,1.38-.49,2.81-.8,4.2a10,10,0,0,1-4.48,6.75c-1.77-.38-4-.81-6.14-1.32-10.94-2.54-25.68,5.4-29.34,15.87a17.19,17.19,0,0,0-.75,3.34,30.5,30.5,0,0,0,8,26.11C28.4,150.08,40.12,156,53.62,158.91c10,2.19,19.91,1.31,29.91.31,8.14-.81,15.72-2.95,22.48-7.67A23.84,23.84,0,0,1,116.13,147.34Z"/>
</g>
</g>
</svg>`),
      obstacle:
        "data:image/svg+xml;base64," +
        btoa(`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#555" height="800px" width="800px" version="1.1" id="Capa_1" viewBox="0 0 470 470" xml:space="preserve">
<g>
<path d="M372.81,48.062c-22.594,0-40.976,18.382-40.976,40.976v77.608c0,23.154-16.968,42.411-39.121,46.01V57.713   C292.713,25.89,266.823,0,235,0s-57.713,25.89-57.713,57.713v221.893c-22.153-3.599-39.121-22.856-39.121-46.01v-77.608   c0-22.594-18.382-40.976-40.976-40.976c-22.594,0-40.976,18.381-40.976,40.976v77.608c0,68.376,53.655,124.458,121.072,128.356V455   h-22.5c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5h160.427c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5h-22.5V295.001   c67.417-3.898,121.072-59.979,121.072-128.356V89.037C413.786,66.443,395.404,48.062,372.81,48.062z M398.786,166.646   c0,62.624-50.948,113.572-113.572,113.572c-4.142,0-7.5,3.358-7.5,7.5V455h-85.427V354.669c0-4.142-3.358-7.5-7.5-7.5   c-62.624,0-113.572-50.948-113.572-113.572v-77.608c0-14.323,11.652-25.976,25.976-25.976c14.323,0,25.976,11.653,25.976,25.976   v77.608c0,31.438,23.67,57.434,54.121,61.148v12.973c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5V57.713   C192.287,34.161,211.448,15,235,15s42.713,19.161,42.713,42.713v183.053c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5v-12.973   c30.451-3.714,54.121-29.711,54.121-61.148V89.037c0-14.323,11.653-25.976,25.976-25.976s25.976,11.653,25.976,25.976V166.646z"/>
<path d="M235,400.013c-4.142,0-7.5,3.358-7.5,7.5v15c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5v-15   C242.5,403.371,239.142,400.013,235,400.013z"/>
<path d="M255.213,150.013c-4.142,0-7.5,3.358-7.5,7.5v15c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5v-15   C262.713,153.371,259.355,150.013,255.213,150.013z"/>
<path d="M214.787,100.013c-4.142,0-7.5,3.358-7.5,7.5v15c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5v-15   C222.287,103.371,218.929,100.013,214.787,100.013z"/>
<path d="M372.81,115.013c-4.142,0-7.5,3.358-7.5,7.5v15c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5v-15   C380.31,118.371,376.952,115.013,372.81,115.013z"/>
<path d="M97.19,205.013c-4.142,0-7.5,3.358-7.5,7.5v15c0,4.142,3.358,7.5,7.5,7.5s7.5-3.358,7.5-7.5v-15   C104.69,208.371,101.332,205.013,97.19,205.013z"/>
</g>
</svg>`),
    }),
    []
  );
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Enable transparency for the canvas
    const ctx = canvas.getContext("2d", {
      alpha: true,
      desynchronized: true,
    });
    if (!ctx) return;

    // Reset game state
    gameStateRef.current.gameLoopStarted = false;
    gameStateRef.current.imagesLoaded = 0;

    // Handle resize (dimensions fixed)
    const handleResize = () => {};
    window.addEventListener("resize", handleResize);
    handleResize();

    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const groundY = canvasHeight * 0.7;

    let dinoY = groundY;
    let velocityY = 0;
    const gravity = canvasHeight * 0.0018;
    const jumpStrength = -canvasHeight * 0.042;
    let isJumping = false;

    const dinoWidth = canvasWidth * 0.14;
    const dinoHeight = canvasHeight * 0.2;
    const obstacleWidth = canvasWidth * 0.09;
    const obstacleHeight = canvasHeight * 0.25;
    const obstacleSpeed = canvasWidth * 0.0055;

    const obstacles = [
      { x: canvasWidth, id: 1 },
      { x: canvasWidth + canvasWidth * 0.8, id: 2 },
    ];

    const dinoImage = new Image();
    const cloudImage = new Image();
    const obstacleImage = new Image();
    dinoImage.crossOrigin = "anonymous";
    cloudImage.crossOrigin = "anonymous";
    obstacleImage.crossOrigin = "anonymous";
    dinoImage.src = imageDataUrls.dino;
    cloudImage.src = imageDataUrls.cloud;
    obstacleImage.src = imageDataUrls.obstacle;

    const clouds = [
      { x: canvasWidth * 0.5, y: canvasHeight * 0.1, speed: canvasWidth * 0.0008 },
      { x: canvasWidth * 0.75, y: canvasHeight * 0.2, speed: canvasWidth * 0.0005 },
    ];

    const drawDino = () => {
      const dinoX = canvasWidth * 0.15;
      if (dinoImage.complete && dinoImage.naturalHeight !== 0) {
        ctx.drawImage(dinoImage, dinoX, dinoY, dinoWidth, dinoHeight);
      } else {
        ctx.fillStyle = "#666";
        ctx.fillRect(dinoX, dinoY, dinoWidth, dinoHeight);
      }
    };

    const drawObstacles = () => {
      obstacles.forEach((obstacle) => {
        if (obstacleImage.complete && obstacleImage.naturalHeight !== 0) {
          ctx.drawImage(
            obstacleImage,
            obstacle.x,
            groundY + dinoHeight - obstacleHeight,
            obstacleWidth,
            obstacleHeight
          );
        } else {
          ctx.fillStyle = "#888";
          ctx.fillRect(
            obstacle.x,
            groundY + dinoHeight - obstacleHeight,
            obstacleWidth,
            obstacleHeight
          );
        }
      });
    };

    const drawGround = () => {
      ctx.strokeStyle = "#ccc";
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(0, groundY + dinoHeight);
      ctx.lineTo(canvasWidth, groundY + dinoHeight);
      ctx.stroke();
    };

    const drawClouds = () => {
      const cloudWidth = canvasWidth * 0.25;
      const cloudHeight = canvasHeight * 0.28;

      clouds.forEach((cloud) => {
        if (cloudImage.complete && cloudImage.naturalHeight !== 0) {
          ctx.drawImage(cloudImage, cloud.x, cloud.y, cloudWidth, cloudHeight);
        } else {
          ctx.fillStyle = "#ddd";
          ctx.beginPath();
          ctx.ellipse(
            cloud.x + cloudWidth / 2,
            cloud.y + cloudHeight / 2,
            cloudWidth / 2,
            cloudHeight / 2,
            0,
            0,
            2 * Math.PI
          );
          ctx.fill();
        }
        cloud.x -= cloud.speed;
        if (cloud.x < -cloudWidth) {
          cloud.x = canvasWidth + Math.random() * canvasWidth * 0.1;
          cloud.y = canvasHeight * 0.05 + Math.random() * canvasHeight * 0.15;
        }
      });
    };

    const jump = () => {
      if (!isJumping) {
        velocityY = jumpStrength * 1.1;
        isJumping = true;
        setTimeout(() => {
          if (isJumping && velocityY < 0) velocityY = jumpStrength;
        }, 50);
      }
    };

    const gameLoop = () => {
      // Clear entire canvas for full transparency
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      velocityY += gravity;
      dinoY += velocityY;
      if (dinoY > groundY) {
        dinoY = groundY;
        velocityY = 0;
        isJumping = false;
      }

      const dinoX = canvasWidth * 0.15;
      const dinoRightEdge = dinoX + dinoWidth;
      const jumpTriggerStart = canvasWidth * 0.35;
      const jumpTriggerEnd = canvasWidth * 0.25;

      obstacles.forEach((obstacle) => {
        const distanceToObstacle = obstacle.x - dinoRightEdge;
        if (
          obstacle.x < jumpTriggerStart &&
          obstacle.x > jumpTriggerEnd &&
          !isJumping &&
          distanceToObstacle > 0
        ) {
          jump();
        }
      });

      obstacles.forEach((obstacle, index) => {
        obstacle.x -= obstacleSpeed;
        if (obstacle.x < -obstacleWidth) {
          const otherObstacles = obstacles.filter((_, i) => i !== index);
          const furthestX = Math.max(...otherObstacles.map((o) => o.x));
          obstacle.x = furthestX + canvasWidth * 0.7 + Math.random() * canvasWidth * 0.2;
        }
      });

      drawClouds();
      drawGround();
      drawDino();
      drawObstacles();

      animationFrameRef.current = requestAnimationFrame(gameLoop);
    };

    // Start loop
    if (!gameStateRef.current.gameLoopStarted) {
      gameStateRef.current.gameLoopStarted = true;
      gameLoop();
    }

    // Image load handlers
    const handleImageLoad = () => {
      gameStateRef.current.imagesLoaded++;
      if (gameStateRef.current.imagesLoaded === gameStateRef.current.totalImages) {
        console.log("All images loaded");
      }
    };
    const handleImageError = () => console.warn("Image failed to load");
    dinoImage.onload = handleImageLoad;
    cloudImage.onload = handleImageLoad;
    obstacleImage.onload = handleImageLoad;
    dinoImage.onerror = handleImageError;
    cloudImage.onerror = handleImageError;
    obstacleImage.onerror = handleImageError;
    [dinoImage, cloudImage, obstacleImage].forEach((img) => {
      if (img.complete) handleImageLoad();
    });

    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize);
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      gameStateRef.current.gameLoopStarted = false;
    };
  }, [imageDataUrls]);

  return (
    <div className="w-full flex justify-center items-center py-2 lg:py-6">
      <div className="w-full max-w-4xl">
        <canvas
          ref={canvasRef}
          width={900}
          height={400}
          className="rounded-lg max-w-full h-auto"
          style={{
            width: "100%",
            maxWidth: "900px",
            height: "auto",
            minHeight: "220px",
            transition: "all 0.3s ease",
            willChange: "transform",
            transform: "translateZ(0)",
          }}
        />
      </div>
    </div>
  );
};

export default DinoLoader;
 
