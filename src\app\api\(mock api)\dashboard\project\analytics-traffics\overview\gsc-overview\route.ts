import { NextResponse } from "next/server";
import type { GSCOverviewType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/gsc-overview/GSCOverview.types";

const GSC_OVERVIEW_DATA: GSCOverviewType = {
  cardsData: {
    clicks: { amount: 12000, growth: "+21.2%" },
    dotted_clicks: { amount: 12000, growth: "+21.2%" },
    CTR: { amount: 12000, growth: "0" },
    dotted_CTR: { amount: 12000, growth: "0" },
    Search_Visibility: { amount: 12000, growth: "+21.2%" },
    dotted_Search_Visibility: { amount: 12000, growth: "+21.2%" },
    Impression: { amount: 12000, growth: "+16%" },
    dotted_Impression: { amount: 12000, growth: "+16%" },
    Avg_Position: { amount: 12000, growth: "-5%" },
    dotted_Avg_Position: { amount: 12000, growth: "-5%" },
  },
  lineChartData: [
    {
      name: "May",
      clicks: 3000,
      dotted_clicks: 3000,
      CTR: 1398,
      dotted_CTR: 1398,
      Search_Visibility: 1400,
      dotted_Search_Visibility: 1400,
      Impression: 3110,
      dotted_Impression: 3110,
      Avg_Position: 3510,
      dotted_Avg_Position: 3510,
    },
    {
      name: "Apr",
      clicks: 4000,
      dotted_clicks: 4300,
      CTR: 2400,
      dotted_CTR: 2400,
      Search_Visibility: 2400,
      dotted_Search_Visibility: 2400,
      Impression: 3100,
      dotted_Impression: 2100,
      Avg_Position: 2110,
      dotted_Avg_Position: 2110,
    },
    {
      name: "Jun",
      clicks: 2000,
      dotted_clicks: 2200,
      CTR: 9800,
      dotted_CTR: 9800,
      Search_Visibility: 2290,
      dotted_Search_Visibility: 2290,
      Impression: 2190,
      dotted_Impression: 4190,
      Avg_Position: 4510,
      dotted_Avg_Position: 4510,
    },
    {
      name: "Jul",
      clicks: 3000,
      dotted_clicks: 1000,
      CTR: 1398,
      dotted_CTR: 1398,
      Search_Visibility: 2210,
      dotted_Search_Visibility: 2210,
      Impression: 1110,
      dotted_Impression: 1110,
      Avg_Position: 1410,
      dotted_Avg_Position: 1410,
    },
    {
      name: "Aug",
      clicks: 3000,
      dotted_clicks: 3000,
      CTR: 1398,
      dotted_CTR: 1398,
      Search_Visibility: 2210,
      dotted_Search_Visibility: 2210,
      Impression: 3110,
      dotted_Impression: 3110,
      Avg_Position: 3510,
      dotted_Avg_Position: 3510,
    },
    {
      name: "Sep",
      clicks: 4000,
      dotted_clicks: 4000,
      CTR: 2400,
      dotted_CTR: 2400,
      Search_Visibility: 2400,
      dotted_Search_Visibility: 2400,
      Impression: 1100,
      dotted_Impression: 1100,
      Avg_Position: 1210,
      dotted_Avg_Position: 1210,
    },
  ],
};

export async function GET() {
  return NextResponse.json(GSC_OVERVIEW_DATA);
}
