import { NextResponse } from "next/server";
import type { BarsData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";

const barsData: BarsData = {
  maxValue: 30_000,
  bars: [
    {
      label: "Organic Traffics",
      barData: [
        { value: 4070, color: "bg-primary" },
        { value: 3000, color: "bg-primary-orange" },
      ],
    },
    {
      label: "Paid Traffics",
      barData: [
        { value: 2000, color: "bg-primary-orange" },
        { value: 1000, color: "bg-primary" },
      ],
    },
    {
      label: "Organic Traffics",
      barData: [
        { value: 4070, color: "bg-primary" },
        { value: 3000, color: "bg-primary-orange" },
      ],
    },
    {
      label: "Paid Traffics",
      barData: [
        { value: 2000, color: "bg-primary-orange" },
        { value: 1000, color: "bg-primary" },
      ],
    },
  ],
};

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  return NextResponse.json(barsData);
}
