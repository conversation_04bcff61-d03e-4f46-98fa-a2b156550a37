/* =============================== REACT QUERY ============================== */
import { useQuery } from "@tanstack/react-query";

/* ================================== TYPES ================================= */
import type { ChartResponse } from "./AudienceOverview.types";
import { useProjectId } from "@/hooks/useProjectId";

/* ================================= ZUSTAND ================================ */
import { useDateRangeStore } from "@/store/useDateRangeStore";

/* ================================ TEST DATA =============================== */
import { AUDIENCE_OVERVIEW_TEST_DATA } from "./AudienceOverview.testData";
import { transformAudienceOverviewData } from "./AudienceOverview.utils";

/* ========================================================================== */
export const useAudienceOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates } = useDateRangeStore();

  // Get formatted dates for API call
  const { startDate, endDate } = getFormattedDates();

  return useQuery({
    queryKey: ["charts-data", projectId, startDate, endDate],
    queryFn: async (): Promise<ChartResponse[]> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // For testing: Use test data instead of API call
      // TODO: Replace with actual API call when ready
      console.log("Using test data for audience overview");

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Transform test data to chart format
      const transformedData = transformAudienceOverviewData(
        AUDIENCE_OVERVIEW_TEST_DATA
      );
      return transformedData;

      // Original API call (commented out for testing)
      /*
      const { data } = await AXIOS.get(
        `/api/project/GA4/audience/overview/${projectId}/`,
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
        }
      );
      return transformAudienceOverviewData(data);
      */
    },
    enabled: isValidProjectId && !!projectId,
    refetchOnMount: false,
    staleTime: 1000 * 60 * 5,
  });
};
