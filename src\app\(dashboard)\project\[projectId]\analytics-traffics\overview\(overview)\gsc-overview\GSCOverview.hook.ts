import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { GSCOverviewType } from "./GSCOverview.types";
import { useProjectId } from "@/hooks/useProjectId";

export const useGSCOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["gsc-overview-data", projectId],
    queryFn: async (): Promise<GSCOverviewType> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/overview/gsc-overview",
        {
          params: {
            projectId,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};
