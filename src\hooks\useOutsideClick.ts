import { useEffect, useRef, RefObject } from "react";

type Handler = (event: MouseEvent) => void;

export default function useOutsideClick<T extends HTMLElement = HTMLDivElement>(
  handler: Handler,
  listenCapturing: boolean = true,
  shouldPreventClose?: () => boolean
): RefObject<T> {
  const ref = useRef<T>(null!); // اضافه کردن non-null assertion

  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (!ref.current || ref.current.contains(e.target as Node)) return;

      // Check if we should prevent closing
      if (shouldPreventClose && shouldPreventClose()) {
        return;
      }

      handler(e);
    };

    document.addEventListener("click", handleClick, listenCapturing);

    return () => {
      document.removeEventListener("click", handleClick, listenCapturing);
    };
  }, [handler, listenCapturing, shouldPreventClose]);

  return ref;
}
