/**
 * Error handling utilities
 * This module provides functions to handle errors consistently across the application
 */

// Define error types
export enum ErrorType {
  AUTHENTICATION = "authentication",
  VALIDATION = "validation",
  SERVER = "server",
  NETWORK = "network",
  UNKNOWN = "unknown",
}

// Define error severity levels
export enum ErrorSeverity {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
  CRITICAL = "critical",
}

// Define error interface
export interface AppError {
  type: ErrorType;
  message: string;
  severity: ErrorSeverity;
  details?: any;
  fieldErrors?: Record<string, string[]>;
}

// Enhanced error type for better error handling
export interface AuthError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

/**
 * Format authentication errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatAuthError(error: any): AppError {
  // Default error
  const defaultError: AppError = {
    type: ErrorType.AUTHENTICATION,
    message: "Authentication failed. Please try again.",
    severity: ErrorSeverity.ERROR,
  };

  // If the error is already formatted, return it
  if (error && error.type === ErrorType.AUTHENTICATION) {
    return error;
  }

  // If the error is an API response
  if (error && error.statusCode) {
    // Handle field errors
    if (error.fieldErrors) {
      return {
        type: ErrorType.VALIDATION,
        message: "Please correct the errors below.",
        severity: ErrorSeverity.WARNING,
        fieldErrors: error.fieldErrors,
      };
    }

    // Handle error message
    if (error.error) {
      return {
        type: ErrorType.AUTHENTICATION,
        message: error.error,
        severity: ErrorSeverity.ERROR,
        details: error,
      };
    }
  }

  // If the error is a string
  if (typeof error === "string") {
    return {
      type: ErrorType.AUTHENTICATION,
      message: error,
      severity: ErrorSeverity.ERROR,
    };
  }

  // If the error is an Error object
  if (error instanceof Error) {
    return {
      type: ErrorType.AUTHENTICATION,
      message: error.message,
      severity: ErrorSeverity.ERROR,
      details: error,
    };
  }

  // Return default error
  return defaultError;
}

/**
 * Format validation errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatValidationError(error: any): AppError {
  // Default error
  const defaultError: AppError = {
    type: ErrorType.VALIDATION,
    message: "Validation failed. Please check your input.",
    severity: ErrorSeverity.WARNING,
  };

  // If the error is already formatted, return it
  if (error && error.type === ErrorType.VALIDATION) {
    return error;
  }

  // If the error has field errors
  if (error && error.fieldErrors) {
    return {
      type: ErrorType.VALIDATION,
      message: "Please correct the errors below.",
      severity: ErrorSeverity.WARNING,
      fieldErrors: error.fieldErrors,
    };
  }

  // Return default error
  return defaultError;
}

/**
 * Format server errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatServerError(error: any): AppError {
  // Default error
  const defaultError: AppError = {
    type: ErrorType.SERVER,
    message: "Server error. Please try again later.",
    severity: ErrorSeverity.ERROR,
  };

  // If the error is already formatted, return it
  if (error && error.type === ErrorType.SERVER) {
    return error;
  }

  // If the error is an API response
  if (error && error.statusCode) {
    if (error.statusCode >= 500) {
      return {
        type: ErrorType.SERVER,
        message: error.error || "Server error. Please try again later.",
        severity: ErrorSeverity.ERROR,
        details: error,
      };
    }
  }

  // Return default error
  return defaultError;
}

/**
 * Format network errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatNetworkError(error: any): AppError {
  return {
    type: ErrorType.NETWORK,
    message: "Network error. Please check your connection and try again.",
    severity: ErrorSeverity.WARNING,
    details: error,
  };
}

/**
 * Format unknown errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatUnknownError(error: any): AppError {
  return {
    type: ErrorType.UNKNOWN,
    message: "An unexpected error occurred. Please try again.",
    severity: ErrorSeverity.ERROR,
    details: error,
  };
}

/**
 * Format any error
 * @param error The error object
 * @returns Formatted error object
 */
export function formatError(error: any): AppError {
  // If the error is already formatted, return it
  if (error && error.type && Object.values(ErrorType).includes(error.type)) {
    return error as AppError;
  }

  // If the error is an API response with a status code
  if (error && error.statusCode) {
    if (error.statusCode === 401) {
      return formatAuthError(error);
    } else if (error.statusCode === 400 || error.statusCode === 422) {
      return formatValidationError(error);
    } else if (error.statusCode >= 500) {
      return formatServerError(error);
    }
  }

  // If the error is a network error
  if (error && error.message && error.message.includes("Network")) {
    return formatNetworkError(error);
  }

  // Default to unknown error
  return formatUnknownError(error);
}

/**
 * Process API errors into a structured format
 * @param error The error object from an API call
 * @returns Structured error object or array of error objects
 */
export function processApiError(error: any): AuthError | AuthError[] {
  // Default error
  const defaultError: AuthError = {
    code: "unknown_error",
    message: "An unexpected error occurred. Please try again.",
  };

  // If no error, return default
  if (!error) return defaultError;

  // If error is already in our format, return it
  if (error.code && error.message) {
    return error as AuthError;
  }

  // Handle Axios error responses
  if (error.response) {
    const { status, data } = error.response;

    // Handle different status codes
    switch (status) {
      case 400: // Bad Request
        // Check for field validation errors
        if (data.fieldErrors || data.errors) {
          const fieldErrors = data.fieldErrors || data.errors;
          const errors: AuthError[] = [];

          // Process each field error
          for (const [field, messages] of Object.entries(fieldErrors)) {
            if (Array.isArray(messages)) {
              messages.forEach((message) => {
                errors.push({
                  code: "validation_error",
                  message,
                  field,
                });
              });
            } else if (typeof messages === "string") {
              errors.push({
                code: "validation_error",
                message: messages,
                field,
              });
            }
          }

          return errors.length > 0
            ? errors
            : [
                {
                  code: "validation_error",
                  message: "Please check your input and try again.",
                },
              ];
        }

        // General bad request
        return {
          code: "bad_request",
          message:
            data.detail ||
            data.message ||
            "Invalid request. Please check your input.",
        };

      case 401: // Unauthorized
        return {
          code: "unauthorized",
          message:
            data.detail ||
            data.message ||
            "Authentication required. Please log in.",
        };

      case 403: // Forbidden
        return {
          code: "forbidden",
          message:
            data.detail ||
            data.message ||
            "You do not have permission to perform this action.",
        };

      case 404: // Not Found
        return {
          code: "not_found",
          message:
            data.detail ||
            data.message ||
            "The requested resource was not found.",
        };

      case 429: // Too Many Requests
        return {
          code: "rate_limited",
          message:
            data.detail ||
            data.message ||
            "Too many requests. Please try again later.",
        };

      case 500: // Server Error
      case 502: // Bad Gateway
      case 503: // Service Unavailable
      case 504: // Gateway Timeout
        return {
          code: "server_error",
          message:
            data.detail ||
            data.message ||
            "Server error. Please try again later.",
        };

      default:
        return {
          code: `http_${status}`,
          message:
            data.detail ||
            data.message ||
            "An error occurred. Please try again.",
        };
    }
  }

  // Handle network errors
  if (error.message && error.message.includes("Network Error")) {
    return {
      code: "network_error",
      message: "Network error. Please check your connection and try again.",
    };
  }

  // Handle timeout errors
  if (
    error.code === "ECONNABORTED" ||
    (error.message && error.message.includes("timeout"))
  ) {
    return {
      code: "timeout",
      message: "Request timed out. Please try again.",
    };
  }

  // Handle other error types
  if (error.error || error.message) {
    return {
      code: "api_error",
      message: error.error || error.message,
    };
  }

  // If we can't determine the error type, return default
  return defaultError;
}

export default {
  formatError,
  formatAuthError,
  formatValidationError,
  formatServerError,
  formatNetworkError,
  formatUnknownError,
  processApiError,
};
