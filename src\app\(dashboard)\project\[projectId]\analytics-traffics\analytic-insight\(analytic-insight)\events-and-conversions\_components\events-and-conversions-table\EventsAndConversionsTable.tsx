"use client";
import React, { useRef, useState } from "react";

/* ============================== FRAMER MOTION ============================= */
import { AnimatePresence, motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import usePagesTable from "./EventsAndConversionsTable.hooks";
import NoData from "../../../../_components/NoData";

/* ========================================================================== */
const PagesReportsTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const badges = ["Events", "Conversions"];
  const [filterBy, setFilterBy] = useState(badges ? badges[0] : "");

  const { data, isLoading, isError } = usePagesTable({
    page,
    filterBy,
  });

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return isError ? (
    <motion.div
      key="error"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title="All Reports-Events & Conversions" />
    </motion.div>
  ) : data?.tableData || isLoading ? (
    <Card className="w-full space-y-10 min-h-[520px] flex flex-col justify-between">
      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <DataTable
          title="All Reports-Events & Conversions"
          tableData={data?.tableData}
          isLoading={isLoading}
          badges={badges}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={stableData?.pagination.initialPage || 1}
        onPageChange={setPage}
      />
    </Card>
  ) : (
    <motion.div
      key="nodata"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title="All Reports-Events & Conversions" />
    </motion.div>
  );
};
export default PagesReportsTable;
