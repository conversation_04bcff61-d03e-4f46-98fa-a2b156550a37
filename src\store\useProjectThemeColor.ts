import { create } from "zustand";
import { persist } from "zustand/middleware";

interface ProjectThemeColorState {
  themeColor: string;
  setThemeColor: (color: string) => void;
}

export const useProjectThemeColor = create<ProjectThemeColorState>()(
  persist(
    (set) => ({
      themeColor: "#914AC4",
      setThemeColor: (color: string) => set({ themeColor: color }),
    }),
    {
      name: "project-theme-color",
    }
  )
);
