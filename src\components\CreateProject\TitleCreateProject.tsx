import React from "react";
type TitleCreateProjectType = {
  head: string;
  description?: string;
  classDiv?: string;
  classHead?: string;
  classP?: string;
};
export default function TitleCreateProject({
  head,
  description,
  classHead,
  classP,
  classDiv,
}: TitleCreateProjectType) {
  return (
    <div className={`flex flex-col gap-3 mb-6 ${classDiv}`}>
      <h3
        className={`text-base md:text-lg lg:text-xl font-semibold tracking-tight leading-tight text-[#344054] ${classHead}`}
      >
        {head}
      </h3>
      {description && (
        <p
          className={`text-[#344054] text-sm md:text-base leading-relaxed max-w-3xl ${classP}`}
        >
          {description}
        </p>
      )}
    </div>
  );
}
