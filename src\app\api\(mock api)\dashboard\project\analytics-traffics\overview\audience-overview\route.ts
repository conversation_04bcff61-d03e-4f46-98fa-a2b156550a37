import { NextResponse } from "next/server";
import type { ChartResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/audience-overview/AudienceOverview.types";

const CHARTS_DATA: ChartResponse[] = [
  {
    id: 1,
    title: "Total Users",
    bigNumber: "12.3K",
    smallNumber: "+16%",
    data: [
      { name: "<PERSON>", value: 400 },
      { name: "Feb", value: 300 },
      { name: "<PERSON>", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 2,
    title: "New Users",
    bigNumber: "12.3K",
    smallNumber: "+16%",
    data: [
      { name: "<PERSON>", value: 400 },
      { name: "Feb", value: 300 },
      { name: "<PERSON>", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 3,
    title: "Returning Users",
    bigNumber: "10.3K",
    smallNumber: "+16%",
    data: [
      { name: "<PERSON>", value: 400 },
      { name: "Feb", value: 300 },
      { name: "<PERSON>", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 4,
    title: "Views",
    bigNumber: "523",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 5,
    title: "Sessions",
    bigNumber: "12.31K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 6,
    title: "Engaged Sessions",
    bigNumber: "12.5k",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 7,
    title: "Avg.Engagement time",
    bigNumber: "01m:09s",
    smallNumber: "00s",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 8,
    title: "Engagement Rate",
    bigNumber: "12.5K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 9,
    title: "Event Count",
    bigNumber: "12.5K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 10,
    title: "Conversions",
    bigNumber: "12.5K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
];

export async function GET() {
  return NextResponse.json(CHARTS_DATA);
}
