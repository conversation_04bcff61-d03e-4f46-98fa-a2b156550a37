"use client";
import BoxDashboard from "@/components/dashboard/BoxDashboard";
import TitlePageDashboard from "@/components/dashboard/TitlePageDashboard";
import { useSearchParams } from "next/navigation";
import { BiCopy } from "react-icons/bi";
import React, { Suspense } from "react";
import ProgressPercent from "@/app/(root)/audit/[urlName]/_/components/ProgressPercent";
import Table from "@/ui/Table";
import { FaCheck } from "react-icons/fa6";
import { MdOutlineClose } from "react-icons/md";
import { AiOutlineExclamationCircle } from "react-icons/ai";
import { ReactDateRangeComponent } from "@/components/dashboard/ReactDateRangeComponent";

// Component that uses useSearchParams
function AffiliateProgramContent() {
  const query = useSearchParams();

  const CopyButtun = () => {
    const text =
      "http://Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua";
    return (
      <div className="flex gap-7 items-center justify-between">
        <div className="w-10/12">
          <p className="bg-blue-100/60 text-blue-500 p-3 rounded-md">{text}</p>
        </div>
        <button
          onClick={async () => {
            await navigator.clipboard.writeText(text);
          }}
          type="button"
          className="btn btn--primary flex items-center justify-center"
        >
          <BiCopy className="text-lg" />
          Copy Link
        </button>
      </div>
    );
  };

  const TableComponent = () => {
    const dataTables = [
      {
        Keyword: "seoptimer",
        status1: "false",
        status2: "true",
        status3: "false",
        number: 34,
        percent: 70,
      },
      {
        Keyword: "seo",
        status1: "true",
        status2: "true",
        status3: "true",
        number: 9,
        percent: 50,
      },
      {
        Keyword: "seoptimer",
        status1: "false",
        status2: "true",
        status3: "true",
        number: 10,
        percent: 30,
      },
      {
        Keyword: "reports",
        status1: "false",
        status2: "true",
        status3: "true",
        number: 18,
        percent: 35,
      },
      {
        Keyword: "website",
        status1: "true",
        status2: "true",
        status3: "false",
        number: 3,
        percent: 10,
      },
      {
        Keyword: "rank",
        status1: "true",
        status2: "true",
        status3: "false",
        number: 22,
        percent: 70,
      },
      {
        Keyword: "tool",
        status1: "true",
        status2: "false",
        status3: "true",
        number: 20,
        percent: 50,
      },
      {
        Keyword: "front",
        status1: "true",
        status2: "false",
        status3: "false",
        number: 16,
        percent: 25,
      },
      {
        Keyword: "site",
        status1: "true",
        status2: "false",
        status3: "true",
        number: 12,
        percent: 38,
      },
    ];
    return (
      <>
        <div className="flex justify-between items-center">
          <TitlePageDashboard value="Detailed statistics" />
          <ReactDateRangeComponent
            months={2}
            showPreview={true}
            showDateDisplay={false}
          />
        </div>
        <Table>
          <Table.Header>
            <th className="pb-4 whitespace-nowrap">Keyword</th>
            <th className="text-center pb-4">Title</th>
            <th className="text-center pb-4">Title</th>
            <th className="text-center pb-4">Lorem ipsum</th>
            <th className="text-center pb-4">Lorem ipsum</th>
            <th className="text-center pb-4">Lorem ipsum</th>
          </Table.Header>
          <Table.Body>
            {dataTables.map((item, index) => (
              <Table.Row key={index}>
                <td className="">{item.Keyword}</td>
                <td className="text-center">
                  {item.status1 === "true" ? (
                    <FaCheck className="text-green-400 mx-auto" />
                  ) : (
                    <MdOutlineClose className="text-red-400 mx-auto" />
                  )}{" "}
                </td>
                <td className="text-center">
                  {item.status2 === "true" ? (
                    <FaCheck className="text-green-400 mx-auto" />
                  ) : (
                    <MdOutlineClose className="text-red-400 mx-auto" />
                  )}
                </td>
                <td className="text-center">
                  {item.status3 === "true" ? (
                    <FaCheck className="text-green-400 mx-auto" />
                  ) : (
                    <MdOutlineClose className="text-red-400 mx-auto" />
                  )}
                </td>
                <td className="text-center">{item.number}</td>
                <td className=" lg:pl-[85px]">
                  <div className="flex justify-start">
                    <ProgressPercent percentage={item.percent} />
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </>
    );
  };

  switch (query?.get("step")) {
    case "statistics":
      return (
        <>
          <TitlePageDashboard value="Statistics" />
          <BoxDashboard>
            <CopyButtun />
          </BoxDashboard>
          <BoxDashboard>
            <div className="flex gap-5 justify-between">
              <div className="flex flex-col gap-1 justify-center items-center">
                <span className="text-gray-400">Total transactions</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex flex-col gap-1 justify-center items-center">
                <span className="text-gray-400">Trials</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex flex-col gap-1 justify-center items-center">
                <span className="text-gray-400">Total earned to date</span>
                <span className="font-semibold">$0</span>
              </div>
              <div className="flex flex-col gap-1 justify-center items-center">
                <span className="text-gray-400">Unique visitors</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex flex-col gap-1 justify-center items-center">
                <span className="text-gray-400">Unique visitors</span>
                <span className="font-semibold">0</span>
              </div>
            </div>
          </BoxDashboard>
          <BoxDashboard classPlus="mt-5">
            <TableComponent />
          </BoxDashboard>
        </>
      );
    case "marketing-materials":
      return (
        <>
          <TitlePageDashboard value="Marketing Materials" />
          <BoxDashboard>
            <CopyButtun />
          </BoxDashboard>
          <BoxDashboard classPlus="mt-5">
            <TableComponent />
          </BoxDashboard>
        </>
      );
    case "commissions":
      return (
        <>
          <TitlePageDashboard value="Commissions" />
          <BoxDashboard>
            <CopyButtun />
          </BoxDashboard>
          <BoxDashboard classPlus="mt-5">
            <TableComponent />
          </BoxDashboard>
        </>
      );
    case "payouts":
      return (
        <div>
          <TitlePageDashboard value="Payouts" />
          <BoxDashboard>
            <CopyButtun />
          </BoxDashboard>
          <BoxDashboard classPlus="my-5 !gap-4">
            <div className="flex items-center gap-2">
              <AiOutlineExclamationCircle className="text-lg" />
              <span className="text-lg fonr-semibold">No commissions</span>
            </div>
            <p className="w-full p-3 rounded-md bg-purple-100 text-purple-500">
              There are not enough funds on the balance to choose a payment
              method or initiate a funds transfer based on the previously
              entered data. The minimum balance for withdrawal is $50.
            </p>
          </BoxDashboard>
          <BoxDashboard classPlus="mt-5">
            <TableComponent />
          </BoxDashboard>
        </div>
      );
    case "landing-pages":
      return (
        <>
          <TitlePageDashboard value="Landing pages" />
          <BoxDashboard>
            <CopyButtun />
          </BoxDashboard>
          <BoxDashboard classPlus="mt-5">
            <TableComponent />
          </BoxDashboard>
        </>
      );
  }
}

// Main component with Suspense boundary
export default function AffiliateProgram() {
  return (
    <Suspense
      fallback={<div className="animate-pulse bg-gray-200 rounded-xl h-96" />}
    >
      <AffiliateProgramContent />
    </Suspense>
  );
}
