import { CurveType } from "recharts/types/shape/Curve";

export type LineChartDataItems = {
  name: string;
  value: number;
};

type LoadedData = {
  title?: string;
  className?: string;
  bigNumber?: string;
  smallNumber?: string;
  data: LineChartDataItems[];
  onClick?: () => void;
  isLoading?: false;
  chartType?: CurveType;
};

type LoadingData = {
  title?: string;
  className?: string;
  bigNumber?: string;
  smallNumber?: string;
  data?: LineChartDataItems[];
  onClick?: () => void;
  isLoading: true;
  chartType?: CurveType;
};
export type LineChartData = LoadingData | LoadedData;
