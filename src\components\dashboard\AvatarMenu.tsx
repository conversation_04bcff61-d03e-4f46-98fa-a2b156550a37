import Image from "next/image";
import { useState, useRef, useEffect } from "react";
import { IoLanguageOutline } from "react-icons/io5";
import { RiSendPlaneLine } from "react-icons/ri";
import { LuDot } from "react-icons/lu";
import { useRouter } from "next/navigation";
import { AnimatePresence, motion } from "framer-motion";
import { CrownIcon, UserIcon } from "@/ui/icons/general";
import { useAuthStore } from "@/store/authStore";
import profileService from "@/services/profileService";
import { useQuery } from "@tanstack/react-query";
import FetchLoadingBox from "../shared/FetchLoadingBox";
import ImageLoading from "../shared/ImageLoading";

export default function AvatarMenu() {
  const { user, logout } = useAuthStore();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const route = useRouter();
  const { data: dataUserProfile, isFetching } = useQuery({
    queryKey: ["Profile"],
    queryFn: profileService.getUserProfile,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
  });
  const logOutHandler = () => {
    // Logout is now optimistic - no need to wait
    logout();
    // Redirect immediately
    route.push("/");
  };
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Check if user has active subscription
  const hasActiveSubscription = user?.subscriptions?.some(
    (subscription) => subscription.status === "active"
  );

  // Get subscription details
  const activeSubscription = user?.subscriptions?.find(
    (subscription) => subscription.status === "active"
  );

  return (
    <div className="relative" ref={dropdownRef}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-center w-12 h-12 rounded-full focus:border"
        aria-label="User profile"
        whileHover={{
          scale: 1.02,
        }}
        whileTap={{ scale: 0.98 }}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        {isFetching ? null : (
          <ImageLoading
            alt={""}
            src={dataUserProfile?.data?.avatar_url || "/images/aboutCard1.jpg"}
            className="w-12 object-cover  focus:opacity-70 h-12 rounded-full"
            classPlus=""
            figureClass=""
            height={48}
            width={48}
          />
        )}
      </motion.button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute  left-0 md:left-auto md:right-0 w-72 bg-white rounded-2xl shadow-lg z-50 border border-gray-200 overflow-hidden "
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            {/* Header with user info */}
            <FetchLoadingBox isFetching={isFetching}>
              <motion.div
                className="p-4 pb-3"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex flex-col items-center">
                  <motion.button
                    className="bg-gray-100 focus:opacity-70 rounded-full w-12 h-12 flex items-center justify-center mb-2"
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      delay: 0.1,
                    }}
                  >
                    <Image
                      width={48}
                      height={48}
                      className="w-12 object-cover h-12 rounded-full"
                      src={
                        dataUserProfile?.data?.avatar_url ||
                        "/images/aboutCard1.jpg"
                      }
                      alt=""
                    />
                  </motion.button>
                  <motion.h3
                    className="text-base font-semibold text-gray-800"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    👋 Hi {dataUserProfile?.data?.first_name || "John M"}
                  </motion.h3>
                </div>
              </motion.div>
              <div className="px-3 pb-2 flex flex-col gap-1">
                {/* Name */}
                <motion.div
                  className="flex items-center gap-2 w-full py-2 px-3 text-gray-800 text-sm font-medium rounded-xl"
                  initial={{ opacity: 0, x: -5 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: 0.05 }}
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span>
                    {dataUserProfile?.data?.first_name}{" "}
                    {dataUserProfile?.data?.last_name}
                  </span>
                </motion.div>
                {/* Email */}
                <motion.div
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.12 }}
                >
                  <motion.div
                    className="flex items-center gap-2 w-full py-2 px-3 text-gray-800 text-sm font-medium rounded-xl mb-4"
                    initial={{ opacity: 0, x: -5 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: 0.1 }}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M19.7055 6.79452C18.2618 5.49044 15.8298 5 12 5C8.1702 5 5.73816 5.49044 4.29452 6.79452M19.7055 6.79452C21.0003 7.96413 21.5 9.78823 21.5 12.5C21.5 18.2353 19.2647 20 12 20C4.73529 20 2.5 18.2353 2.5 12.5C2.5 9.78823 2.99972 7.96413 4.29452 6.79452M19.7055 6.79452L13.4142 13.0858C12.6331 13.8668 11.3668 13.8668 10.5858 13.0858L4.29452 6.79452"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <span className="truncate">{user?.email}</span>
                  </motion.div>

                  <p className="text-xs text-gray-700 font-bold mb-1 px-3">
                    Subscription Status
                  </p>
                  {hasActiveSubscription ? (
                    <motion.div className="flex items-center gap-2 w-full py-2 px-3 bg-green-50 rounded-lg mb-2">
                      <CrownIcon className="w-4 h-4 text-green-600 flex-shrink-0" />
                      <span className="text-xs text-green-700 font-medium">
                        {activeSubscription?.plan_name || "Active Plan"}
                      </span>
                    </motion.div>
                  ) : (
                    <motion.div className="flex items-center gap-2 w-full py-2 px-3 bg-gray-50 rounded-lg mb-2">
                      <CrownIcon className="w-4 h-4 text-gray-400 flex-shrink-0" />
                      <span className="text-xs text-gray-500 font-medium">
                        No active subscription
                      </span>
                    </motion.div>
                  )}
                </motion.div>
                {/* Profile */}
                <div className=" border-b border-gray-300 pb-1 mb-1">
                  <motion.button
                    onClick={() => {
                      route.push("/profile");
                      setIsOpen(false);
                    }}
                    className="flex border-b border-gray-300 mb-1 focus:opacity-70 items-center bg-gray-50  gap-2 w-full py-2 px-3  text-sm font-medium rounded-xl text-gray-500"
                    whileHover={{
                      color: "#914ac4",
                      x: 2,
                      transition: { duration: 0.2 },
                    }}
                    initial={{ opacity: 0, x: -5 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: 0.15 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg
                      width={18}
                      height={18}
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 16 16"
                      className="opacity-90"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 8h11m0 0-4-4m4 4-4 4m-5 3H3a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h3"
                      ></path>
                    </svg>
                    <span>Go to my profile</span>
                  </motion.button>
                </div>
                {/* Logout */}
                <motion.button
                  onClick={() => {
                    logOutHandler();
                    setIsOpen(false);
                  }}
                  className="flex border-b border-gray-300 mb-1 focus:opacity-70 items-center bg-red-50 text-red-600 gap-2 w-full py-2 px-3  text-sm font-medium rounded-xl hover:bg-red-50 hover:text-red-600"
                  whileHover={{
                    backgroundColor: "rgba(254, 226, 226, 0.5)",
                    color: "#dc2626",
                    x: 2,
                    transition: { duration: 0.2 },
                  }}
                  initial={{ opacity: 0, x: -5 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: 0.15 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg
                    width={18}
                    height={18}
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 16 16"
                    className="opacity-90"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 8h11m0 0-4-4m4 4-4 4m-5 3H3a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h3"
                    ></path>
                  </svg>
                  <span>Log Out</span>
                </motion.button>
              </div>
            </FetchLoadingBox>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
