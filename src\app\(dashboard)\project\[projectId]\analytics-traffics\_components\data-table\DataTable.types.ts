export type TableBody = { value: string; growth?: string };
export type TableData = {
  tableHeadings: string[];
  tableBody: TableBody[][];
};

export type DataTableProps = {
  tableData: TableData | undefined;
  badges?: string[];
  selectedItem?: string;
  setSelectedItem?: React.Dispatch<React.SetStateAction<string>>;
  isLoading?: boolean;
  title?: string;
  showDateRange?: boolean;
  checkbox?: boolean;
  setSelectedItems?: React.Dispatch<React.SetStateAction<string[]>>;
  selectedItems?: string[];
};

type PaginationType = {
  totalPages: number;
  initialPage?: number;
};
export type TableDataRequest = {
  tableData: TableData;
  pagination: PaginationType;
};
