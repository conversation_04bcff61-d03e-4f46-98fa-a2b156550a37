"use client";

import React, { ReactNode } from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { motion } from "framer-motion";

type DropdownProps = {
  button: ReactNode;
  children: ReactNode;
  className?: string;
  align?: "start" | "end" | "left" | "right";
  width?: number | string;
  open?: boolean;
  onClose?: boolean;
  setOpen?: (val: boolean) => void;
};

export default function Dropdown({
  button,
  children,
  className,
  onClose,
  align = "start",
  width = 192,
  open: controlledOpen,
  setOpen: controlledSetOpen,
}: DropdownProps) {
  const isControlled =
    controlledOpen !== undefined && controlledSetOpen !== undefined;

  // Handle onClose effect
  React.useEffect(() => {
    if (onClose && isControlled && controlledSetOpen) {
      controlledSetOpen(false);
    }
  }, [onClose, isControlled, controlledSetOpen]);

  return (
    <DropdownMenu.Root
      open={isControlled ? controlledOpen : undefined}
      onOpenChange={isControlled ? controlledSetOpen : undefined}
    >
      <DropdownMenu.Trigger asChild className={className}>
        <div className="cursor-pointer">{button}</div>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align={align}
          sideOffset={8}
          collisionPadding={16}
          avoidCollisions={true}
          className="z-[9999] border border-gray-200 rounded-xl shadow-lg will-change-[opacity,transform] data-[side=top]:animate-slideDownAndFade data-[side=right]:animate-slideLeftAndFade data-[side=bottom]:animate-slideUpAndFade data-[side=left]:animate-slideRightAndFade"
          style={{ width, backgroundColor: "#F4F4F4" }}
          asChild
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </motion.div>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
