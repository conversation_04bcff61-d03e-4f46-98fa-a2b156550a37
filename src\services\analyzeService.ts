import http from "./httpService";
import deviceService from "./deviceService";

/**
 * Analyze a URL using the httpService with device ID tracking
 * @param url The URL to analyze
 * @returns Promise with the analysis data
 */
export async function analyzeApi(url: string) {
  // Get the device ID
  const deviceId = deviceService.getDeviceId();

  try {
    // Use httpService but add the device ID header and enable auth if token is available
    const response = await http.post(
      "/api/analyze/",
      { url },
      {
        headers: {
          "X-Device-ID": deviceId,
        },
        useAuth: true, // This will add the auth token if it's available
      }
    );

    return response.data;
  } catch (error) {
    // Let the error propagate to be handled by the caller
    // This allows us to access the error.response object with status code and data
    throw error;
  }
}

export async function checkStatusAnalyzeApi(taskId: string) {
  return http
    .get(`/api/status/${taskId}/`, { useAuth: true })
    .then(({ data }) => data);
}

export async function getSharedAnalysisApi(taskId: string) {
  return http
    .get(`/api/share/${taskId}/`, { useAuth: true })
    .then(({ data }) => data);
}
