"use client";

import React from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { MdClose } from "react-icons/md";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa6";

interface KeywordTableRow {
  id: string;
  keyword: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
  volume: string;
  numericVolume?: number; // Numeric value for sorting
  configId: string;
  isSuggested?: boolean; // Optional flag to indicate if keyword was AI-suggested
}

interface KeywordsListProps {
  currentRows: KeywordTableRow[];
  startIndex: number;
  totalRows: number;
  currentPage: number;
  totalPages: number;
  onRemoveKeywordConfig: (keyword: string, configId: string) => void;
  onPageChange: (page: number) => void;
  isLoading?: boolean; // Optional loading state
}

export default function KeywordsList({
  currentRows,
  startIndex,
  totalRows,
  currentPage,
  totalPages,
  onRemoveKeywordConfig,
  onPageChange,
  isLoading = false,
}: KeywordsListProps) {
  return (
    <>
      {/* Keywords List */}
      <div className="flex flex-col gap-1 mt-3 p-1 h-[330px] overflow-y-auto mb-2">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center gap-3">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span className="text-primary text-sm">
                Loading keyword suggestions...
              </span>
            </div>
          </div>
        ) : totalRows > 0 ? (
          <AnimatePresence mode="popLayout">
            {currentRows.map((row, index) => (
              <motion.div
                key={row.id}
                initial={{ opacity: 0, scale: 0.95, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="bg-white rounded-md py-2 px-3 flex justify-between items-center select-none"
                style={{
                  userSelect: "none",
                  WebkitUserSelect: "none",
                  MozUserSelect: "none",
                  msUserSelect: "none",
                }}
                onMouseDown={(e) => e.preventDefault()}
                onSelectStart={(e) => e.preventDefault()}
                onDragStart={(e) => e.preventDefault()}
              >
                <div className="flex gap-3 items-center">
                  <span
                    className="text-xs w-6 min-w-6 max-w-6 text-right select-none"
                    style={{
                      userSelect: "none",
                      WebkitUserSelect: "none",
                      MozUserSelect: "none",
                      msUserSelect: "none",
                    }}
                    onMouseDown={(e) => e.preventDefault()}
                    onSelectStart={(e) => e.preventDefault()}
                    onDragStart={(e) => e.preventDefault()}
                  >
                    {startIndex + index + 1}.
                  </span>
                  <div className="flex items-center mr-5 w-48 min-w-48 max-w-48">
                    <span className="truncate text-xs w-32 min-w-32 max-w-32 block">
                      {row.keyword}
                    </span>
                    <div className="w-20 min-w-20 max-w-20 flex items-center justify-start ml-2">
                      {row.isSuggested && (
                        <>
                          <div className="flex items-center gap-1 text-primary bg-primary/10 py-1 px-2 rounded-full">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth={1.5}
                              stroke="currentColor"
                              className="w-3 h-3"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
                              />
                            </svg>
                            <span className="text-xs font-medium whitespace-nowrap">
                              Suggest
                            </span>
                          </div>
                          <span className="text-gray-300 text-xs ml-2">•</span>
                        </>
                      )}
                    </div>
                  </div>
                  {row.searchEngine && (
                    <>
                      <figure className="py-1 px-2 bg-gray-200 rounded-full">
                        <Image
                          src={row.searchEngine.image}
                          alt="search engine"
                          width={16}
                          height={16}
                        />
                      </figure>
                      <span className="text-gray-300 text-xs">•</span>
                    </>
                  )}
                  {row.country && (
                    <>
                      <figure className="py-1 px-2 bg-gray-200 rounded-full">
                        <Image
                          src={row.country.image}
                          alt="country"
                          width={18}
                          height={18}
                        />
                      </figure>
                      {row.location && (
                        <span className="text-gray-300 text-xs">•</span>
                      )}
                    </>
                  )}
                  {row.location && (
                    <span className="text-xs text-gray-600 font-medium">
                      {row.location.name}
                    </span>
                  )}
                </div>
                <div className="flex gap-2 ml-2 items-center">
                  {row.language && (
                    <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded mr-2">
                      {row.language.code.toUpperCase()}
                    </span>
                  )}
                  {row.volume && (
                    <span className="text-xs text-gray-500 mr-2 w-8 min-w-8 max-w-8 text-right">
                      {row.volume}
                    </span>
                  )}
                  <button
                    type="button"
                    title="Delete keyword"
                    className="select-none"
                    style={{
                      userSelect: "none",
                      WebkitUserSelect: "none",
                      MozUserSelect: "none",
                      msUserSelect: "none",
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onSelectStart={(e) => e.preventDefault()}
                    onDragStart={(e) => e.preventDefault()}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveKeywordConfig(row.keyword, row.configId);
                    }}
                  >
                    <MdClose size={16} />
                  </button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500 text-sm">
            No keywords added yet. Start by entering a keyword above.
          </div>
        )}
      </div>

      {/* Pagination Box */}
      <div className="grid grid-cols-3 items-center">
        <div className="text-left">
          <span className="text-sm text-purple-800 font-medium bg-purple-100 px-3 py-1.5 rounded-md">
            {totalRows} of 750
          </span>
        </div>
        <div className="flex gap-1 items-center justify-center">
          {totalRows > 0 && totalPages > 1 && (
            <>
              {/* Previous Button */}
              <button
                type="button"
                className={`w-6 h-6 min-w-6 min-h-6 rounded-full border-2 border-pagination flex items-center justify-center flex-shrink-0 transition-all duration-200 ease-in-out ${
                  currentPage === 1
                    ? "opacity-40 cursor-not-allowed text-pagination"
                    : "cursor-pointer text-pagination hover:text-gray-800 hover:bg-gray-200 hover:border-gray-600 hover:scale-105 active:scale-95"
                }`}
                onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <FaArrowLeft size={10} />
              </button>

              {/* Page Numbers - Show 4 pages at a time */}
              <div className="flex items-center gap-1">
                {(() => {
                  const pages = [];
                  const maxVisiblePages = 4;

                  if (totalPages <= maxVisiblePages) {
                    // Show all pages if total is 4 or less
                    for (let i = 1; i <= totalPages; i++) {
                      pages.push(
                        <button
                          key={i}
                          type="button"
                          className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                            i === currentPage
                              ? "text-black font-semibold"
                              : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                          }`}
                          onClick={() => onPageChange(i)}
                        >
                          {i}
                        </button>
                      );
                    }
                  } else {
                    // Determine which pages to show based on current page
                    if (currentPage <= maxVisiblePages - 1) {
                      // Show first 4 pages + ellipsis + last page
                      for (let i = 1; i <= maxVisiblePages; i++) {
                        pages.push(
                          <button
                            key={i}
                            type="button"
                            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                              i === currentPage
                                ? "text-black font-semibold"
                                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                            }`}
                            onClick={() => onPageChange(i)}
                          >
                            {i}
                          </button>
                        );
                      }
                      pages.push(
                        <span
                          key="ellipsis"
                          className="text-xs text-gray-500 opacity-60 px-1"
                        >
                          ...
                        </span>
                      );
                      pages.push(
                        <button
                          key={totalPages}
                          type="button"
                          className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                            totalPages === currentPage
                              ? "text-black font-semibold"
                              : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                          }`}
                          onClick={() => onPageChange(totalPages)}
                        >
                          {totalPages}
                        </button>
                      );
                    } else if (
                      currentPage >=
                      totalPages - maxVisiblePages + 2
                    ) {
                      // Show first page + ellipsis + last 4 pages
                      pages.push(
                        <button
                          key={1}
                          type="button"
                          className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                            1 === currentPage
                              ? "text-black font-semibold"
                              : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                          }`}
                          onClick={() => onPageChange(1)}
                        >
                          1
                        </button>
                      );
                      pages.push(
                        <span
                          key="ellipsis"
                          className="text-xs text-gray-500 opacity-60 px-1"
                        >
                          ...
                        </span>
                      );
                      for (
                        let i = totalPages - maxVisiblePages + 1;
                        i <= totalPages;
                        i++
                      ) {
                        pages.push(
                          <button
                            key={i}
                            type="button"
                            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                              i === currentPage
                                ? "text-black font-semibold"
                                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                            }`}
                            onClick={() => onPageChange(i)}
                          >
                            {i}
                          </button>
                        );
                      }
                    } else {
                      // Show first page + ellipsis + current page range + ellipsis + last page
                      pages.push(
                        <button
                          key={1}
                          type="button"
                          className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                            1 === currentPage
                              ? "text-black font-semibold"
                              : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                          }`}
                          onClick={() => onPageChange(1)}
                        >
                          1
                        </button>
                      );
                      pages.push(
                        <span
                          key="ellipsis1"
                          className="text-xs text-gray-500 opacity-60 px-1"
                        >
                          ...
                        </span>
                      );
                      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                        pages.push(
                          <button
                            key={i}
                            type="button"
                            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                              i === currentPage
                                ? "text-black font-semibold"
                                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                            }`}
                            onClick={() => onPageChange(i)}
                          >
                            {i}
                          </button>
                        );
                      }
                      pages.push(
                        <span
                          key="ellipsis2"
                          className="text-xs text-gray-500 opacity-60 px-1"
                        >
                          ...
                        </span>
                      );
                      pages.push(
                        <button
                          key={totalPages}
                          type="button"
                          className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                            totalPages === currentPage
                              ? "text-black font-semibold"
                              : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                          }`}
                          onClick={() => onPageChange(totalPages)}
                        >
                          {totalPages}
                        </button>
                      );
                    }
                  }

                  return pages;
                })()}
              </div>

              {/* Next Button */}
              <button
                type="button"
                className={`w-6 h-6 min-w-6 min-h-6 rounded-full border-2 border-pagination flex items-center justify-center flex-shrink-0 transition-all duration-200 ease-in-out ${
                  currentPage === totalPages
                    ? "opacity-40 cursor-not-allowed text-pagination"
                    : "cursor-pointer text-pagination hover:text-gray-800 hover:bg-gray-200 hover:border-gray-600 hover:scale-105 active:scale-95"
                }`}
                onClick={() =>
                  currentPage < totalPages && onPageChange(currentPage + 1)
                }
                disabled={currentPage === totalPages}
              >
                <FaArrowRight size={10} />
              </button>
            </>
          )}
        </div>

        <div className="text-right flex justify-end">
          {/* Import button moved to before input field */}
        </div>
      </div>
    </>
  );
}
