import { create } from "zustand";
import type { LineChartData } from "@/types/LineChartCard.type";

type ChartPopupState = {
  isVisible: boolean;
  show: () => void;
  hide: () => void;
};

export const useChartPopupStore = create<ChartPopupState>((set) => ({
  isVisible: false,
  show: () => set({ isVisible: true }),
  hide: () => set({ isVisible: false }),
}));

type LineChartStore = LineChartData & {
  setChartData: (data: LineChartData) => void;
  resetChartData: () => void;
};

export const useLineChartDataStore = create<LineChartStore>((set) => ({
  title: "",
  className: "",
  bigNumber: "",
  smallNumber: "",
  data: [],

  setChartData: (data: LineChartData) => set(() => ({ ...data })),
  resetChartData: () =>
    set(() => ({
      title: "",
      className: "",
      bigNumber: "",
      smallNumber: "",
      data: [],
    })),
}));
