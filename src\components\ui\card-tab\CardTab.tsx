import React from "react";
import Card from "../card";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";
import Skeleton from "react-loading-skeleton";

/* ========================================================================== */
/**
 * CardTab Component
 *
 * A stylized card tab used for displaying a titled metric with its value and a change indicator.
 * Clicking the card triggers a selection action (onSelect).
 *
 * Use case: Dashboards, analytics panels, tabs with numbers and metrics.
 *
 * @component
 * @example
 * ```tsx
 * <CardTab
 *   title="Clicks"
 *   value="2.4K"
 *   changeValue="+4.5%"
 *   onSelect={() => console.log("Tab selected!")}
 * />
 * ```
 *
 * @param className - Optional additional class names for custom styling
 * @param title - Title of the tab (e.g. "Clicks", "Impressions")
 * @param value - Main metric value to display
 * @param changeValue - Change indicator (e.g. "+4.5%")
 * @param onSelect - Callback fired when the card is clicked
 *
 * @returns A clickable tab styled as a card
 */

const CardTab = ({
  className,
  title,
  value,
  changeValue,
  onSelect,
  isLoading,
  style,
}: TCardTabProps) => {
  return (
    <Card
      onClick={onSelect}
      className={cn(
        "bg-[#F4F4F4] w-36 h-16 text-secondary space-y-2 p-2 cursor-pointer text-xs rounded-lg flex flex-col justify-around",
        className
      )}
      style={style}
    >
      {isLoading ? <Skeleton width={"80%"} /> : <span>{title}</span>}
      <div className="space-x-2">
        {isLoading ? (
          <Skeleton width={"50%"} />
        ) : (
          <span className="text-lg font-bold">{value}</span>
        )}
        {!isLoading && (
          <span className="text-[10px] text-primary-green">{changeValue}</span>
        )}
      </div>
    </Card>
  );
};

export default CardTab;
