import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import type { AudienceResponse } from "./Audience.types";
import { useProjectId } from "@/hooks/useProjectId";

/**
 * Fetches audience data from the server.
 *
 * @param request The request to fetch (e.g. "cardTabs", "lineChartData", etc.)
 * @returns The fetched data
 */
const useAudience = (request: string) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["audience", projectId, request],
    queryFn: async (): Promise<AudienceResponse> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/audience",
        {
          params: {
            projectId,
            audience: request,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
    refetchOnMount: false,
    staleTime: 1000 * 60 * 5,
  });
};

export default useAudience;
