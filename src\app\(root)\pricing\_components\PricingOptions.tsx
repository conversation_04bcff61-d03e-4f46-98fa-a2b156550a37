import Link from "next/link";
import React from "react";

type Variant = "primary" | "blue" | "green";
const renderStyles = (key: Variant) => {
  switch (key) {
    case "primary":
      return {
        text: "text-primary",
        bg: "!bg-primary",
        bgOpacity: "bg-primary/10",
      };
    case "blue":
      return {
        text: "text-[#7EA2EF]",
        bg: "!bg-[#7EA2EF]",
        bgOpacity: "bg-[#7EA2EF]/10",
      };
    case "green":
      return {
        text: "text-[#4AC452]",
        bg: "!bg-[#4AC452]",
        bgOpacity: "bg-[#4AC452]/10",
      };
    default:
      return {
        text: "text-primary",
        bg: "!bg-primary",
        bgOpacity: "bg-primary/10",
      };
  }
};

export default function PricingOptions() {
  return <div></div>;
}

type TitleProps = {
  variant: Variant;
  title: string;
};
function Title({ title, variant }: TitleProps) {
  return (
    <div
      className={`${
        renderStyles(variant).bg
      } h-16 flex justify-center items-center text-center rounded-t-lg text-lg font-extrabold text-white`}
    >
      {title}
    </div>
  );
}

function Description({
  text,
  className,
}: {
  text: string | React.ReactNode;
  className?: string;
}) {
  return (
    <div
      className={`${className} py-2.5 px-2 flex items-center bg-white rounded-lg text-sm font-semibold text-secondary flex items-center`}
    >
      {text}
    </div>
  );
}

function Label({ label }: { label: string }) {
  return (
    <div className="text-secondary font-black py-2.5 px-2 lg:bg-white rounded-lg">
      {label}
    </div>
  );
}

function Button({ title, variant }: { title: string; variant: Variant }) {
  return (
    <button
      className={`w-full btn btn--primary !border-0 ${
        renderStyles(variant).bg
      }`}
    >
      <Link href="checkout">{title} </Link>
    </button>
  );
}

PricingOptions.Title = Title;
PricingOptions.Description = Description;
PricingOptions.Label = Label;
PricingOptions.Button = Button;
