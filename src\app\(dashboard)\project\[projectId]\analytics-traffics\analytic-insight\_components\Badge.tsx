import { cn } from "@/utils/cn";
import React, { ReactNode } from "react";

/**
 * A badge component that can be used to represent a filter tag.
 *
 * @prop {ReactNode} children The content of the badge.
 * @prop {string} [className] Additional classes to apply to the badge.
 * @prop {() => void} onSelect Function to call when the badge is clicked.
 */
const Badge = ({
  children,
  className,
  onSelect,
  selected,
  style,
}: {
  children: ReactNode;
  className?: string;
  selected?: boolean;
  onSelect: () => void;
  style?: React.CSSProperties;
}) => {
  return (
    <div
      onClick={onSelect}
      className={cn(
        "border-2 bg-gray-100 text-secondary py-2 px-4 text-xs w-fit rounded-md cursor-pointer",
        { "text-primary bg-primary/10 border-transparent": selected },
        className,
      )}
      style={style}
    >
      {children}
    </div>
  );
};

/**
 * BadgeWithCheckbox – A badge with a checkbox on the left, controlled by parent.
 *
 * @prop {boolean} checked - Whether the checkbox is checked.
 * @prop {(checked: boolean) => void} onChange - Handler for checkbox state change.
 * @prop {ReactNode} children - The badge content.
 * @prop {string} [className] - Additional classes for the badge container.
 * @prop {string} [checkboxId] - Optional id for the checkbox.
 * @prop {boolean} [selected] - Optional, for badge highlight styling.
 * @prop {React.CSSProperties} [style] - Optional, for custom styles.
 */
import Checkbox from "@/components/ui/Checkbox";

export const BadgeWithCheckbox = ({
  checked,
  onChange,
  children,
  className,
  checkboxId,
  selected,
  style,
}: {
  checked: boolean;
  onChange: (checked: boolean) => void;
  children: ReactNode;
  className?: string;
  checkboxId?: string;
  selected?: boolean;
  style?: React.CSSProperties;
}) => {
  return (
    <div
      className={cn(
        "flex items-center gap-2 border-2 bg-gray-100 text-secondary py-2 px-4 text-xs w-fit rounded-md cursor-pointer",
        { "text-primary bg-primary/10 border-transparent": selected },
        className,
      )}
      style={style}
      // Optionally, you can add tabIndex or role for accessibility
    >
      <Checkbox checked={checked} onChange={onChange} id={checkboxId} />
      <span>{children}</span>
    </div>
  );
};

export default Badge;
