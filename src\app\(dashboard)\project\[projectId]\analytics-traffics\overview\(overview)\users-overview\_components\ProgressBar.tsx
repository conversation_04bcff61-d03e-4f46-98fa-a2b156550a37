import React from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import Skeleton from "react-loading-skeleton";

const ProgressBar = ({
  percentage,
  className,
  title,
  color,
  isLoading,
}: {
  percentage: number;
  className?: string;
  title: string;
  color?: string;
  isLoading: boolean | false;
}) => {
  return (
    <div className={cn(className, "space-y-1.5")}>
      <div className="flex justify-between text-secondary text-xs">
        {isLoading ? (
          <Skeleton width={100} />
        ) : (
          <>
            <span>{title}</span>
          </>
        )}
        {isLoading ? <Skeleton width={30} /> : <span>{percentage}%</span>}
      </div>
      <div className="border-2 h-2.5 w-full overflow-hidden rounded-full">
        {isLoading ? (
          <Skeleton
            width={`${percentage}%`}
            className="-translate-y-2"
            style={{ borderRadius: "200px" }}
          />
        ) : (
          <motion.div
            className={`h-full rounded-full ${color || "bg-primary"}`}
            initial={{ width: 0 }}
            whileInView={{ width: `${percentage}%` }}
            transition={{ duration: 2, type: "spring" }}
            viewport={{ once: true, amount: 0.2 }}
          />
        )}
      </div>
    </div>
  );
};

export default ProgressBar;
