import Table from "@/ui/Table";
import ProgressPercent from "../../ProgressPercent";
import { CheckIcon, CrossIcon } from "@/ui/icons/general";
import { KeywordType, PharseType } from "./OnPageSeo";

// const tableData = [
//   {
//     keyword: "seoptimer",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: false,
//     frequency: "39",
//     percentage: 100,
//   },
//   {
//     keyword: "seo",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "16",
//     percentage: 70,
//   },
//   {
//     keyword: "audit",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "18",
//     percentage: 100,
//   },
//   {
//     keyword: "reports",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "40",
//     percentage: 100,
//   },
//   {
//     keyword: "website",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "8",
//     percentage: 40,
//   },
//   {
//     keyword: "tool",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "24",
//     percentage: 80,
//   },
//   {
//     keyword: "site",
//     title: true,
//     metaDescriptionTag: false,
//     headingsTags: true,
//     frequency: "12",
//     percentage: 40,
//   },
//   {
//     keyword: "rank",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: false,
//     frequency: "10",
//     percentage: 50,
//   },
// ];

export default function Keywords({
  data,
  pharses,
}: {
  data: KeywordType[];
  pharses: PharseType[];
}) {
  // Calculate percentages for keywords if not provided
  const processedData = data.map(item => {
    if (item.percentage !== undefined) return item;

    // Find the max frequency to calculate percentage
    const maxFrequency = Math.max(...data.map(k => k.frequency));
    const percentage = maxFrequency > 0
      ? Math.round((item.frequency / maxFrequency) * 100)
      : 0;

    return {
      ...item,
      percentage
    };
  });

  // Calculate percentages for phrases if not provided
  const processedPhrases = pharses.map(item => {
    if (item.percentage !== undefined) return item;

    // Find the max frequency to calculate percentage
    const maxFrequency = Math.max(...pharses.map(p => p.frequency));
    const percentage = maxFrequency > 0
      ? Math.round((item.frequency / maxFrequency) * 100)
      : 0;

    return {
      ...item,
      percentage
    };
  });

  return (
    <div>
      <h4 className="font-semibold text-secondary pb-2">Individual Keywords</h4>
      {processedData.length > 0 ? (
        <Table>
          <Table.Header>
            <th className="pb-2 pr-[58px] lg:pr-0">Keyword</th>
            <th className="pb-2">Title</th>
            <th className="text-center pb-2 whitespace-nowrap px-[58px] lg:px-0">
              Meta Description
              <br /> Tag
            </th>
            <th className="text-center pb-2">
              Headings
              <br /> Tags
            </th>
            <th className="min-w-[158px] lg:min-w-0 text-center pb-2">
              Page <br />
              Frequency
            </th>
            <th className="lg:w-[180px]"></th>
          </Table.Header>
          <Table.Body>
            {processedData.map((item, index) => (
              <Table.Row key={index}>
                <td>{item.keyword}</td>
                <td>
                  {item.in_title ? (
                    <CheckIcon className="w-6 h-6 text-primary-green" />
                  ) : (
                    <CrossIcon className="w-6 h-6 text-primary-red" />
                  )}
                </td>
                <td>
                  <div className="w-full flex justify-center">
                    {item.in_meta_description ? (
                      <CheckIcon className="w-6 h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-6 h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="">
                  <div className="w-full flex justify-center">
                    {item.in_headings ? (
                      <CheckIcon className="w-6 h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-6 h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="text-center">{item.frequency}</td>
                <td className="lg:pl-[91px]">
                  <div className="flex justify-end">
                    <div className="w-[180px]">
                      <ProgressPercent percentage={item.percentage || 0} />
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No keyword data available
        </div>
      )}

      <h4 className="font-semibold text-secondary pb-2 mt-6">Phrases</h4>
      {processedPhrases.length > 0 ? (
        <Table>
          <Table.Header>
            <th className="pb-2 pr-[58px] lg:pr-0">Phrase</th>
            <th className="pb-2">Title</th>
            <th className="text-center pb-2 whitespace-nowrap px-[58px] lg:px-0">
              Meta Description
              <br /> Tag
            </th>
            <th className="text-center pb-2">
              Headings
              <br /> Tags
            </th>
            <th className="min-w-[158px] lg:min-w-0 text-center pb-2">
              Page <br />
              Frequency
            </th>
            <th className="lg:w-[180px]"></th>
          </Table.Header>
          <Table.Body>
            {processedPhrases.map((item, index) => (
              <Table.Row key={index}>
                <td>{item.phrase}</td>
                <td>
                  {item.in_title ? (
                    <CheckIcon className="w-6 h-6 text-primary-green" />
                  ) : (
                    <CrossIcon className="w-6 h-6 text-primary-red" />
                  )}
                </td>
                <td>
                  <div className="w-full flex justify-center">
                    {item.in_meta_description ? (
                      <CheckIcon className="w-6 h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-6 h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="">
                  <div className="w-full flex justify-center">
                    {item.in_headings ? (
                      <CheckIcon className="w-6 h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-6 h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="text-center">{item.frequency}</td>
                <td className="lg:pl-[91px]">
                  <div className="flex justify-end">
                    <div className="w-[180px]">
                      <ProgressPercent percentage={item.percentage || 0} />
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No phrase data available
        </div>
      )}
    </div>
  );
}
