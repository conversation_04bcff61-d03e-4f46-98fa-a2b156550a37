"use client";
import React, { useEffect, useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useOptimizedAnimations } from "./AnimationPerformanceMonitor";

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  staggerChildren?: boolean;
  delay?: number;
}

// Animation variants for page transitions - optimized for performance
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    will<PERSON><PERSON><PERSON>: "transform, opacity",
  },
  animate: {
    opacity: 1,
    y: 0,
    will<PERSON>hange: "auto",
    transition: {
      duration: 0.4,
      ease: [0.25, 0.1, 0.25, 1] as const,
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    will<PERSON>hange: "transform, opacity",
    transition: {
      duration: 0.3,
      ease: [0.25, 0.1, 0.25, 1] as const,
    },
  },
};

// Animation variants for staggered children
const childVariants = {
  initial: {
    opacity: 0,
    y: 15,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.1, 0.25, 1] as const,
    },
  },
};

// Animation variants for form elements
const formElementVariants = {
  initial: {
    opacity: 0,
    y: 10,
    scale: 0.98,
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.1, 0.25, 1] as const,
    },
  },
};

// Animation variants for cards and boxes
const cardVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.1, 0.25, 1] as const,
    },
  },
};

// Main PageTransition component
export default function PageTransition({
  children,
  className = "",
  staggerChildren = true,
  delay = 0,
}: PageTransitionProps) {
  const variants = staggerChildren
    ? pageVariants
    : {
        ...pageVariants,
        animate: {
          ...pageVariants.animate,
          transition: {
            ...pageVariants.animate.transition,
            delay,
            staggerChildren: undefined,
            delayChildren: undefined,
          },
        },
      };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Animated wrapper for individual elements (optimized)
export function AnimatedElement({
  children,
  className = "",
  variant = "child",
  delay = 0,
}: {
  children: React.ReactNode;
  className?: string;
  variant?: "child" | "form" | "card";
  delay?: number;
}) {
  const animationSettings = useOptimizedAnimations();

  // Memoize variants to prevent recalculation on every render
  const variants = useMemo(() => {
    if (!animationSettings.enableAnimations) {
      // Return static variants if animations are disabled
      return {
        initial: {},
        animate: {},
        exit: {},
      };
    }

    const getVariants = () => {
      switch (variant) {
        case "form":
          return formElementVariants;
        case "card":
          return cardVariants;
        default:
          return childVariants;
      }
    };

    const baseVariants = getVariants();
    return {
      ...baseVariants,
      animate: {
        ...baseVariants.animate,
        transition: {
          ...baseVariants.animate.transition,
          delay: animationSettings.reducedAnimations ? delay * 0.5 : delay,
          duration: animationSettings.animationDuration,
        },
      },
    };
  }, [variant, delay, animationSettings]);

  // Skip motion wrapper if animations are disabled for better performance
  if (!animationSettings.enableAnimations) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div variants={variants} className={className}>
      {children}
    </motion.div>
  );
}

// Animated wrapper for navigation transitions
export function NavigationTransition({
  children,
  direction = "forward",
}: {
  children: React.ReactNode;
  direction?: "forward" | "backward";
}) {
  const variants = {
    initial: {
      opacity: 0,
      x: direction === "forward" ? 30 : -30,
    },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.1, 0.25, 1] as const,
      },
    },
    exit: {
      opacity: 0,
      x: direction === "forward" ? -30 : 30,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.1, 0.25, 1] as const,
      },
    },
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial="initial"
        animate="animate"
        exit="exit"
        variants={variants}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}
