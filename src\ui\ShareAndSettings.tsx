import { Button } from "@/components/ui/button";
import React from "react";
import { IoSettingsOutline } from "react-icons/io5";
import { SlShare } from "react-icons/sl";

const ShareAndSettings = () => {
  return (
    <div className="flex gap-4 items-center">
      <IoSettingsOutline className="size-[20px] text-secondary hover:rotate-90 transition-all duration-500 cursor-pointer" />
      <Button
        variant={"secondary"}
        className="bg-gray-300 text-secondary font-bold hover:text-white"
      >
        <SlShare />
        <span>Share</span>
      </Button>
    </div>
  );
};

export default ShareAndSettings;
