"use client";
import React from "react";

/* ================================== ICONS ================================= */
// import GoogleConnectedHeader from "./GoogleConnectedHeader";
import Card from "@/components/ui/card";
import { CgInfo } from "react-icons/cg";

/* ========================================================================== */
const GoogleDisconnected = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  // const buttons = [
  //   {
  //     name: "Connect Google Analytics",
  //     image: "/images/create-project/google-analytics.svg",
  //     id: 1,
  //   },
  //   {
  //     name: "Connect Google Search Console",
  //     image: "/images/create-project/google.svg",
  //     id: 2,
  //   },
  // ];

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className={`w-full container space-y-6`}>
      {/* <GoogleConnectedHeader /> */}
      <Card className="flex flex-col justify-start items-center pt-24 h-screen">
        <div className="flex gap-4 items-center text-secondary">
          <CgInfo className="scale-y-[-1] size-6" />
          <h4 className="text-xl font-extrabold">No Data Found</h4>
        </div>
        <span className="text-secondary/30">
          connect to google analytics and search console to get fully report
        </span>
      </Card>
    </div>
  );
};

export default GoogleDisconnected;
