"use client";
import { LocalSEOAnalysis } from "@/types/seoAnalyzerTypes";
import BoxPrimary from "../../BoxPrimary";
import ShowMoreSection from "../usability/ShowMoreSection";
import {
  DocumentCheckIcon,
  DocumentCrossIcon,
  InfoIcon,
} from "@/ui/icons/general";
import { motion } from "framer-motion";
import LoadingSlate from "@/components/loading/LoadingSlate";
import OverallSection from "../OverallSection";

type LocalSeoProps = {
  results: LocalSEOAnalysis | string | null;
};

export default function LocalSeo({ results }: LocalSeoProps) {
  // Check if results is a string (like "loading") or null instead of an object
  if (typeof results === "string" || results === null) {
    return (
      <BoxPrimary title="Local SEO Results">
        <LoadingSlate
          title="Loading local SEO results..."
          showHeader={true}
          showCards={false}
          showChart={false}
          showProgress={true}
          height="md"
        />
      </BoxPrimary>
    );
  }

  // Check if we have any data to show (even partial)
  const hasAnyData = results && Object.keys(results).length > 0;

  // If we don't have any data at all, show loading state
  if (!hasAnyData) {
    return (
      <BoxPrimary title="Local SEO Results">
        <LoadingSlate
          title="Loading local SEO results..."
          showHeader={true}
          showCards={false}
          showChart={false}
          showProgress={true}
          height="md"
        />
      </BoxPrimary>
    );
  }

  // Check if we have header data
  const hasHeaderData = results.overall_title || results.overall_description;

  return (
    <BoxPrimary title="Local SEO Results">
      {hasHeaderData ? (
        <OverallSection
          title={results.overall_title || "Local SEO Analysis"}
          description={results.overall_description || "Local SEO helps businesses promote their products and services to local customers at the exact time they're looking for them."}
        />
      ) : (
        <div className="flex items-start gap-3 p-4 py-6 bg-primary/7 rounded-lg animate-pulse">
          <div className="flex justify-center items-center min-h-full">
            <InfoIcon className="h-20 w-20 text-primary flex-shrink-0" />
          </div>
          <div className="flex-1">
            <div className="h-5 bg-primary/20 rounded w-3/4 mb-3"></div>
            <div className="h-4 bg-primary/15 rounded w-full mb-2"></div>
            <div className="h-4 bg-primary/15 rounded w-5/6"></div>
          </div>
        </div>
      )}

      <div className="mt-8 space-y-6">
        {/* Contact Info Section */}
        {/* {results.contact_info && (
          <ShowMoreSection
            title="Contact Information"
            description={results.contact_info.description}
            passed={results.contact_info.pass}
            icon={
              results.contact_info.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.contact_info.importance}
            recommendation={results.contact_info.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Address:</strong>{" "}
                {results.contact_info.has_address ? "Yes" : "No"}
              </p>
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Phone:</strong>{" "}
                {results.contact_info.has_phone ? "Yes" : "No"}
              </p>

              {results.contact_info.addresses &&
                results.contact_info.addresses.length > 0 && (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-secondary mb-1">
                      Addresses Found:
                    </p>
                    <ul className="list-disc pl-5">
                      {results.contact_info.addresses.map((address, index) => (
                        <li key={index} className="text-sm text-secondary/80">
                          {address}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

              {results.contact_info.phones &&
                results.contact_info.phones.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-secondary mb-1">
                      Phone Numbers Found:
                    </p>
                    <ul className="list-disc pl-5">
                      {results.contact_info.phones.map((phone, index) => (
                        <li key={index} className="text-sm text-secondary/80">
                          {phone}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )} */}

        {/* Business Schema Section */}
        {/* {results.business_schema && (
          <ShowMoreSection
            title="Business Schema"
            description={results.business_schema.description}
            passed={results.business_schema.pass}
            icon={
              results.business_schema.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.business_schema.importance}
            recommendation={results.business_schema.recommendation}
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Local Business Schema:</strong>{" "}
                {results.business_schema.has_local_business_schema
                  ? "Yes"
                  : "No"}
              </p>

              {results.business_schema.schema_types &&
                results.business_schema.schema_types.length > 0 && (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-secondary mb-1">
                      Schema Types Found:
                    </p>
                    <ul className="list-disc pl-5">
                      {results.business_schema.schema_types.map(
                        (type, index) => (
                          <li key={index} className="text-sm text-secondary/80">
                            {type}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

              {results.business_schema.local_business_schemas &&
                results.business_schema.local_business_schemas.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-secondary mb-1">
                      Local Business Schemas:
                    </p>
                    <div className="mt-2 bg-gray-50 p-3 rounded-md">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(
                          results.business_schema.local_business_schemas,
                          null,
                          2
                        )}
                      </pre>
                    </div>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )} */}

        {/* Google Business Section */}
        {results.google_business && (
          <ShowMoreSection
            title="Google Business Profile"
            description={results.google_business.description}
            passed={results.google_business.pass}
            icon={
              results.google_business.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.google_business.importance}
            recommendation={results.google_business.recommendation}
            actionButton={
              <div className="flex items-center justify-end">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Link disabled for now */}
                  <div>
                    <motion.button
                      className="btn border border-primary text-primary bg-white opacity-50 cursor-not-allowed px-4 py-1.5 rounded-md shadow-sm text-sm font-bold"
                      disabled={true}
                    >
                      {/* Background SVG with reduced opacity */}

                      <svg
                        viewBox="0 0 48 48"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="stroke-current  w-6 h-6"
                      >
                        <polygon points="14.25 19.557 24 19.557 24 7.094 15.858 7.094 14.25 19.557" />
                        <path d="M15.8582,7.0938H9.1424A2,2,0,0,0,7.2006,8.6145L4.5,19.5573h9.75" />
                        <path d="M24,19.5573a4.875,4.875,0,0,1-9.75,0" />
                        <path d="M14.25,19.5573a4.875,4.875,0,0,1-9.75,0" />
                        <polygon points="33.75 19.557 24 19.557 24 7.094 32.142 7.094 33.75 19.557" />
                        <path d="M32.1418,7.0938h6.7158a2,2,0,0,1,1.9418,1.5207L43.5,19.5573H33.75" />
                        <path d="M24,19.5573a4.875,4.875,0,0,0,9.75,0" />
                        <path d="M33.75,19.5573a4.875,4.875,0,0,0,9.75,0" />
                        <path d="M7.3252,23.9768V38.9062a2,2,0,0,0,2,2h29.35a2,2,0,0,0,2-2V23.978" />
                        <path d="M33.4991,33.5687h3.9566a3.9771,3.9771,0,0,1-3.88,4.0721l-.0768.0012a4.0733,4.0733,0,1,1,0-8.1466,4.011,4.011,0,0,1,2.02.5357" />
                      </svg>

                      {/* Button text with proper z-index to appear above the background */}
                      <span className="relative z-10">Connect GBP</span>
                    </motion.button>
                  </div>
                </motion.div>
              </div>
            }
          >
            <div className="max-h-80 overflow-y-auto pr-2">
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Google Business References:</strong>{" "}
                {results.google_business.has_gmb_references ? "Yes" : "No"}
              </p>
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Google Business Links:</strong>{" "}
                {results.google_business.has_gmb_links ? "Yes" : "No"}
              </p>
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Google Business Embeds:</strong>{" "}
                {results.google_business.has_gmb_embeds ? "Yes" : "No"}
              </p>
              <p className="text-sm text-secondary/80 mb-2">
                <strong>Has Google Business Profile Mentions:</strong>{" "}
                {results.google_business.has_gbp_mentions ? "Yes" : "No"}
              </p>

              {results.google_business.gmb_urls &&
                results.google_business.gmb_urls.length > 0 && (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-secondary mb-1">
                      Google Business URLs:
                    </p>
                    <ul className="list-disc pl-5">
                      {results.google_business.gmb_urls.map((url, index) => (
                        <li
                          key={index}
                          className="text-sm text-secondary/80 break-all"
                        >
                          {url}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

              {results.google_business.gmb_embeds &&
                results.google_business.gmb_embeds.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-secondary mb-1">
                      Google Business Embeds:
                    </p>
                    <ul className="list-disc pl-5">
                      {results.google_business.gmb_embeds.map(
                        (embed, index) => (
                          <li
                            key={index}
                            className="text-sm text-secondary/80 break-all"
                          >
                            {embed}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}
      </div>
    </BoxPrimary>
  );
}
