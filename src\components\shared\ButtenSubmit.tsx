import React, { ComponentProps } from "react";
import { motion, HTMLMotionProps } from "framer-motion";

type ButtenSubmitType = {
  isLoading?: boolean;
  text: string;
  textloading?: string;
  icon?: React.ReactNode;
  prevIcon?: React.ReactNode;
  color?:
    | "primary"
    | "secondary"
    | "primary__outline"
    | "outline"
    | "outline-light"
    | "null"
    | "primary__outline_hover";
  classPluss?: string;
  classPlussSvg?: string;
} & Omit<HTMLMotionProps<"button">, "onSubmit">;

export default function ButtenSubmit({
  text,
  isLoading,
  classPlussSvg,
  classPluss,
  prevIcon,
  icon,
  textloading,
  color,
  ...other
}: ButtenSubmitType) {
  const getButtonClass = () => {
    if (color === "null") {
      return "border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-2 focus:ring-primary/20 focus:border-primary";
    }

    if (color) {
      return `btn btn--${color} focus:ring-2 focus:ring-primary/20`;
    }

    return "btn btn--primary focus:ring-2 focus:ring-primary/20";
  };

  return (
    <motion.button
      className={`${getButtonClass()} py-3 px-4 w-full text-sm font-medium rounded-lg  ${
        isLoading ? "opacity-80 cursor-not-allowed" : ""
      } ${classPluss}`}
      disabled={isLoading}
      whileTap={{ scale: 0.95 }}
      {...other}
    >
      <div className="flex justify-center items-center gap-2">
        {prevIcon && prevIcon}
        {isLoading && (
          <svg
            className={`w-5 h-5 text-current animate-spin ${
              classPlussSvg || ""
            }`}
            fill="none"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        <span>{isLoading ? textloading : text}</span>
        {icon && icon}
      </div>
    </motion.button>
  );
}
