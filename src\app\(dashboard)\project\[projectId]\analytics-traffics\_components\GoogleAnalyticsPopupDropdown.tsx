"use client";
import React, { useState } from "react";
import { IoChevronDown, IoAdd } from "react-icons/io5";
import Dropdown from "@/components/shared/DropDown";
import trashIcon from "@/../public/images/analytics-traffics/trash-2.svg";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/cn";
/* ========================================================================== */
type OptionItems = {
  id: number;
  title: string;
  icon?: React.ReactNode;
};
type TProps = {
  deleteButton?: boolean;
  label: string;
  icon?: React.ReactNode;
  options: OptionItems[];
  addNewButtonLabel?: string;
};
const GoogleAnalyticsPopupDropdown = ({
  deleteButton,
  label,
  icon,
  options,
  addNewButtonLabel: addNewButton,
}: TProps) => {
  const [closeModal] = useState<boolean>(false);
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="space-y-2">
      <p>{label}</p>
      <div className="flex gap-2">
        <Dropdown
          onClose={closeModal}
          width={"100%"}
          align="left"
          className="border border-primary-gray w-full p-4 rounded-md"
          button={<DropdownButton icon={icon} />}
        >
          <div className="p-2 w-full">
            {options.map((option) => (
              <div key={option.id} className="flex items-center gap-2">
                {option.icon}
                <p>{option.title}</p>
              </div>
            ))}
          </div>
        </Dropdown>
        {deleteButton && (
          <div className="border p-4 rounded-md border-primary-gray">
            <Image src={trashIcon} alt="trash icon" className="w-[24px]" />
          </div>
        )}
      </div>
      {addNewButton && (
        <Button variant={"link"} className="justify-start">
          <IoAdd /> <span>{addNewButton}</span>
        </Button>
      )}
    </div>
  );
};

const DropdownButton = ({
  icon,
  className,
}: {
  icon?: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex justify-between items-center", className)}>
      <div className="flex items-center gap-2">
        {icon}
        <span>digikala.com</span>
      </div>
      <IoChevronDown />
    </div>
  );
};

export default GoogleAnalyticsPopupDropdown;
