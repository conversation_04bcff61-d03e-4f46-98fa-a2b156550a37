import { SVGProps } from "react";

export function TagIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.9 2.1L9.2 3.8C8.9 4.1 8.9 4.7 9.2 5C9.5 5.3 10.1 5.3 10.4 5L12.1 3.3C12.4 3 12.4 2.4 12.1 2.1C11.8 1.8 11.2 1.8 10.9 2.1Z"
        fill="currentColor"
      />
      <path
        d="M9.5 7.5C10.3 7.5 11 6.8 11 6C11 5.2 10.3 4.5 9.5 4.5C8.7 4.5 8 5.2 8 6C8 6.8 8.7 7.5 9.5 7.5Z"
        fill="currentColor"
      />
      <path
        d="M14.7 9.7L21.7 2.7C22.1 2.3 22.1 1.7 21.7 1.3C21.3 0.9 20.7 0.9 20.3 1.3L13.3 8.3C12.9 8.7 12.9 9.3 13.3 9.7C13.7 10.1 14.3 10.1 14.7 9.7Z"
        fill="currentColor"
      />
      <path
        d="M18.3 14.3L20 12.6C20.4 12.2 20.4 11.6 20 11.2C19.6 10.8 19 10.8 18.6 11.2L16.9 12.9C16.5 13.3 16.5 13.9 16.9 14.3C17.3 14.7 17.9 14.7 18.3 14.3Z"
        fill="currentColor"
      />
      <path
        d="M20.4 16.4L19.5 15.5C19.1 15.1 18.5 15.1 18.1 15.5C17.7 15.9 17.7 16.5 18.1 16.9L19 17.8C19.4 18.2 20 18.2 20.4 17.8C20.8 17.4 20.8 16.8 20.4 16.4Z"
        fill="currentColor"
      />
      <path
        d="M17 21H7C5.9 21 5 20.1 5 19V5C5 3.9 5.9 3 7 3H10C10.6 3 11 2.6 11 2C11 1.4 10.6 1 10 1H7C4.8 1 3 2.8 3 5V19C3 21.2 4.8 23 7 23H17C19.2 23 21 21.2 21 19V16C21 15.4 20.6 15 20 15C19.4 15 19 15.4 19 16V19C19 20.1 18.1 21 17 21Z"
        fill="currentColor"
      />
    </svg>
  );
}

export default TagIcon;
