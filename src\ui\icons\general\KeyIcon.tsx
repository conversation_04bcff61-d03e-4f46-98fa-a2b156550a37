import { SVGProps } from "react";

export function KeyIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M18.3334 6.6958L20.3334 8.6958L17.3334 11.6958L15.3334 9.6958M12.0248 13.0044L21.3334 3.6958M13.3334 16.1958C13.3334 18.6791 11.3185 20.6958 8.83216 20.6958C6.34823 20.6958 4.33337 18.6791 4.33337 16.1958C4.33337 13.7125 6.34823 11.6958 8.83216 11.6958C11.3185 11.6958 13.3334 13.7125 13.3334 16.1958Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
