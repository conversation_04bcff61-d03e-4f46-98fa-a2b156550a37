"use client";
import NavbarCreateProject from "@/components/CreateProject/NavbarCreateProject";
import StepMobileCreateProject from "@/components/CreateProject/StepMobileCreateProject";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import Link from "next/link";
import { useMutation } from "@tanstack/react-query";
import { useCreateProjectStore } from "@/store/createProjectStore";
import PageTransition, {
  AnimatedElement,
} from "@/components/CreateProject/PageTransition";
import SearchEnginePageContent from "@/components/CreateProject/search-engines/SearchEnginePageContent";
import createProjectToast from "@/lib/createProjectToast";

export default function Page() {
  // Store hooks
  const { searchEngineConfigs, setCurrentStep, projectInfo, markStepComplete } =
    useCreateProjectStore();
  const route = useRouter();
  const { isEditMode, isProjectDataReady } = useEditProject();
  const { urls } = useEditNavigation();

  const { mutate, isPending } = useMutation({
    mutationFn: () => {
      // Validate that at least one configuration exists
      if (searchEngineConfigs.length === 0) {
        createProjectToast.warning.missingConfiguration("search engine");
        throw new Error(
          "Please add at least one search engine configuration before proceeding."
        );
      }

      // Show success message for configurations
      createProjectToast.success.searchEnginesConfigured(
        searchEngineConfigs.length
      );

      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(true);
        }, 500);
      });
    },
    onSuccess: () => {
      // Mark search engines step as complete
      markStepComplete("searchEngines");
      setCurrentStep("keywords");

      // Navigate to keywords with proper URL handling
      route.push(urls.keywords);
    },
    onError: (error: any) => {
      // Error handling is now done by createProjectToast
      console.error(error.message || "An error occurred");
    },
  });

  useEffect(() => {
    // Set current step
    setCurrentStep("search-engines");

    // In edit mode, wait for project data to be ready before any validation
    if (isEditMode && !isProjectDataReady) {
      return;
    }

    // Only redirect if not in edit mode and no project ID exists
    // Add additional check to ensure we're not in a loading state
    if (!isEditMode && !projectInfo?.id) {
      route.push("/create-project/project-information");
      return;
    }
  }, [setCurrentStep, projectInfo, route, isEditMode, isProjectDataReady]);

  return (
    <PageTransition>
      <div className="flex justify-between flex-col gap-6 lg:gap-8 h-full min-h-[calc(100vh-2rem)]">
        <div className="flex  gap-3 flex-col">
          <AnimatedElement variant="child">
            <NavbarCreateProject>Search engines</NavbarCreateProject>
          </AnimatedElement>
          <AnimatedElement variant="child" delay={0.1}>
            <StepMobileCreateProject />
          </AnimatedElement>

          {/* Main Content Card */}
          <AnimatedElement variant="card" delay={0.2}>
            <SearchEnginePageContent />
          </AnimatedElement>
        </div>
        {/* Action Buttons */}
        <AnimatedElement variant="card" delay={0.9}>
          <div className="flex flex-col sm:flex-row gap-3 justify-end bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
            <Link href={urls.projectInformation}>
              <ButtenSubmit
                text="Back"
                color="primary__outline_hover"
                classPluss="sm:w-auto w-full order-2 sm:order-1"
              />
            </Link>
            <ButtenSubmit
              type="button"
              text="Continue to Keywords"
              textloading="Setting up..."
              isLoading={isPending}
              onClick={() => mutate()}
              classPluss="sm:w-auto w-full order-1 sm:order-2"
            />
          </div>
        </AnimatedElement>
      </div>
    </PageTransition>
  );
}
