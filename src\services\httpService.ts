import axios, { AxiosRequestConfig } from "axios";
import storageService from "./storageService";
import authErrorHandler from "@/utils/authErrorHandler";

// Extended request config interface to include useAuth option
export interface ExtendedRequestConfig extends AxiosRequestConfig {
  useAuth?: boolean;
  skipAuthErrorHandling?: boolean; // Skip auth error handling for specific requests
}

// Use the API URL from environment variables
const baseURL = process.env.NEXT_PUBLIC_API_URL || "https://seoanalyser.com.au";

// Only log in development environment
if (process.env.NODE_ENV === "development") {
  console.log("HTTP Service initialized with baseURL:", baseURL);
}

const app = axios.create({
  baseURL,
  // Explicitly disable credentials to prevent CORS issues
  withCredentials: false,
});

// Add authorization token and custom headers to requests
app.interceptors.request.use(
  (config) => {
    // Cast to our extended type to access the useAuth property
    const extConfig = config as ExtendedRequestConfig;

    // Initialize headers if not present
    config.headers = config.headers || {};

    // CSRF token handling removed to fix CORS issues

    // Only add the auth token if useAuth is explicitly set to true
    if (extConfig.useAuth === true) {
      const token = storageService.getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    // Preserve custom headers if they were passed in the config
    // This ensures headers like X-Device-ID are properly passed through
    if (extConfig.headers) {
      Object.keys(extConfig.headers).forEach((key) => {
        if (extConfig.headers && extConfig.headers[key]) {
          config.headers[key] = extConfig.headers[key];
        }
      });
    }

    // Ensure secure connection
    if (
      config.url &&
      !config.url.startsWith("https://") &&
      !config.url.startsWith("/")
    ) {
      config.url = config.url.replace("http://", "https://");
    }

    return config;
  },
  (err) => Promise.reject(err)
);

// Token refresh state management
// Using a more robust approach with a promise-based queue
interface RefreshTokenState {
  isRefreshing: boolean;
  refreshPromise: Promise<boolean> | null;
  pendingRequests: Array<{
    resolve: (value: any) => void;
    reject: (reason: any) => void;
    config: any;
  }>;
}

const tokenRefreshState: RefreshTokenState = {
  isRefreshing: false,
  refreshPromise: null,
  pendingRequests: [],
};

// Function to process pending requests after token refresh
const processPendingRequests = (success: boolean) => {
  // Only log in development environment
  if (process.env.NODE_ENV === "development") {
    console.log(
      `Processing ${tokenRefreshState.pendingRequests.length} pending requests after token refresh (success: ${success})`
    );
  }

  tokenRefreshState.pendingRequests.forEach((request) => {
    if (success) {
      // Retry the request with the new token
      const token = storageService.getToken();
      if (token && request.config.headers) {
        request.config.headers.Authorization = `Bearer ${token}`;
      }
      app(request.config).then(request.resolve).catch(request.reject);
    } else {
      // If token refresh failed, reject all pending requests
      request.reject(new Error("Token refresh failed"));
    }
  });

  // Clear pending requests
  tokenRefreshState.pendingRequests = [];
};

// Function to refresh token with proper state management
const refreshAuthToken = async (): Promise<boolean> => {
  // If already refreshing, return the existing promise
  if (tokenRefreshState.isRefreshing && tokenRefreshState.refreshPromise) {
    if (process.env.NODE_ENV === "development") {
      console.log(
        "Token refresh already in progress, returning existing promise"
      );
    }
    return tokenRefreshState.refreshPromise;
  }

  if (process.env.NODE_ENV === "development") {
    console.log("Starting token refresh process");
  }
  tokenRefreshState.isRefreshing = true;

  // Create a new refresh promise
  tokenRefreshState.refreshPromise = (async () => {
    try {
      // Import auth service dynamically to avoid circular dependencies
      const authService = await import("./authService").then(
        (module) => module.default
      );
      const refreshSuccess = await authService.refreshToken();

      if (process.env.NODE_ENV === "development") {
        console.log(
          `Token refresh ${refreshSuccess ? "successful" : "failed"}`
        );
      }
      return refreshSuccess;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Error during token refresh:", error);
      }
      return false;
    } finally {
      // Reset state regardless of outcome
      tokenRefreshState.isRefreshing = false;
      tokenRefreshState.refreshPromise = null;
    }
  })();

  // Return the promise
  return tokenRefreshState.refreshPromise;
};

// Handle response errors
app.interceptors.response.use(
  (res) => {
    // Only log in development environment
    if (process.env.NODE_ENV === "development") {
      console.log(`API Success: ${res.config.url} - Status: ${res.status}`);
    }
    return res;
  },
  async (err) => {
    // Only log detailed errors in development environment
    // Skip logging for expected 404 errors on share API
    const isExpectedShareApi404 =
      err.response?.status === 404 &&
      err.config?.url?.includes("/api/share/") &&
      err.response?.data?.status === "error" &&
      err.response?.data?.message === "Analysis not found";

    if (process.env.NODE_ENV === "development" && !isExpectedShareApi404) {
      console.error(`API Error: ${err.config?.url || "unknown endpoint"}`);
      console.error(`Status: ${err.response?.status || "No response"}`);
      console.error(`Message: ${err.message}`);

      if (err.response?.data) {
        console.error("Error details:", err.response.data);
      }
    }

    // Get the extended config to check if we should skip auth error handling
    const extConfig = err.config as ExtendedRequestConfig;
    const originalRequest = err.config;

    // Handle 404 errors for share API endpoints - don't show error, just continue
    if (
      err.response &&
      err.response.status === 404 &&
      err.config?.url?.includes("/api/share/")
    ) {
      // For share API 404 errors, check if response matches expected format
      if (
        err.response.data &&
        err.response.data.status === "error" &&
        err.response.data.message === "Analysis not found"
      ) {
        // This is an expected 404 for share API - don't log as error in development
        if (process.env.NODE_ENV === "development") {
          console.log(`Share API: Analysis not found for ${err.config.url}`);
        }
        // Return the rejected promise without additional error handling
        return Promise.reject(err);
      }
    }

    // Handle 401 Unauthorized errors (token expired or invalid)
    if (err.response && err.response.status === 401) {
      // Skip token refresh for token refresh requests to avoid infinite loops
      const isTokenRefreshRequest =
        originalRequest.url?.includes("/token/refresh/");

      // Skip token refresh for auth endpoints to avoid infinite loops
      const isAuthEndpoint =
        originalRequest.url?.includes("/api/accounts/login/") ||
        originalRequest.url?.includes("/api/accounts/register/") ||
        originalRequest.url?.includes("/api/accounts/verify-email/");

      // Attempt token refresh if this is not a token refresh request itself
      if (
        !isTokenRefreshRequest &&
        !isAuthEndpoint &&
        !extConfig?.skipAuthErrorHandling
      ) {
        // If we're already refreshing, add this request to the queue and wait for the refresh to complete
        if (tokenRefreshState.isRefreshing) {
          if (process.env.NODE_ENV === "development") {
            console.log("Token refresh in progress, adding request to queue");
          }
          return new Promise((resolve, reject) => {
            tokenRefreshState.pendingRequests.push({
              resolve,
              reject,
              config: originalRequest,
            });
          });
        }

        try {
          // Attempt to refresh the token
          if (process.env.NODE_ENV === "development") {
            console.log("Attempting to refresh token for 401 response");
          }
          const refreshSuccess = await refreshAuthToken();

          if (refreshSuccess) {
            // Token refresh successful, retry the original request
            if (process.env.NODE_ENV === "development") {
              console.log(
                "Token refresh successful, retrying original request"
              );
            }
            const token = storageService.getToken();
            if (token && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }

            // Process any pending requests
            processPendingRequests(true);

            // Retry the original request
            return app(originalRequest);
          } else {
            // Token refresh failed, clear auth data
            if (process.env.NODE_ENV === "development") {
              console.log("Token refresh failed, clearing auth data");
            }
            storageService.clearAuth();

            // Process pending requests (they will be rejected)
            processPendingRequests(false);

            // Notify about auth error to show login modal (unless explicitly skipped)
            if (!extConfig?.skipAuthErrorHandling) {
              if (process.env.NODE_ENV === "development") {
                console.log(
                  "401 error detected and token refresh failed, triggering auth error handler"
                );
              }
              authErrorHandler.notifyAuthError();
            }

            // Return a rejected promise with the original error
            return Promise.reject(err);
          }
        } catch (refreshError) {
          // Error during token refresh, clear auth data
          if (process.env.NODE_ENV === "development") {
            console.error("Error during token refresh:", refreshError);
          }
          storageService.clearAuth();

          // Process pending requests (they will be rejected)
          processPendingRequests(false);

          // Notify about auth error to show login modal (unless explicitly skipped)
          if (!extConfig?.skipAuthErrorHandling) {
            if (process.env.NODE_ENV === "development") {
              console.log(
                "401 error detected and token refresh failed with error, triggering auth error handler"
              );
            }
            authErrorHandler.notifyAuthError();
          }

          // Return a rejected promise with the original error
          return Promise.reject(err);
        }
      } else {
        // For token refresh requests or auth endpoints, just clear auth data
        storageService.clearAuth();

        // Notify about auth error to show login modal (unless explicitly skipped)
        if (!extConfig?.skipAuthErrorHandling) {
          if (process.env.NODE_ENV === "development") {
            console.log(
              "401 error detected for auth endpoint, triggering auth error handler"
            );
          }
          authErrorHandler.notifyAuthError();
        }
      }
    }

    return Promise.reject(err);
  }
);

// Create wrapper functions that accept the extended config with useAuth option
const http = {
  get: (url: string, config?: ExtendedRequestConfig) => {
    return app.get(url, config);
  },
  post: (url: string, data?: any, config?: ExtendedRequestConfig) => {
    return app.post(url, data, config);
  },
  delete: (url: string, config?: ExtendedRequestConfig) => {
    return app.delete(url, config);
  },
  put: (url: string, data?: any, config?: ExtendedRequestConfig) => {
    return app.put(url, data, config);
  },
  patch: (url: string, data?: any, config?: ExtendedRequestConfig) => {
    return app.patch(url, data, config);
  },
};

// Subscription management types
export interface SubscriptionCancellationResponse {
  success: boolean;
  message: string;
  details: {
    current_period_end: string;
    cancel_at_period_end: boolean;
    access_until: string;
    will_auto_renew: boolean;
  };
}

export interface SubscriptionReactivationResponse {
  success: boolean;
  message: string;
  details?: {
    current_period_end: string;
    cancel_at_period_end: boolean;
    will_auto_renew: boolean;
  };
}

// Subscription management API methods
export const subscriptionAPI = {
  // Cancel subscription
  cancelSubscription: (
    subscriptionId?: string
  ): Promise<{ data: SubscriptionCancellationResponse }> => {
    return http.post(
      "/api/accounts/subscriptions/cancel/",
      subscriptionId ? { subscription_id: subscriptionId } : {},
      { useAuth: true }
    );
  },

  // Reactivate subscription
  reactivateSubscription: (
    subscriptionId?: string
  ): Promise<{ data: SubscriptionReactivationResponse }> => {
    return http.post(
      "/api/accounts/subscriptions/reactivate/",
      subscriptionId ? { subscription_id: subscriptionId } : {},
      { useAuth: true }
    );
  },
};

// Blog API methods
export const blogAPI = {
  // Get blog post by slug
  getBlogPostBySlug: (slug: string) => {
    return http.get(`/api/blog/posts/${slug}/`);
  },

  // Get blog posts with pagination and filters
  getBlogPosts: (params?: {
    page?: number;
    category?: string;
    q?: string;
    tag?: string;
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.category) searchParams.append("category", params.category);
    if (params?.q) searchParams.append("q", params.q);
    if (params?.tag) searchParams.append("tag", params.tag);

    const queryString = searchParams.toString();
    return http.get(`/api/blog/posts/${queryString ? `?${queryString}` : ""}`);
  },

  // Search blog posts
  searchBlogPosts: (query: string, page?: number) => {
    const searchParams = new URLSearchParams();
    searchParams.append("q", query);
    if (page) searchParams.append("page", page.toString());

    return http.get(`/api/blog/search/?${searchParams.toString()}`);
  },
};

// White Label API methods
export const whiteLabelAPI = {
  // Get white label settings
  getSettings: () => {
    return http.get("/api/accounts/whitelabel-setting/", { useAuth: true });
  },

  // Save white label settings
  saveSettings: (data: FormData) => {
    return http.post("/api/accounts/whitelabel-setting/", data, {
      useAuth: true,
      timeout: 10000,
    });
  },

  // Update white label settings
  updateSettings: (data: FormData) => {
    return http.patch("/api/accounts/whitelabel-setting/", data, {
      useAuth: true,
      timeout: 10000,
    });
  },
};

export default http;
