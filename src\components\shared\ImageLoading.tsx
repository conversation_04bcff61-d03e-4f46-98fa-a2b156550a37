"use client";

import Image, { StaticImageData } from "next/image";
import React, { useState } from "react";
type ImageType = {
    src: any;
    alt: string | null;
    className?: string;
    width?: number;
    height?: number;
    classPlus?: string;
    figureClass?: string;
    onClick?: (value: any) => void
};
export default function ImageLoading({
    width,
    height,
    src,
    alt,
    className,
    classPlus,
    figureClass,
    onClick
}: ImageType) {
    const [load, setLoad] = useState<boolean>(true);
    const [error, setError] = useState<StaticImageData | null>(null)
    const classImage = className ? className : classPlus ? `${classPlus} rounded-md shadow-md  table mx-auto` : "w-full h-auto table mx-auto object-fill"
    return (
        <figure className={figureClass || "w-full relative"}>
            <Image
                width={width}
                height={height}
                loading="lazy"
                onClick={onClick}
                // placeholder="blur"
                // blurDataURL="data:image/gif;base64,..."
                onLoad={() => setLoad(false)}
                src={error ? error : src}
                alt={alt || "error"}
                className={classImage}
            // onError={() => setError(ImageError)}
            />
            {load &&
                <div className="absolute left-1/2 top-1/2 -translate-y-1/2 transform -translate-x-1/2 z-[1000]">
                    <svg
                        className={"w-5 h-5 text-[#914ac4] animate-spin "}
                        fill="none"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                        ></circle>
                        <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                </div>
            }
        </figure>
    );
}
