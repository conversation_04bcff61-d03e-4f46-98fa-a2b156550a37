"use client";

import * as React from "react";
import { addDays } from "date-fns";
import { DatePickerRange } from "./DatePickerRange";

export function DatePickerWithPresets() {
  const [state, setState] = React.useState<{ startDate: Date; endDate: Date } | undefined>(() => ({
    startDate: new Date(),
    endDate: addDays(new Date(), 7),
  }));

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold text-foreground">
          Date Picker with Presets
        </h2>
        <p className="text-muted-foreground">
          Date range picker with visible preset buttons matching the design
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-foreground">
            Date Range Picker with Presets
          </h3>
          <p className="text-sm text-muted-foreground">
            Preset buttons on the left, calendar on the right with proper configuration
          </p>
        </div>
        
        <DatePickerRange
          value={state}
          onChange={setState}
          showPresets={true}
          className="max-w-md"
        />
        
        {state && (
          <div className="text-sm text-muted-foreground">
            Selected: {state.startDate?.toDateString()} 
            {state.endDate && ` - ${state.endDate.toDateString()}`}
          </div>
        )}
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-foreground">
            Simple Date Range Picker (No Presets)
          </h3>
          <p className="text-sm text-muted-foreground">
            Original layout without preset buttons
          </p>
        </div>
        
        <DatePickerRange
          value={state}
          onChange={setState}
          showPresets={false}
          className="max-w-md"
        />
      </div>
    </div>
  );
}
