import { useIsFetching } from "@tanstack/react-query";
export default function LoadingQuery() {
  const isLoad = useIsFetching();
  if (!isLoad) return;
  return (
    <div className="p-4 flex z-[1000] items-center gap-3 justify-center shadow rounded-md bg-white fixed bottom-5 transform opacity-90 -translate-x-1/2 left-1/2">
      <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      <p className="text-secondary">Loading Data ...</p>
    </div>
  );
}