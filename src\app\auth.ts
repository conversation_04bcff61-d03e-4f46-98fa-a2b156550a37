import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

export const { handlers, signIn, ut, auth } = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        confirm_password: { label: "Confirm Password", type: "password", optional: true },
      },
      async authorize(credentials) {
        if (!credentials) return null;

        const isSignUp = credentials.confirm_password !== undefined;

        const url = isSignUp
          ? `${process.env.NEXT_PUBLIC_API_URL}/api/accounts/register` 
          : `${process.env.NEXT_PUBLIC_API_URL}/api/accounts/login` 

        const res = await fetch(url, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            email: credentials.email,
            password: credentials.password,
            ...(isSignUp ? { confirm_password: credentials.confirm_password } : {}),
          }),
        });

        if (!res.ok) throw new Error("Failed to authenticate");

        const user = await res.json();

        if (user && user.id) {
          return user;
        }

        return null;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && typeof token === "object") {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
      }
      return session;
    },
  },
  pages: {
    signIn: "/signin",
  },
  session: { strategy: "jwt" },
});
