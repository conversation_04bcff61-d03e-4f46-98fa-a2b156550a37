import { SVGProps } from "react";

export function JsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="65"
      height="65"
      viewBox="0 0 65 65"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M52.1667 22.3887H38.1667V8.38867L52.1667 22.3887Z"
        fill="currentColor"
      />
      <path
        d="M53.5817 20.9737L39.5817 6.97367C39.3959 6.788 39.1752 6.64078 38.9324 6.5404C38.6897 6.44002 38.4295 6.38847 38.1667 6.38867H14.1667C13.1059 6.38867 12.0885 6.8101 11.3383 7.56025C10.5882 8.31039 10.1667 9.32781 10.1667 10.3887V28.3887C10.1667 28.9191 10.3775 29.4278 10.7525 29.8029C11.1276 30.178 11.6363 30.3887 12.1667 30.3887C12.6972 30.3887 13.2059 30.178 13.581 29.8029C13.956 29.4278 14.1667 28.9191 14.1667 28.3887V10.3887H36.1667V22.3887C36.1667 22.9191 36.3775 23.4278 36.7525 23.8029C37.1276 24.178 37.6363 24.3887 38.1667 24.3887H50.1667V54.3887H44.1667C43.6363 54.3887 43.1276 54.5994 42.7525 54.9745C42.3775 55.3495 42.1667 55.8582 42.1667 56.3887C42.1667 56.9191 42.3775 57.4278 42.7525 57.8029C43.1276 58.178 43.6363 58.3887 44.1667 58.3887H50.1667C51.2276 58.3887 52.245 57.9672 52.9952 57.2171C53.7453 56.467 54.1667 55.4495 54.1667 54.3887V22.3887C54.167 22.126 54.1154 21.8658 54.015 21.623C53.9146 21.3802 53.7674 21.1595 53.5817 20.9737ZM40.1667 13.2162L47.3392 20.3887H40.1667V13.2162ZM37.1192 49.4662C37.0413 50.2311 36.7948 50.9693 36.3976 51.6276C36.0003 52.286 35.4622 52.8481 34.8217 53.2737C33.5242 54.1387 31.9167 54.3887 30.4492 54.3887C29.1725 54.381 27.9017 54.213 26.6667 53.8887C26.4108 53.8208 26.1709 53.7027 25.9609 53.5414C25.751 53.38 25.5751 53.1786 25.4436 52.9487C25.3121 52.7189 25.2276 52.4652 25.1949 52.2025C25.1622 51.9397 25.182 51.6731 25.2532 51.418C25.3244 51.1629 25.4455 50.9246 25.6095 50.7167C25.7735 50.5088 25.9772 50.3356 26.2087 50.207C26.4402 50.0785 26.6949 49.9972 26.9581 49.9678C27.2213 49.9385 27.4876 49.9617 27.7417 50.0362C28.8367 50.3362 31.4917 50.7112 32.6292 49.9462C32.8492 49.7987 33.0868 49.5662 33.1642 48.9637C33.2517 48.2962 32.9867 47.9387 29.9692 47.0662C27.6317 46.3912 23.7192 45.2587 24.2192 41.2887C24.2972 40.5388 24.5392 39.8153 24.9281 39.1694C25.3169 38.5235 25.8431 37.971 26.4692 37.5512C29.4292 35.5512 34.1467 36.7237 34.6767 36.8612C35.1899 36.9961 35.6285 37.3294 35.896 37.7877C36.1635 38.246 36.2379 38.7917 36.103 39.3049C35.9681 39.8181 35.6348 40.2567 35.1765 40.5242C34.7182 40.7916 34.1724 40.8661 33.6592 40.7312C32.5367 40.4387 29.8517 40.0912 28.7017 40.8712C28.551 40.9729 28.4269 41.1093 28.3399 41.269C28.2529 41.4286 28.2055 41.6069 28.2017 41.7887C28.1717 42.0137 28.1667 42.0612 28.4792 42.2637C29.0567 42.6362 30.0917 42.9337 31.0917 43.2237C33.5392 43.9312 37.6792 45.1387 37.1192 49.4662ZM20.1667 38.3887V47.8887C20.1667 49.6126 19.4819 51.2659 18.2629 52.4849C17.044 53.7039 15.3907 54.3887 13.6667 54.3887C11.9428 54.3887 10.2895 53.7039 9.07055 52.4849C7.85157 51.2659 7.16675 49.6126 7.16675 47.8887C7.16675 47.3582 7.37746 46.8495 7.75253 46.4745C8.12761 46.0994 8.63632 45.8887 9.16675 45.8887C9.69718 45.8887 10.2059 46.0994 10.581 46.4745C10.956 46.8495 11.1667 47.3582 11.1667 47.8887C11.1667 48.5517 11.4301 49.1876 11.899 49.6564C12.3678 50.1253 13.0037 50.3887 13.6667 50.3887C14.3298 50.3887 14.9657 50.1253 15.4345 49.6564C15.9034 49.1876 16.1667 48.5517 16.1667 47.8887V38.3887C16.1667 37.8582 16.3775 37.3495 16.7525 36.9745C17.1276 36.5994 17.6363 36.3887 18.1667 36.3887C18.6972 36.3887 19.2059 36.5994 19.581 36.9745C19.956 37.3495 20.1667 37.8582 20.1667 38.3887Z"
        fill="currentColor"
      />
    </svg>
  );
}
