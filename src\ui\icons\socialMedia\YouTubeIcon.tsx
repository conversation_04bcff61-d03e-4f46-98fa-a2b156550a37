import { SVGProps } from "react";

export function YouTubeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_3890)">
        <path
          d="M23.4985 6.22548C23.3657 5.74503 23.1141 5.30891 22.7669 4.96177C22.4197 4.61462 21.9836 4.36301 21.5032 4.23018C19.6252 3.69018 12.0002 3.69018 12.0002 3.69018C12.0002 3.69018 4.37524 3.69018 2.49724 4.23018C2.01682 4.36301 1.58074 4.61462 1.23354 4.96177C0.886335 5.30891 0.634671 5.74503 0.501945 6.22548C-0.0380547 10.1255 -0.0380547 14.0655 0.501945 17.9655C0.634671 18.4459 0.886335 18.8821 1.23354 19.2292C1.58074 19.5764 2.01682 19.828 2.49724 19.9608C4.37524 20.5008 12.0002 20.5008 12.0002 20.5008C12.0002 20.5008 19.6252 20.5008 21.5032 19.9608C21.9836 19.828 22.4197 19.5764 22.7669 19.2292C23.1141 18.8821 23.3657 18.4459 23.4985 17.9655C24.0385 14.0655 24.0385 10.1255 23.4985 6.22548Z"
          fill="#FF0000"
        />
        <path
          d="M9.60059 15.4255L15.8406 12.0955L9.60059 8.76547V15.4255Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_204_3890">
          <rect width="24" height="24" fill="white"/>
        </clipPath>
      </defs>
    </svg>
  );
}
