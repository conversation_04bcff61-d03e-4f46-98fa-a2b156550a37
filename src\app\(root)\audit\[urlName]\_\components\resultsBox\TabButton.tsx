"use client";

type Props = {
  value: string;
  label: string;
  isActive: boolean;
  onClick: (value: string) => void;
  className?: string;
};

export default function TabButton({
  isActive,
  label,
  onClick,
  value,
  className = "",
}: Props) {
  return (
    <button
      onClick={() => onClick(value)}
      className={`${
        isActive
          ? "bg-white text-primary font-semibold border-t border-l border-r border-gray-200 shadow-sm"
          : "bg-gray-50 text-secondary/70 hover:bg-gray-100 border-b border-gray-200"
      } px-2 sm:px-4 md:px-6 lg:px-8 py-2 lg:py-3 rounded-t-lg text-xs sm:text-sm transition-all duration-200 ${className}`}
    >
      {label}
    </button>
  );
}
