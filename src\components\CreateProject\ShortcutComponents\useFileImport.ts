import { useCallback } from "react";
import * as ExcelJS from "exceljs";

interface KeywordTableRow {
  id: string;
  keyword: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
  volume: string;
  configId: string;
}

interface UseFileImportProps {
  keywords: KeywordTableRow[];
  setKeywords: React.Dispatch<React.SetStateAction<KeywordTableRow[]>>;
  dataState: {
    searchEngines: any[];
    country: any;
    language: any;
  };
  setError: React.Dispatch<React.SetStateAction<string>>;
}

export const useFileImport = ({
  keywords,
  setKeywords,
  dataState,
  setError,
}: UseFileImportProps) => {
  const addKeywordsToTable = useCallback(
    (importedKeywords: string[]) => {
      importedKeywords.forEach((keyword) => {
        if (
          dataState.searchEngines &&
          dataState.country &&
          dataState.language
        ) {
          // Check if keyword already exists
          if (
            keywords.some(
              (k) => k.keyword.toLowerCase() === keyword.toLowerCase()
            )
          ) {
            return; // Skip duplicate keywords
          }

          const newKeyword: KeywordTableRow = {
            id:
              Date.now().toString() +
              Math.random().toString(36).substring(2, 7),
            keyword: keyword,
            searchEngine: dataState.searchEngines[0],
            country: dataState.country,
            language: dataState.language,
            location: dataState.country
              ? { name: dataState.country.name }
              : null,
            volume: "21k",
            configId: Date.now().toString(),
          };

          setKeywords((prev) => [...prev, newKeyword]);
        }
      });

      setError("");
    },
    [keywords, dataState, setKeywords, setError]
  );

  const handleFileChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      const fileExtension = file.name.split(".").pop()?.toLowerCase();

      try {
        if (fileExtension === "xlsx" || fileExtension === "xls") {
          // Handle Excel files
          const reader = new FileReader();
          reader.onload = async (e) => {
            try {
              const buffer = e.target?.result as ArrayBuffer;
              const workbook = new ExcelJS.Workbook();
              await workbook.xlsx.load(buffer);

              // Get the first worksheet
              const worksheet = workbook.worksheets[0];
              if (!worksheet) {
                setError("No worksheet found in the Excel file");
                return;
              }

              const keywords: string[] = [];

              // Try to find keywords in the worksheet
              // First, check if there's a 'name' column header
              const headerRow = worksheet.getRow(1);
              let nameColumnIndex = -1;

              headerRow.eachCell((cell, colNumber) => {
                if (
                  cell.value &&
                  cell.value.toString().toLowerCase() === "name"
                ) {
                  nameColumnIndex = colNumber;
                }
              });

              if (nameColumnIndex > 0) {
                // Extract keywords from the 'name' column
                worksheet.eachRow((row, rowNumber) => {
                  if (rowNumber > 1) {
                    // Skip header row
                    const cell = row.getCell(nameColumnIndex);
                    const value = cell.value;
                    if (
                      value &&
                      typeof value === "string" &&
                      value.trim().length > 0
                    ) {
                      keywords.push(value.trim());
                    }
                  }
                });
              } else {
                // Extract keywords from the first column
                worksheet.eachRow((row) => {
                  const cell = row.getCell(1);
                  const value = cell.value;
                  if (
                    value &&
                    typeof value === "string" &&
                    value.trim().length > 0
                  ) {
                    keywords.push(value.trim());
                  }
                });
              }

              // Add imported keywords to the table
              addKeywordsToTable(keywords);
            } catch (err) {
              setError(
                "Error processing Excel file. Please check the file format."
              );
              console.error(err);
            }
          };

          reader.onerror = () => {
            setError("Failed to read Excel file. Please try again.");
          };

          reader.readAsArrayBuffer(file);
        } else {
          // Handle text and CSV files
          const reader = new FileReader();
          reader.onload = (e) => {
            const content = e.target?.result as string;
            if (!content) return;

            // Parse keywords from file content
            let keywords: string[] = [];

            if (fileExtension === "csv") {
              // For CSV files, split by lines and take the first column
              keywords = content
                .split("\n")
                .map((line) => {
                  const firstColumn = line.split(",")[0];
                  return firstColumn
                    ? firstColumn.trim().replace(/"/g, "")
                    : "";
                })
                .filter((line) => line.length > 0);
            } else {
              // For text files, assume one keyword per line
              keywords = content
                .split("\n")
                .map((line) => line.trim())
                .filter((line) => line.length > 0);
            }

            // Add imported keywords to the table
            addKeywordsToTable(keywords);
          };

          reader.onerror = () => {
            setError("Failed to read file. Please try again.");
          };

          reader.readAsText(file);
        }
      } catch (err) {
        setError("Error processing file. Please try again.");
        console.error(err);
      }

      // Reset file input
      event.target.value = "";
    },
    [addKeywordsToTable, setError]
  );

  return { handleFileChange };
};
