"use client";
import { motion } from "framer-motion";

export default function ContactBanner() {
  return (
    <div className="w-full pt-1 lg:py-4 lg:pt-12">
      <div className="container mx-auto ">
        <div
          className="relative w-full overflow-hidden rounded-2xl shadow-xl aspect-16/8 lg:aspect-16/3 "
          style={{
            backgroundImage: `url('/images/background.png')`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          {/* Enhanced color overlay with reduced opacity for better pattern visibility */}
          <div className="absolute inset-0 bg-primary/90"></div>

          {/* Content */}
          <div className="relative z-20 flex items-center justify-center h-full px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
            >
              {/* Badge */}
              <motion.div
                className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-white/40 backdrop-blur-sm rounded-full mb-4 sm:mb-6"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              >
                <span className="text-white text-xs sm:text-sm font-medium tracking-wide uppercase">
                  Let’s connect
                </span>
              </motion.div>

              {/* Main heading */}
              <motion.h1
                className="text-3xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.6, ease: "easeOut" }}
              >
                Get In Touch
              </motion.h1>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
