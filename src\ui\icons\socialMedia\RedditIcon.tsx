import { SVGProps } from "react";

export function RedditIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_3885)">
        <path
          d="M12 24.001C18.6274 24.001 24 18.6284 24 12.001C24 5.37356 18.6274 0.000976562 12 0.000976562C5.37258 0.000976562 0 5.37356 0 12.001C0 18.6284 5.37258 24.001 12 24.001Z"
          fill="#FF4500"
        />
        <path
          d="M19.9828 12.1149C19.9828 11.1421 19.1962 10.3695 18.2375 10.3695C17.7846 10.3689 17.349 10.5432 17.0216 10.856C15.8199 9.99754 14.1746 9.43973 12.3434 9.3682L13.1444 5.61998L15.7481 6.17779C15.7769 6.83591 16.3204 7.36532 16.993 7.36532C17.6796 7.36532 18.2375 6.80751 18.2375 6.12051C18.2375 5.43388 17.6797 4.87598 16.993 4.87598C16.5066 4.87598 16.0772 5.1621 15.877 5.57704L12.973 4.96185C12.8871 4.94741 12.8012 4.96185 12.7439 5.00479C12.6724 5.04773 12.6296 5.11916 12.6154 5.20504L11.7281 9.38235C9.86843 9.43973 8.19452 9.99754 6.97859 10.8704C6.65108 10.5576 6.21544 10.3833 5.76256 10.384C4.78971 10.384 4.01721 11.1706 4.01721 12.1293C4.01721 12.8445 4.44631 13.4453 5.04734 13.7174C5.01815 13.8923 5.00379 14.0693 5.0044 14.2467C5.0044 16.936 8.13753 19.125 12.0001 19.125C15.8628 19.125 18.9959 16.9504 18.9959 14.2467C18.9959 14.0694 18.9816 13.8923 18.953 13.7174C19.5537 13.4453 19.9828 12.8301 19.9828 12.1149ZM7.99427 13.3594C7.99427 12.6728 8.55209 12.1149 9.23909 12.1149C9.92571 12.1149 10.4836 12.6727 10.4836 13.3594C10.4836 14.0461 9.92581 14.6042 9.23909 14.6042C8.55218 14.6183 7.99427 14.0461 7.99427 13.3594ZM14.9615 16.6642C14.1031 17.5227 12.4721 17.5799 12.0001 17.5799C11.5137 17.5799 9.88287 17.5082 9.03856 16.6642C8.91002 16.5355 8.91002 16.3351 9.03856 16.2064C9.16737 16.0779 9.36762 16.0779 9.49643 16.2064C10.0402 16.7502 11.1847 16.936 12.0001 16.936C12.8156 16.936 13.9743 16.7501 14.5036 16.2064C14.6324 16.0779 14.8327 16.0779 14.9615 16.2064C15.0759 16.3351 15.0759 16.5355 14.9615 16.6642ZM14.7325 14.6184C14.0457 14.6184 13.4879 14.0606 13.4879 13.3739C13.4879 12.6871 14.0457 12.1293 14.7325 12.1293C15.4194 12.1293 15.9772 12.6871 15.9772 13.3739C15.9772 14.046 15.4194 14.6184 14.7325 14.6184Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_204_3885">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
