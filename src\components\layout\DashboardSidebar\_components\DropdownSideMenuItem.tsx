import React, { forwardRef } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

/* ================================== TYPES ================================= */
import type { TDropdownSideMenuItemProps } from "../Types";

/* ========================================================================== */
const DropdownSideMenuItem = forwardRef<
  HTMLDivElement,
  TDropdownSideMenuItemProps
>(({ href, title, themeColor }, ref) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const pathname = usePathname();

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Link className="w-[236px]" href={href}>
      <div className="pl-6 py-2 rounded-md" ref={ref}>
        <span
          style={{ color: pathname === href ? themeColor : "#344054" }}
          className={`text-sm ${
            pathname === href ? "text-primary" : "text-secondary"
          } transition-colors duration-300 ease-in-out`}
        >
          {title}
        </span>
      </div>
    </Link>
  );
});
DropdownSideMenuItem.displayName = "DropdownSideMenuItem";

export default DropdownSideMenuItem;
