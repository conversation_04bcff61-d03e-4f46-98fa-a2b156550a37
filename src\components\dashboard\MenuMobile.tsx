import { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { HiChevronDown } from "react-icons/hi";
import Link from "next/link";
import Image from "next/image";

import { usePathname, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { CrossIcon, MenuHamburgerIcon } from "@/ui/icons/general";

type MenuMobileType = {
  url: string
  name: string
  step?: string
  icon?: React.ReactNode
  children?: MenuMobileType[] | undefined
}

export default function MenuMobile({ menuItems }: { menuItems: MenuMobileType[] }) {
  const [openMenu, setOpenMenu] = useState(false);
  const search = useSearchParams()
  useEffect(() => {
    setOpenMenu(false)
  }, [search])

  return (
    <>
      <button className="ml-auto" title="open menu" onClick={() => setOpenMenu(true)}>
        <MenuHamburgerIcon className="w-8 h-8 text-secondary" />
      </button>

      <AnimatePresence>
        {openMenu && (
          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="fixed top-0 right-0 w-full h-full bg-gray-100 p-4 z-50"
          >
            {/* Header Menu*/}
            <div className="flex items-center justify-between mb-6">
              <Link
                href="/"
                className="w-[60px] h-[30px] lg:w-[80px] lg:h-[45px] relative inline-block"
              >
                <Image
                  src="/images/appLogo.svg"
                  alt="seo analyser logo"
                  fill
                  quality={100}
                  className="w-full h-full"
                  priority
                />
              </Link>
              <button title="close menu" onClick={() => setOpenMenu(false)}>
                <CrossIcon className="w-6 h-6 text-secondary" />
              </button>
            </div>

            {/* Menu Items */}
            <div className="space-y-4">
              {menuItems.map((item, i) => (
                <MenuItem key={i} item={item} />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
const MenuItem = ({ item }: { item: MenuMobileType }) => {
  const [open, setOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const path = usePathname();
  const search = useSearchParams();
  const childrens = item?.children?.length ? true : false;

  const classLink = `${path === item.url
    ? "text-purple-600/80 bg-purple-100/70 shadow"
    : ""
    } p-3 transition-all w-full flex items-center justify-between gap-2 rounded-md`;

  return (
    <div className="flex flex-col gap-1">
      <div onClick={() => setOpen(!open)} className={classLink}>
        {childrens ? (
          <>
            <span className="flex items-center gap-2">
              {item.icon}
              {item.name}
            </span>
            <div
              className={`transition-transform duration-300 ${open ? "rotate-180" : ""
                }`}
            >
              <HiChevronDown className="text-xl" />
            </div>
          </>
        ) : (
          <Link href={item.url} className="flex items-center gap-2 w-full">
            {item.icon}
            {item.name}
          </Link>
        )}
      </div>

      {childrens && (
        <div
          ref={contentRef}
          className="transition-height"
          style={{
            maxHeight: open ? contentRef.current?.scrollHeight : 0,
            opacity: open ? 1 : 0,
          }}
        >
          <div className="mt-0 space-y-2 bg-white p-3 rounded-b-md text-gray-600">
            {item.children?.map((sub: MenuMobileType, i: number) => (
              <Link
                href={sub.url}
                key={i}
                className={cn(
                  "p-2 rounded-md block transition-colors w-full",
                  sub.step === search?.get("step")
                    ? "text-purple-600/80 bg-purple-100/70 shadow"
                    : "hover:bg-gray-100"
                )}
              >
                {sub.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
