## User Overview API

**Endpoint:**  
`GET /api/dashboard/project/analytics-traffics/users-overview`

---

## Query Parameters

| Name           | Type     | Required | Description                           |
| -------------- | -------- | -------- | ------------------------------------- |
| `userOverview` | `string` | Yes      | The selected metric to filter the dat |

---

## Acceptable Values for `userOverview`

_(Case-sensitive! `age` ≠ `Age`)_

- `Age`
- `Device`
- `Gender`
- `Cities`
- `Countries`

---

## Response Schema

```ts
{
    leftMap: {
        [ISO3CountryCode]: "percent, total users, growth percentage",
    },
    rightMap: {
        [ISO3CountryCode]: "percent, total users, growth percentage",
    },
    progressbarData: [
        {
            title: string,
            percentage: number
        },
    ]
}
```

```json
{
  "leftMap": {
    "USA": "20, 2000, 2",
    "CAN": "50, 5000, 5",
    "IRN": "100, 10000, 10"
  },
  "rightMap": {
    "USA": "20, 2000, 2",
    "IRN": "100, 10000, 10"
  },
  "progressbarData": [
    {
      "title": "under 18",
      "percentage": 50
    }
  ]
}
```

## Important Notes

> ℹ️ The leftMap and rightMap use ISO 3166-1 Alpha-3 country codes as keys.
These keys may differ between leftMap and rightMap.

> ℹ️ The value format inside each map entry is a string in the form:
"percent, total users, growth percentage" (e.g., "50, 5000, 5")


