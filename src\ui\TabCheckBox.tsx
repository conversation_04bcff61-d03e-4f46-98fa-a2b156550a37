"use client";

type Props = {
  value: string;
  label: string;
  checked: boolean;
  onClick: (value: string) => void;
};

export default function TabCheckBox({ checked, label, onClick, value }: Props) {
  return (
    <button
      onClick={() => onClick(value)}
      className={`${
        checked
          ? "text-primary border-primary font-bold"
          : "text-secondary/60 border-light-gray"
      } px-6 py-2 border rounded-lg text-sm`}
    >
      {label}
    </button>
  );
}
