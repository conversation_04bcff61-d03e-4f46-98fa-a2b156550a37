# Analytics Connection System

This document explains the comprehensive analytics connection state management system for Google Analytics and Search Console integration across all analytics-traffics pages.

## Overview

The system provides:
- **Global connection state management** across all analytics-traffics pages
- **Automatic connection status checking** before API calls
- **Clean, modular hooks** for different service requirements
- **Consistent error handling** and loading states
- **Developer-friendly API** with TypeScript support

## Architecture

### 1. Context Provider (`AnalyticsConnectionContext`)

Provides global state management for Google Analytics and Search Console connections.

```typescript
// Automatically provided at analytics-traffics layout level
<AnalyticsConnectionProvider projectId={projectId}>
  {children}
</AnalyticsConnectionProvider>
```

### 2. Connection Status Hook (`useAnalyticsConnection`)

Access connection status and actions from any component:

```typescript
const {
  isGoogleAnalyticsConnected,
  isGoogleSearchConsoleConnected,
  connectGA,
  connectGSC,
  gaConnecting,
  gscConnecting,
} = useAnalyticsConnection();
```

### 3. API Hooks (`useAnalyticsAPI`)

Smart hooks that automatically check connection status before making API calls:

```typescript
// For Google Analytics data
const { data, isLoading, error } = useGoogleAnalyticsAPI(
  ["traffic-data", tab, filter],
  () => fetchTrafficData(tab, filter)
);

// For Google Search Console data
const { data, isLoading, error } = useGoogleSearchConsoleAPI(
  ["gsc-data", page, filterBy],
  () => fetchGSCData(page, filterBy)
);

// For data requiring both services
const { data, isLoading, error } = useBothServicesAPI(
  ["combined-data", dateRange],
  () => fetchCombinedData(dateRange)
);
```

## Usage Examples

### Basic Component with Google Analytics Data

```typescript
import { useGoogleAnalyticsAPI } from "@/hooks/useAnalyticsAPI";

const TrafficChart = ({ tab, filter }) => {
  const { 
    data, 
    isLoading, 
    error, 
    isConnectionReady,
    connectionStatus 
  } = useGoogleAnalyticsAPI(
    ["traffic-chart", tab, filter],
    async () => {
      const response = await AXIOS.get("/api/traffic-data", {
        params: { tab, filter }
      });
      return response.data;
    }
  );

  if (!isConnectionReady) {
    return <div>Google Analytics connection required</div>;
  }

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return <Chart data={data} />;
};
```

### Component with Connection Actions

```typescript
import { useAnalyticsConnection } from "@/contexts/AnalyticsConnectionContext";

const ConnectionStatus = () => {
  const {
    isGoogleAnalyticsConnected,
    isGoogleSearchConsoleConnected,
    connectGA,
    connectGSC,
    gaConnecting,
    gscConnecting,
  } = useAnalyticsConnection();

  return (
    <div>
      <button 
        onClick={connectGA} 
        disabled={gaConnecting || isGoogleAnalyticsConnected}
      >
        {gaConnecting ? "Connecting..." : 
         isGoogleAnalyticsConnected ? "GA Connected" : "Connect GA"}
      </button>
      
      <button 
        onClick={connectGSC} 
        disabled={gscConnecting || isGoogleSearchConsoleConnected}
      >
        {gscConnecting ? "Connecting..." : 
         isGoogleSearchConsoleConnected ? "GSC Connected" : "Connect GSC"}
      </button>
    </div>
  );
};
```

## Migration Guide

### Before (Old Pattern)

```typescript
const useOldHook = ({ tab, filter }) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["data", projectId, tab, filter],
    queryFn: async () => {
      if (!projectId) throw new Error("Project ID required");
      
      const { data } = await AXIOS.get("/api/data", {
        params: { projectId, tab, filter }
      });
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};
```

### After (New Pattern)

```typescript
const useNewHook = ({ tab, filter }) => {
  return useGoogleAnalyticsAPI(
    ["data", tab, filter],
    async () => {
      const { data } = await AXIOS.get("/api/data", {
        params: { tab, filter } // projectId automatically included
      });
      return data;
    }
  );
};
```

## Benefits

1. **Automatic Connection Checking**: API calls only execute when services are connected
2. **Consistent Error Handling**: Standardized error messages for connection issues
3. **Better Developer Experience**: Clear TypeScript types and helpful error messages
4. **Performance**: Prevents unnecessary API calls when services aren't connected
5. **Maintainability**: Centralized connection logic reduces code duplication

## File Structure

```
src/
├── contexts/
│   └── AnalyticsConnectionContext.tsx    # Global state provider
├── hooks/
│   ├── useAnalyticsServices.ts           # Core connection logic
│   └── useAnalyticsAPI.ts                # Smart API hooks
└── app/(dashboard)/project/[projectId]/analytics-traffics/
    ├── layout.tsx                        # Context provider setup
    └── [pages]/                          # All pages have access to context
```

## Best Practices

1. **Use the appropriate hook** for your data requirements:
   - `useGoogleAnalyticsAPI` for GA data
   - `useGoogleSearchConsoleAPI` for GSC data
   - `useBothServicesAPI` for combined data

2. **Handle connection states** gracefully in your UI
3. **Use TypeScript** for better development experience
4. **Cache data appropriately** using staleTime and retry options
5. **Test connection flows** thoroughly in development
