"use client";

import React, { lazy, useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import GoogleConnectedHeader from "./GoogleConnectedHeader";
import GSCOverview from "../(overview)/gsc-overview/GSCOverview";
import DateRangePicker from "../../_components/date-range-picker/DateRangePicker";
import { useClickOutside } from "@mantine/hooks";
const TrafficOverview = lazy(
  () => import("../(overview)/traffic-overview/TrafficOverview")
);
const UsersOverview = lazy(
  () => import("../(overview)/users-overview/UsersOverview")
);
const AudienceOverview = lazy(
  () => import("../(overview)/audience-overview/AudienceOverview")
);

/* ========================================================================== */
type GoogleConnectedProps = {
  gaStatus?: any;
  gscStatus?: any;
  isGoogleAnalyticsConnected?: () => boolean;
  isGoogleSearchConsoleConnected?: () => boolean;
  connectGA?: () => void;
  connectGSC?: () => void;
  gaConnecting?: boolean;
  gscConnecting?: boolean;
};

const GoogleConnected = ({
  gaStatus,
  gscStatus,
  isGoogleAnalyticsConnected,
  isGoogleSearchConsoleConnected,
  connectGA,
  connectGSC,
  gaConnecting,
  gscConnecting,
}: GoogleConnectedProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const toggleButtonRef = useRef<HTMLDivElement>(null);
  const dateRangePickerRef = useClickOutside(() => {
    if (showDateRangePicker) {
      const isClickInsideToggle = toggleButtonRef.current?.contains(
        document.activeElement
      );

      if (!isClickInsideToggle) {
        setShowDateRangePicker(false);
      }
    }
  });
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (showDateRangePicker) {
        if (
          toggleButtonRef.current?.contains(event.target as Node) ||
          dateRangePickerRef.current?.contains(event.target as Node)
        ) {
          return;
        }
        setShowDateRangePicker(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDateRangePicker, dateRangePickerRef]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return (
    <div className="w-full h-fit space-y-6">
      <DateRangePicker
        ShowDateRangePicker={showDateRangePicker}
        ref={dateRangePickerRef}
      />
      <GoogleConnectedHeader
        onDateRangeClick={() => setShowDateRangePicker((prev) => !prev)}
        title="Traffics"
        ref={toggleButtonRef}
        gaStatus={gaStatus}
        gscStatus={gscStatus}
        isGoogleAnalyticsConnected={isGoogleAnalyticsConnected}
        isGoogleSearchConsoleConnected={isGoogleSearchConsoleConnected}
        connectGA={connectGA}
        connectGSC={connectGSC}
        gaConnecting={gaConnecting}
        gscConnecting={gscConnecting}
      />
      <AudienceOverview />
      <UsersOverview />
      <TrafficOverview />
      <GSCOverview />
    </div>
  );
};

export default GoogleConnected;
