"use client";
import { useState } from "react";
import Modal from "@/ui/Modal";

/**
 * Test component to verify scroll lock functionality
 * This component can be temporarily added to any page to test modal scroll locking
 */
export default function ScrollLockTest() {
  const [isModal1Open, setIsModal1Open] = useState(false);
  const [isModal2Open, setIsModal2Open] = useState(false);

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Scroll Lock Test</h2>
      <p className="text-sm text-gray-600">
        Test the background scroll locking functionality with multiple modals.
        Scroll down on the page, then open modals to verify background scrolling is disabled.
      </p>
      
      {/* Generate some content to make the page scrollable */}
      <div className="space-y-4">
        {Array.from({ length: 20 }, (_, i) => (
          <div key={i} className="p-4 bg-gray-100 rounded">
            <h3 className="font-semibold">Content Block {i + 1}</h3>
            <p>
              This is some test content to make the page scrollable. 
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>
        ))}
      </div>

      {/* Test buttons */}
      <div className="fixed bottom-4 right-4 space-y-2">
        <button
          onClick={() => setIsModal1Open(true)}
          className="block w-full btn btn--primary"
        >
          Open Modal 1
        </button>
        <button
          onClick={() => setIsModal2Open(true)}
          className="block w-full btn btn--secondary"
        >
          Open Modal 2
        </button>
      </div>

      {/* Test Modals */}
      <Modal
        open={isModal1Open}
        onClose={() => setIsModal1Open(false)}
        title="Test Modal 1"
        size="md"
      >
        <div className="p-4 space-y-4">
          <p>This is the first test modal. Background scrolling should be locked.</p>
          <button
            onClick={() => setIsModal2Open(true)}
            className="btn btn--primary"
          >
            Open Modal 2 (Nested)
          </button>
          <div className="space-y-2">
            {Array.from({ length: 10 }, (_, i) => (
              <div key={i} className="p-2 bg-gray-50 rounded">
                Modal content {i + 1} - This modal should be scrollable while background is locked.
              </div>
            ))}
          </div>
        </div>
      </Modal>

      <Modal
        open={isModal2Open}
        onClose={() => setIsModal2Open(false)}
        title="Test Modal 2"
        size="sm"
      >
        <div className="p-4 space-y-4">
          <p>This is the second test modal. It can be opened while Modal 1 is open.</p>
          <p>Background should remain locked until both modals are closed.</p>
          <button
            onClick={() => setIsModal1Open(false)}
            className="btn btn--secondary"
          >
            Close Modal 1
          </button>
        </div>
      </Modal>
    </div>
  );
}
