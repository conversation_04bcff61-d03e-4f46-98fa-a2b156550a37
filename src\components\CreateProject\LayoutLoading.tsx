import React from "react";

/**
 * Loading component for create-project layout
 * Shows while fetching existing project data in edit mode
 */
export default function LayoutLoading() {
  return (
    <div className="w-full max-w-8xl mx-auto px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 flex flex-col lg:flex-row min-h-screen gap-6 lg:gap-8">
      {/* Sidebar Skeleton */}
      <div className="hidden lg:block lg:w-80 xl:w-96 flex-shrink-0">
        <div className="sticky top-6">
          <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6 space-y-6">
            {/* Header skeleton */}
            <div className="space-y-3">
              <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-100 rounded animate-pulse w-3/4"></div>
            </div>
            
            {/* Steps skeleton */}
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((step) => (
                <div key={step} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-1"></div>
                    <div className="h-3 bg-gray-100 rounded animate-pulse w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="flex-1 min-w-0">
        <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6 lg:p-8 space-y-6">
          {/* Title skeleton */}
          <div className="space-y-3">
            <div className="h-8 bg-gray-200 rounded animate-pulse w-1/2"></div>
            <div className="h-4 bg-gray-100 rounded animate-pulse w-3/4"></div>
          </div>

          {/* Form skeleton */}
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
              <div className="h-12 bg-gray-100 rounded-lg animate-pulse"></div>
            </div>
            
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
              <div className="h-12 bg-gray-100 rounded-lg animate-pulse"></div>
            </div>
            
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
              <div className="h-32 bg-gray-100 rounded-lg animate-pulse"></div>
            </div>
          </div>

          {/* Buttons skeleton */}
          <div className="flex justify-end space-x-3 pt-6">
            <div className="h-12 w-24 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="h-12 w-32 bg-primary/20 rounded-lg animate-pulse"></div>
          </div>
        </div>

        {/* Loading indicator */}
        <div className="flex items-center justify-center mt-8">
          <div className="flex items-center space-x-2 text-gray-600">
            <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm">Loading project data...</span>
          </div>
        </div>
      </div>
    </div>
  );
}
