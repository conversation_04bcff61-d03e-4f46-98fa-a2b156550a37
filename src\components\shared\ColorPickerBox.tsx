"use client";

import React, { useState } from "react";
import * as Popover from "@radix-ui/react-popover";
import TooltipPortal from "../ui/TooltipPortal";
import { BsExclamationCircle } from "react-icons/bs";

const dataColor = [
  {
    name: "pink",
    color: "#C189E9",
  },
  {
    name: "blue",
    color: "#2756A9",
  },
  {
    name: "silver",
    color: "#587DBD",
  },
  {
    name: "blue-low",
    color: "#00BBEC",
  },
  {
    name: "red",
    color: "#FF0000",
  },
  {
    name: "orange",
    color: "#FF5E00",
  },
  {
    name: "orange-low",
    color: "#FF9500",
  },
  {
    name: "yellow",
    color: "#F8BD00",
  },
  {
    name: "green",
    color: "#319F43",
  },
  {
    name: "green-light",
    color: "#31D37A",
  },
  {
    name: "purple",
    color: "#8C00FF",
  },
  {
    name: "red-pink",
    color: "#FF00C3",
  },
];

type ColorPickerBoxType = {
  title?: string;
  guideText?: string;
  value?: string;
  onChange?: (color: string) => void;
};

export default function ColorPickerBox({
  title = "Choose Color",
  guideText,
  value,
  onChange,
}: ColorPickerBoxType) {
  const [open, setOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState(value || "#8C00FF");

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    onChange?.(color);
    setOpen(false);
  };

  return (
    <div className="w-full">
      {title && (
        <div className="mb-3 flex items-center gap-2 text-[#344054]">
          <span className="text-sm lg:text-base font-medium">{title}</span>
          {guideText && (
            <TooltipPortal width="xl" content={<span>{guideText}</span>}>
              <BsExclamationCircle className="text-gray-400 hover:text-gray-600 transition-colors" />
            </TooltipPortal>
          )}
        </div>
      )}

      <Popover.Root open={open} onOpenChange={setOpen}>
        <Popover.Trigger asChild>
          <button
            type="button"
            className="relative w-12 h-12 cursor-pointer focus:outline-none transition-all duration-200 group"
            aria-label="Select project color"
          >
            <div
              style={{ backgroundColor: selectedColor }}
              className="absolute inset-0 rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-200"
            />
            <div
              style={{ borderColor: selectedColor }}
              className="absolute -inset-1 border-2 rounded-lg opacity-60 group-hover:opacity-80 transition-opacity duration-200"
            />
          </button>
        </Popover.Trigger>

        <Popover.Portal>
          <Popover.Content
            className="bg-white border border-gray-200 rounded-xl shadow-lg z-50 p-4 w-64 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
            sideOffset={8}
            align="start"
          >
            <div className="mb-3">
              <h3 className="text-sm font-medium text-[#344054]">
                Choose Color
              </h3>
              <p className="text-xs text-gray-500 mt-1">
                Select a color to help identify your project
              </p>
            </div>

            <div className="grid grid-cols-4 gap-3">
              {dataColor.map((colorItem, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleColorSelect(colorItem.color)}
                  className="relative w-12 h-12 p-1 cursor-pointer focus:outline-none hover:scale-110 transition-all duration-200 group rounded-lg border-2 flex items-center justify-center"
                  style={{ borderColor: colorItem.color }}
                  aria-label={`Select ${colorItem.name} color`}
                  title={colorItem.name}
                >
                  <div
                    style={{ backgroundColor: colorItem.color }}
                    className="w-full h-full rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200"
                  />
                  {selectedColor === colorItem.color && (
                    <div className="absolute inset-1 rounded-md border-2 border-white shadow-lg" />
                  )}
                </button>
              ))}
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  );
}
