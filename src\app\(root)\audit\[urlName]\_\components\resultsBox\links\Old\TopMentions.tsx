import Table from "@/ui/Table";

// Define the structure based on the actual API response
type MentionItem = {
  link?: string;
  title?: string;
  description?: string;
};

type TopMentionsProps = {
  mentions?: MentionItem[];
  brandName?: string;
};

export default function TopMentions({
  mentions = [],
  brandName = "",
}: TopMentionsProps) {
  // Use the provided mentions or fall back to default data if empty
  const hasMentions = mentions && mentions.length > 0;
  const tableData = hasMentions ? mentions : [];

  return (
    <div className="mt-4 lg:mt-6">
      <div className="mb-6">
        <h4 className="font-semibold text-secondary pb-2">Brand Mentions</h4>
        <span className="text-sm text-secondary/60">
          {hasMentions
            ? `These are mentions of "${
                brandName || "your brand"
              }" across the web.`
            : "No brand mentions found for your website."}
        </span>
      </div>

      {hasMentions ? (
        <Table>
          <Table.Header>
            <th className="pb-4 whitespace-nowrap">URL</th>
            <th className="text-center pb-4">Title</th>
            <th className="text-center pb-4">Description</th>
          </Table.Header>
          <Table.Body>
            {tableData.map((item, index) => (
              <Table.Row key={index}>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 pr-2 sm:pr-4 break-words">
                  <a
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline text-xs sm:text-sm"
                  >
                    {item.link || "N/A"}
                  </a>
                </td>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 break-words text-xs sm:text-sm">
                  {item.title || "N/A"}
                </td>
                <td className="w-full sm:min-w-[200px] sm:max-w-[300px] py-4 break-words text-xs sm:text-sm">
                  {item.description || "N/A"}
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No brand mention data available
        </div>
      )}
    </div>
  );
}
