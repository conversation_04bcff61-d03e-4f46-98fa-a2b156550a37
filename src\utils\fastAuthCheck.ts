/**
 * Fast Authentication Check Utilities
 * 
 * These utilities provide immediate, synchronous token checking for fast redirection
 * without waiting for async operations or hydration.
 */

import storageService from "@/services/storageService";
import { isProtectedRoute } from "@/utils/authUtils";

/**
 * Synchronously check if user has valid tokens in storage
 * This is fast and doesn't require any async operations
 */
export function hasValidTokens(): boolean {
  if (typeof window === "undefined") return false;
  
  try {
    const token = storageService.getToken();
    const refreshToken = storageService.getRefreshToken();
    const user = storageService.getUser();
    
    // Consider tokens valid if we have at least an access token and user data
    return !!(token && user);
  } catch (error) {
    console.error("Error checking tokens:", error);
    return false;
  }
}

/**
 * Synchronously check if user has any authentication data
 * This is even faster as it only checks for access token
 */
export function hasAccessToken(): boolean {
  if (typeof window === "undefined") return false;
  
  try {
    const token = storageService.getToken();
    return !!token;
  } catch (error) {
    console.error("Error checking access token:", error);
    return false;
  }
}

/**
 * Fast redirect function for protected routes
 * This should be called immediately on component mount for protected routes
 */
export function fastRedirectIfNoTokens(router: any, pathname: string): boolean {
  // Only redirect if this is a protected route
  if (!isProtectedRoute(pathname)) {
    return false;
  }
  
  // Check if user has tokens
  if (!hasAccessToken()) {
    console.log("No tokens found, redirecting from protected route:", pathname);
    router.replace("/");
    return true; // Indicates redirect was performed
  }
  
  return false; // No redirect needed
}

/**
 * Enhanced fast redirect that also checks for valid user data
 */
export function fastRedirectIfNoAuth(router: any, pathname: string): boolean {
  // Only redirect if this is a protected route
  if (!isProtectedRoute(pathname)) {
    return false;
  }
  
  // Check if user has valid tokens and user data
  if (!hasValidTokens()) {
    console.log("No valid authentication data found, redirecting from protected route:", pathname);
    router.replace("/");
    return true; // Indicates redirect was performed
  }
  
  return false; // No redirect needed
}

/**
 * Clear all authentication data immediately
 * This is used during logout to ensure immediate state clearing
 */
export function clearAuthDataImmediately(): void {
  if (typeof window === "undefined") return;
  
  try {
    // Clear storage service data
    storageService.clearAuth();
    
    // Clear session storage
    sessionStorage.removeItem("postLoginRedirect");
    sessionStorage.removeItem("postLoginCallback");
    sessionStorage.removeItem("auth-storage");
    
    // Clear localStorage auth data
    localStorage.removeItem("auth-storage");
    
    console.log("Authentication data cleared immediately");
  } catch (error) {
    console.error("Error clearing auth data:", error);
  }
}

/**
 * Check if current route should be immediately protected
 * This is used to determine if we should show loading or redirect immediately
 */
export function shouldImmediatelyProtect(pathname: string): boolean {
  const immediatelyProtectedRoutes = [
    "/dashboard",
    "/my-projects", 
    "/profile",
    "/settings",
    "/billing"
  ];
  
  return immediatelyProtectedRoutes.some(route => pathname.startsWith(route));
}

/**
 * Get authentication status without async operations
 * Returns an object with current auth state based on storage only
 */
export function getImmediateAuthStatus() {
  if (typeof window === "undefined") {
    return {
      hasTokens: false,
      hasValidTokens: false,
      isProtectedRoute: false,
      shouldRedirect: false
    };
  }
  
  const pathname = window.location.pathname;
  const hasTokens = hasAccessToken();
  const hasValid = hasValidTokens();
  const isProtected = isProtectedRoute(pathname);
  const shouldRedirect = isProtected && !hasTokens;
  
  return {
    hasTokens,
    hasValidTokens: hasValid,
    isProtectedRoute: isProtected,
    shouldRedirect,
    pathname
  };
}
