import React, { useState, useEffect, ReactNode } from "react";
import { CheckIcon } from "@heroicons/react/24/solid";
import { motion, Variants } from "framer-motion";

type CustomCheckboxProps = {
  id: string;
  label: ReactNode;
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
  required?: boolean;
};

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  id,
  label,
  checked,
  onChange,
  className = "",
  required = false,
}) => {
  const [isChecked, setIsChecked] = useState(checked);

  useEffect(() => {
    setIsChecked(checked);
  }, [checked]);

  const handleChange = () => {
    setIsChecked(!isChecked);
    onChange(!isChecked);
  };

  // Animation variants
  const checkboxVariants: Variants = {
    checked: {
      backgroundColor: "rgba(145, 74, 196, 1)",
      borderColor: "rgba(145, 74, 196, 1)",
      transition: { duration: 0.2 },
    },
    unchecked: {
      backgroundColor: "#FFFFFF",
      borderColor: "#D1D5DB",
      transition: { duration: 0.2 },
    },
    hover: {
      scale: 1.05,
      borderColor: "rgba(145, 74, 196, 0.7)",
      boxShadow: "0 0 0 3px rgba(145, 74, 196, 0.15)",
    },
    tap: {
      scale: 0.9,
    },
  };

  const checkIconVariants: Variants = {
    checked: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 15,
      },
    },
    unchecked: {
      opacity: 0,
      scale: 0,
      transition: {
        duration: 0.15,
      },
    },
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div className="relative flex items-center">
        <input
          type="checkbox"
          id={id}
          checked={isChecked}
          onChange={handleChange}
          className="sr-only"
          required={required}
        />
        <motion.div
          variants={checkboxVariants}
          initial={isChecked ? "checked" : "unchecked"}
          animate={isChecked ? "checked" : "unchecked"}
          whileHover="hover"
          whileTap="tap"
          className="w-5 h-5 rounded border flex items-center justify-center cursor-pointer"
          onClick={handleChange}
          aria-hidden="true"
        >
          <motion.div
            variants={checkIconVariants}
            initial={isChecked ? "checked" : "unchecked"}
            animate={isChecked ? "checked" : "unchecked"}
          >
            <CheckIcon className="w-3.5 h-3.5 text-white" />
          </motion.div>
        </motion.div>
      </div>
      <label
        htmlFor={id}
        className="ml-2 text-sm text-gray-700 cursor-pointer select-none"
      >
        {label}
      </label>
    </div>
  );
};

export default CustomCheckbox;
