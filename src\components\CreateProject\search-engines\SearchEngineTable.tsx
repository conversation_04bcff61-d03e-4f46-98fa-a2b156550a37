"use client";
import React from "react";
import Image from "next/image";
import { AnimatePresence, motion } from "framer-motion";
import { MdClose } from "react-icons/md";
import { SearchEngineConfig } from "@/store/createProjectStore";
import PaginationTable from "@/components/CreateProject/PaginationTable";
import { AnimatedElement } from "@/components/CreateProject/PageTransition";
import SearchEngineEmptyState from "./SearchEngineEmptyState";

interface SearchEngineTableProps {
  searchEngineConfigs: SearchEngineConfig[];
  currentPage: number;
  setCurrentPage: (page: number) => void;
  itemsPerPage: number;
  getCurrentPageItems: () => SearchEngineConfig[];
  onDelete: (id: string) => void;
}

export default function SearchEngineTable({
  searchEngineConfigs,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  getCurrentPageItems,
  onDelete,
}: SearchEngineTableProps) {
  return (
    <AnimatedElement variant="child" delay={0.7}>
      <div className="space-y-6 pt-6 border-t border-gray-100">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <span className="text-primary font-semibold text-sm">2</span>
          </div>
          <h3 className="text-base font-semibold text-[#344054]">
            Added Configurations
          </h3>
          {searchEngineConfigs.length > 0 && (
            <span className="bg-primary/10 text-primary text-xs font-medium px-2 py-1 rounded-full">
              {searchEngineConfigs.length} configuration
              {searchEngineConfigs.length !== 1 ? "s" : ""}
            </span>
          )}
        </div>

        {searchEngineConfigs.length === 0 ? (
          <SearchEngineEmptyState />
        ) : (
          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-[#344054] uppercase tracking-wider">
                      #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-[#344054] uppercase tracking-wider">
                      Configuration
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-[#344054] uppercase tracking-wider">
                      Search Engine
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-[#344054] uppercase tracking-wider">
                      Country
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-[#344054] uppercase tracking-wider">
                      Language
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-[#344054] uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <AnimatePresence mode="popLayout">
                    {getCurrentPageItems().map((config, index) => (
                      <motion.tr
                        key={config.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{
                          duration: 0.2,
                          ease: "easeOut",
                        }}
                        className="hover:bg-gray-50 transition-colors duration-200"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                            <span className="text-sm font-semibold text-gray-600">
                              {(currentPage - 1) * itemsPerPage + index + 1}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            {config.searchEngine?.name || "Unknown"}-
                            {config.country?.code || "XX"}
                            {config.location?.name
                              ? `-${config.location.name}`
                              : ""}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          {config.searchEngine && (
                            <div className="flex justify-center">
                              <div className="w-8 h-8 flex items-center justify-center">
                                <Image
                                  src={config.searchEngine.image}
                                  alt={config.searchEngine.name}
                                  width={24}
                                  height={24}
                                  className="rounded"
                                />
                              </div>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          {config.country && (
                            <div className="flex justify-center">
                              <div className="w-8 h-8 flex items-center justify-center">
                                <Image
                                  src={config.country.image}
                                  alt={config.country.name}
                                  width={24}
                                  height={24}
                                  className="rounded"
                                />
                              </div>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <span className="text-sm text-gray-900">
                            {config.language?.name || "Unknown"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <button
                            type="button"
                            title="Remove configuration"
                            onClick={() => onDelete(config.id)}
                            className="w-8 h-8 flex items-center justify-center rounded-lg bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-colors duration-200 group mx-auto"
                          >
                            <MdClose className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                          </button>
                        </td>
                      </motion.tr>
                    ))}
                  </AnimatePresence>
                </tbody>
              </table>
            </div>

            {searchEngineConfigs.length > itemsPerPage && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <PaginationTable
                  currentPage={currentPage}
                  limitPage={itemsPerPage}
                  setCurrentPage={setCurrentPage}
                  total={searchEngineConfigs.length}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </AnimatedElement>
  );
}
