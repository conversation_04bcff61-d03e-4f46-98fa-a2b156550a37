import { SVGProps } from "react";

export function InstagramColorIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_4071)">
        <path
          d="M18.375 0.144531H5.625C2.5184 0.144531 0 2.66293 0 5.76953V18.5195C0 21.6261 2.5184 24.1445 5.625 24.1445H18.375C21.4816 24.1445 24 21.6261 24 18.5195V5.76953C24 2.66293 21.4816 0.144531 18.375 0.144531Z"
          fill="url(#paint0_radial_204_4071)"
        />
        <path
          d="M18.375 0.144531H5.625C2.5184 0.144531 0 2.66293 0 5.76953V18.5195C0 21.6261 2.5184 24.1445 5.625 24.1445H18.375C21.4816 24.1445 24 21.6261 24 18.5195V5.76953C24 2.66293 21.4816 0.144531 18.375 0.144531Z"
          fill="url(#paint1_radial_204_4071)"
        />
        <path
          d="M12.0008 2.76953C9.45478 2.76953 9.13519 2.78069 8.13525 2.82616C7.13719 2.87191 6.45591 3.02987 5.85984 3.26172C5.24316 3.50116 4.72013 3.8215 4.19906 4.34275C3.67753 4.86391 3.35719 5.38694 3.117 6.00334C2.8845 6.59959 2.72634 7.28116 2.68144 8.27875C2.63672 9.27878 2.625 9.59847 2.625 12.1446C2.625 14.6908 2.63625 15.0093 2.68163 16.0093C2.72756 17.0073 2.88553 17.6886 3.11719 18.2847C3.35681 18.9014 3.67716 19.4244 4.19841 19.9455C4.71938 20.467 5.24241 20.7881 5.85862 21.0275C6.45516 21.2594 7.13653 21.4173 8.13441 21.4631C9.13444 21.5086 9.45375 21.5197 11.9997 21.5197C14.5461 21.5197 14.8646 21.5086 15.8646 21.4631C16.8626 21.4173 17.5447 21.2594 18.1412 21.0275C18.7576 20.7881 19.2799 20.467 19.8007 19.9455C20.3223 19.4244 20.6425 18.9014 20.8828 18.285C21.1133 17.6886 21.2715 17.0072 21.3184 16.0095C21.3633 15.0095 21.375 14.6908 21.375 12.1446C21.375 9.59847 21.3633 9.27897 21.3184 8.27894C21.2715 7.28087 21.1133 6.59969 20.8828 6.00363C20.6425 5.38694 20.3223 4.86391 19.8007 4.34275C19.2793 3.82131 18.7578 3.50097 18.1406 3.26181C17.543 3.02987 16.8613 2.87181 15.8632 2.82616C14.8632 2.78069 14.5448 2.76953 11.9979 2.76953H12.0008ZM11.1598 4.459C11.4095 4.45862 11.688 4.459 12.0008 4.459C14.5041 4.459 14.8007 4.468 15.7892 4.51291C16.7032 4.55472 17.1994 4.70744 17.5298 4.83578C17.9674 5.00566 18.2793 5.20881 18.6072 5.53703C18.9353 5.86516 19.1384 6.17762 19.3088 6.61516C19.4371 6.94516 19.59 7.44128 19.6316 8.35534C19.6765 9.34366 19.6863 9.64047 19.6863 12.1425C19.6863 14.6445 19.6765 14.9414 19.6316 15.9296C19.5898 16.8437 19.4371 17.3398 19.3088 17.6699C19.1389 18.1074 18.9353 18.4189 18.6072 18.7469C18.2791 19.075 17.9676 19.2781 17.5298 19.448C17.1997 19.5769 16.7032 19.7293 15.7892 19.7711C14.8009 19.816 14.5041 19.8258 12.0008 19.8258C9.49753 19.8258 9.20081 19.816 8.21259 19.7711C7.29853 19.7289 6.80241 19.5762 6.47166 19.4478C6.03422 19.2779 5.72166 19.0748 5.39353 18.7467C5.06541 18.4186 4.86234 18.1068 4.692 17.6691C4.56366 17.339 4.41075 16.8429 4.36913 15.9288C4.32422 14.9405 4.31522 14.6437 4.31522 12.1401C4.31522 9.63653 4.32422 9.34131 4.36913 8.353C4.41094 7.43894 4.56366 6.94281 4.692 6.61234C4.86197 6.17481 5.06541 5.86234 5.39363 5.53422C5.72184 5.20609 6.03422 5.00294 6.47175 4.83269C6.80222 4.70378 7.29853 4.55144 8.21259 4.50944C9.07744 4.47034 9.41259 4.45862 11.1598 4.45666V4.459ZM17.0052 6.01562C16.3841 6.01562 15.8802 6.51906 15.8802 7.14025C15.8802 7.76134 16.3841 8.26525 17.0052 8.26525C17.6263 8.26525 18.1302 7.76134 18.1302 7.14025C18.1302 6.51916 17.6263 6.01525 17.0052 6.01525V6.01562ZM12.0008 7.33009C9.34209 7.33009 7.18641 9.48578 7.18641 12.1446C7.18641 14.8035 9.34209 16.9581 12.0008 16.9581C14.6597 16.9581 16.8146 14.8035 16.8146 12.1446C16.8146 9.48588 14.6595 7.33009 12.0007 7.33009H12.0008ZM12.0008 9.01956C13.7267 9.01956 15.1259 10.4186 15.1259 12.1446C15.1259 13.8705 13.7267 15.2697 12.0008 15.2697C10.275 15.2697 8.87588 13.8705 8.87588 12.1446C8.87588 10.4186 10.2749 9.01956 12.0008 9.01956Z"
          fill="white"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_204_4071"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(6.375 25.993) rotate(-90) scale(23.7858 22.1227)"
        >
          <stop stopColor="#FFDD55" />
          <stop offset="0.1" stopColor="#FFDD55" />
          <stop offset="0.5" stopColor="#FF543E" />
          <stop offset="1" stopColor="#C837AB" />
        </radialGradient>
        <radialGradient
          id="paint1_radial_204_4071"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(-4.02009 1.87337) rotate(78.681) scale(10.6324 43.827)"
        >
          <stop stopColor="#3771C8" />
          <stop offset="0.128" stopColor="#3771C8" />
          <stop offset="1" stopColor="#6600FF" stopOpacity="0" />
        </radialGradient>
        <clipPath id="clip0_204_4071">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.144531)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
