"use client";
import React, { useState } from "react";
import Image from "next/image";

// Placeholder icon for failed image loads
export const PlaceholderIcon: React.FC = () => (
  <svg
    className="w-full h-full text-gray-400"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm7.4-2a5.6 5.6 0 0 0 0-2l2-1.5a.5.5 0 0 0 .1-.7l-2-3.5a.5.5 0 0 0-.6-.2l-2.3 1a5.7 5.7 0 0 0-1.7-1l-.3-2.5a.5.5 0 0 0-.5-.4h-4a.5.5 0 0 0-.5.4l-.3 2.5a5.7 5.7 0 0 0-1.7 1l-2.3-1a.5.5 0 0 0-.6.2l-2 3.5a.5.5 0 0 0 .1.7l2 1.5a5.6 5.6 0 0 0 0 2l-2 1.5a.5.5 0 0 0-.1.7l2 3.5a.5.5 0 0 0 .6.2l2.3-1a5.7 5.7 0 0 0 1.7 1l.3 2.5a.5.5 0 0 0 .5.4h4a.5.5 0 0 0 .5-.4l.3-2.5a5.7 5.7 0 0 0 1.7-1l2.3 1a.5.5 0 0 0 .6-.2l2-3.5a.5.5 0 0 0-.1-.7l-2-1.5z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Watermark component for the entire document
export const Watermark: React.FC<{
  brandName?: string;
  brandWebsite?: string;
}> = ({ brandName, brandWebsite }) => {
  const displayBrand = brandName ?? brandWebsite ?? "seoanalyser.com.au";

  return (
    <div className="fixed bottom-0 left-0 w-full h-full pointer-events-none flex items-center justify-center z-0 overflow-hidden watermark-container">
      <div className="text-7xl font-bold text-gray-400/10 rotate-[-30deg] select-none whitespace-nowrap scale-80 opacity-30 print:text-gray-400/30">
        {displayBrand}
      </div>
      {/* Additional watermark for both normal and print view */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-8xl font-bold text-gray-400/15 rotate-[-30deg] select-none whitespace-nowrap print:text-gray-400/25">
        {displayBrand}
      </div>
    </div>
  );
};

// Logo watermark component for sections
export const LogoWatermark: React.FC<{
  brandPhoto?: string | null;
  onLoad?: () => void;
  onError?: () => void;
  size?: "small" | "medium" | "large";
  className?: string;
}> = ({ brandPhoto, onLoad, onError, size = "medium", className = "" }) => {
  // Default logo path if no brand logo is provided
  const defaultLogoPath = "/images/appLogo.svg";
  const [imgError, setImgError] = useState(false);

  // Determine size based on prop
  const sizeClasses = {
    small: "w-32 h-32",
    medium: "w-40 h-40",
    large: "w-48 h-48",
  };

  const sizeClass = sizeClasses[size];

  // Only render if brandPhoto is provided and hasn't failed to load
  if (!brandPhoto || imgError) {
    return null;
  }

  return (
    <div
      className={`absolute inset-0 w-full h-full flex justify-center items-center pointer-events-none z-20 overflow-visible logo-watermark ${className}`}
    >
      <div className={`relative ${sizeClass}`}>
        <img
          src={brandPhoto}
          alt="Brand Logo Watermark"
          className="object-contain print-watermark-image w-full h-full"
          referrerPolicy="no-referrer"
          style={{
            opacity: 0.3,
            filter: "contrast(1.2) brightness(0.9)",
            mixBlendMode: "multiply",
            maxWidth:
              size === "large"
                ? "192px"
                : size === "medium"
                ? "160px"
                : "128px",
            maxHeight:
              size === "large"
                ? "192px"
                : size === "medium"
                ? "160px"
                : "128px",
          }}
          onError={() => {
            setImgError(true);
            if (onError) onError();
          }}
          onLoad={() => {
            if (onLoad) onLoad();
          }}
        />
      </div>
    </div>
  );
};

// Section-specific watermark component
export const SectionWatermark: React.FC<{
  brandName?: string;
  brandWebsite?: string;
  brandPhoto?: string | null;
  onLogoLoad?: () => void;
  onLogoError?: () => void;
  showLogo?: boolean;
  logoSize?: "small" | "medium" | "large";
  sectionId?: string; // Add section ID to track which sections have logos
}> = ({
  brandName,
  brandWebsite,
  brandPhoto,
  onLogoLoad,
  onLogoError,
  showLogo = true,
  logoSize = "medium",
  sectionId = "default",
}) => {
  const displayBrand = brandName ?? brandWebsite ?? "seoanalyser.com.au";

  return (
    <div className="absolute inset-0 pointer-events-none flex flex-col items-center justify-center z-0 overflow-visible section-watermark">
      {/* Text watermark in the center */}
      <div className="text-8xl font-bold text-gray-400/15 rotate-[-30deg] select-none whitespace-nowrap opacity-30 print:text-gray-400/30 print:opacity-40">
        {displayBrand}
      </div>

      {/* Logo watermark at the top - only include it if showLogo is true */}
      {showLogo && (
        <LogoWatermark
          brandPhoto={brandPhoto}
          onLoad={onLogoLoad}
          onError={onLogoError}
          size={logoSize}
          className={`section-logo-watermark section-logo-${sectionId}`}
        />
      )}
    </div>
  );
};
