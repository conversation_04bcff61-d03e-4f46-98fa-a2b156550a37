import { NextResponse } from "next/server";
import type { DateRangeResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange.type";
const DATE_RANGE: DateRangeResponse = {
  date: {
    start: "May 03, 2025",
    end: "Aug 15, 2025",
  },
  compared_to: {
    start: "May 03, 2023",
    end: "Aug 15, 2023",
  },
};

export async function GET() {
  return NextResponse.json(DATE_RANGE);
}
