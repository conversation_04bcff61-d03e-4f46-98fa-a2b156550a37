import AXIOS from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import type { UsersOverviewData } from "./UsersOverview.type";
import { useProjectId } from "@/hooks/useProjectId";

const useUsersOverview = (userOverviewQuery: string) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["user-overview-data", projectId, userOverviewQuery],
    queryFn: async (): Promise<UsersOverviewData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/overview/users-overview",
        {
          params: {
            projectId,
            userOverview: userOverviewQuery,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default useUsersOverview;
