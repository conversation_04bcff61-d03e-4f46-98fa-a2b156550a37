"use client";
import React, { useEffect, useState, useCallback } from "react";
import NavbarCreateProject from "./NavbarCreateProject";
import StepMobileCreateProject from "./StepMobileCreateProject";
import BoxCreateProject from "./BoxCreateProject";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useRouter } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { defaultCountries, defaultSearchEngines } from "@/utils/flagUtils";
import { useQuery } from "@tanstack/react-query";
import { projectAPI } from "@/services/projectService";

// Import modular components
import WebsiteConfigurationSection from "./ShortcutComponents/WebsiteConfigurationSection";
import SearchEngineLocationSection from "./ShortcutComponents/SearchEngineLocationSection";
import KeywordsSection from "./ShortcutComponents/KeywordsSection";
import ActionButtonsSection from "./ShortcutComponents/ActionButtonsSection";
import { useFileImport } from "./ShortcutComponents/useFileImport";
import { useProjectSubmission } from "./ShortcutComponents/useProjectSubmission";

type ShortcutFormData = {
  domain: string;
  name: string;
};

type TableType = {
  country: {
    name: string;
    code: string;
    image: string;
  } | null;
  searchEngines:
    | {
        name: string;
        image: string;
      }[]
    | null;
  language: {
    code: string;
    name: string;
  } | null;
};

const schema = yup.object({
  domain: yup
    .string()
    .required("")
    .matches(
      /^(?:(?:https?:\/\/)?(?:www\.)?)?((([a-zA-Z0-9\u0600-\u06FF\-]+\.)+[a-zA-Z]{2,}|localhost|\d{1,3}(\.\d{1,3}){3})(:\d+)?)(\/.*)?$/,
      "Enter a valid domain like example.com"
    ),
  name: yup
    .string()
    .required("Name is required")
    .max(50, "Project name must be at most 50 characters"),
});

// Interface for keyword table rows
interface KeywordTableRow {
  id: string;
  keyword: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
  volume: string;
  configId: string;
}

export default function ShortcutPage() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
    setValue,
    watch,
  } = useForm<ShortcutFormData>({ resolver: yupResolver(schema) });

  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [isNameTouched, setIsNameTouched] = useState(false);
  const [countriesData, setCountriesData] = useState<any[]>([]);
  const [googleLanguage, setGoogleLanguage] = useState<any[]>([]);
  const [keywords, setKeywords] = useState<KeywordTableRow[]>([]);
  const [error, setError] = useState("");

  const navigate = useRouter();
  const {
    setProjectInfo,
    setCurrentStep,
    setSupportedLocations,
    supportedLocations,
  } = useCreateProjectStore();

  // Load supported locations on page mount
  useQuery({
    queryKey: ["supported-locations"],
    queryFn: async () => {
      try {
        const response = await projectAPI.getSupportedLocations();
        setSupportedLocations(response.data.locations);
        return response.data;
      } catch (error) {
        console.error("Failed to load supported locations:", error);
        return { locations: [] };
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  // Function to extract project name from domain (same as project-information page)
  const splitDomain = (domain: string) => {
    // Remove protocol and www prefix before processing
    const cleanDomain = domain
      .replace(/^https?:\/\//, "")
      .replace(/^www\./, "")
      .split("/")[0]; // Remove path if any

    const parts = cleanDomain.split(".");
    if (parts.length < 2) return cleanDomain;
    const name = parts.slice(0, -1).join(".");
    return name;
  };

  // Watch domain field for auto-completion
  const domainValue = watch("domain");

  // Auto-complete project name based on domain (same logic as project-information page)
  useEffect(() => {
    if (!isNameTouched) {
      if (domainValue) {
        setValue("name", splitDomain(domainValue) || "", {
          shouldValidate: true,
          shouldDirty: true,
        });
      } else {
        setValue("name", "");
      }
    }
  }, [domainValue, isNameTouched, setValue]);

  const [dataState, setDataState] = useState<TableType>({
    country: defaultCountries.australia,
    searchEngines: [defaultSearchEngines.google],
    language: {
      name: "English",
      code: "en",
    },
  });

  // Initialize custom hooks
  const { handleFileChange } = useFileImport({
    keywords,
    setKeywords,
    dataState: {
      searchEngines: dataState.searchEngines || [],
      country: dataState.country,
      language: dataState.language,
    },
    setError,
  });

  const { mutate, isPending } = useProjectSubmission({
    keywords,
    dataState: {
      searchEngines: dataState.searchEngines || [],
      country: dataState.country,
      language: dataState.language,
    },
    getValues,
  });

  // Callback handlers
  const handleNameFocus = useCallback(() => {
    setIsNameTouched(true);
  }, []);

  const handleAdvancedRoute = useCallback(() => {
    const name = getValues("name");
    const domain = getValues("domain");

    if (name && domain) {
      setProjectInfo({ name, domain });
    }
    setCurrentStep("project-information");
    navigate.push("/create-project/project-information");
  }, [getValues, navigate, setProjectInfo, setCurrentStep]);

  const handleFormSubmit = useCallback(() => {
    setHasAttemptedSubmit(true);
    handleSubmit(() => mutate())();
  }, [handleSubmit, mutate]);

  // Load countries and language data
  useEffect(() => {
    const loadData = async () => {
      try {
        const [countriesModule, googleLanguageModule] = await Promise.all([
          import("@/data/countries"),
          import("@/data/googleLanguage"),
        ]);
        setCountriesData(countriesModule.default || countriesModule);
        setGoogleLanguage(googleLanguageModule.default || googleLanguageModule);
      } catch (error) {
        console.error("Failed to load data:", error);
      }
    };

    loadData();
  }, []);

  return (
    <div className="flex justify-between flex-col gap-5 h-full min-h-[calc(100vh-2rem)]">
      <div className="flex flex-col gap-3">
        <NavbarCreateProject>Create project</NavbarCreateProject>
        <StepMobileCreateProject />

        <BoxCreateProject classPlus="relative">
          <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
            <WebsiteConfigurationSection
              register={register}
              errors={errors}
              hasAttemptedSubmit={hasAttemptedSubmit}
              onNameFocus={handleNameFocus}
            />

            <SearchEngineLocationSection
              dataState={dataState}
              setDataState={setDataState}
              countriesData={countriesData}
              supportedLocations={supportedLocations}
              googleLanguage={googleLanguage}
            />

            <KeywordsSection
              keywords={keywords}
              setKeywords={setKeywords}
              dataState={{
                searchEngines: dataState.searchEngines || [],
                country: dataState.country,
                language: dataState.language,
              }}
              error={error}
              setError={setError}
              onFileImport={handleFileChange}
              domainUrl={domainValue}
              projectName={watch("name")}
            />

            <ActionButtonsSection
              onAdvancedRoute={handleAdvancedRoute}
              onFormSubmit={handleFormSubmit}
              isPending={isPending}
            />
          </form>
        </BoxCreateProject>
      </div>
    </div>
  );
}
