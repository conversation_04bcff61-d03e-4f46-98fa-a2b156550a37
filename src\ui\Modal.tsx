"use client";
import React, { ReactNode, useEffect, useState, memo, useRef } from "react";
import { createPortal } from "react-dom";
import { CrossSmallIcon } from "./icons/general/CrossSmallIcon";
import useOutsideClick from "@/hooks/useOutsideClick";
import useKeyboardVisibility from "@/hooks/useKeyboardVisibility";
import useScrollLock from "@/hooks/useScrollLock";
import { motion, AnimatePresence } from "framer-motion";

type Props = {
  title: string | ReactNode;
  children: ReactNode;
  open: boolean;
  onClose: () => void;
  size?: "sm" | "md" | "lg" | "xl";
  shouldPreventClose?: () => boolean;
};

// Optimized animation settings for better performance
const overlayAnimation = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: {
    duration: 0.2,
    ease: [0.25, 0.1, 0.25, 1.0], // Matching easing function with modal animation
  },
};

const modalAnimation = {
  initial: { opacity: 0, y: 15 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 15 },
  transition: {
    duration: 0.2,
    ease: [0.25, 0.1, 0.25, 1.0], // Improved easing function for smoother animation
  },
};

// Use React.memo to prevent unnecessary re-renders
function ModalComponent({
  title,
  children,
  onClose,
  open,
  size = "md",
  shouldPreventClose,
}: Props) {
  const [isMounted, setIsMounted] = useState(false);
  const ref = useOutsideClick(onClose, true, shouldPreventClose);
  const modalContentRef = useRef<HTMLDivElement>(null);
  const { isKeyboardVisible, viewportHeight } = useKeyboardVisibility();

  // Lock background scroll when modal is open
  useScrollLock(open);

  // Effect to handle scrolling to focused input when keyboard is visible
  useEffect(() => {
    if (isKeyboardVisible && modalContentRef.current) {
      // Find the active/focused input element
      const focusedElement = document.activeElement;
      if (
        focusedElement &&
        (focusedElement.tagName === "INPUT" ||
          focusedElement.tagName === "TEXTAREA") &&
        modalContentRef.current.contains(focusedElement)
      ) {
        // Calculate the position of the focused element relative to the modal
        const modalRect = modalContentRef.current.getBoundingClientRect();
        const focusedRect = focusedElement.getBoundingClientRect();

        // If the focused element is below the visible area, scroll to it
        if (focusedRect.bottom > modalRect.bottom) {
          focusedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    }
  }, [isKeyboardVisible]);

  // Add iOS-specific fixes for keyboard handling
  useEffect(() => {
    // Function to handle iOS viewport issues when keyboard appears
    const handleIOSKeyboard = () => {
      // Add a small delay to ensure the viewport has adjusted
      setTimeout(() => {
        // Force redraw by scrolling slightly
        // window.scrollTo(0, 0);

        // If there's a focused element, ensure it's visible
        const focusedElement = document.activeElement;
        if (
          focusedElement &&
          (focusedElement.tagName === "INPUT" ||
            focusedElement.tagName === "TEXTAREA") &&
          modalContentRef.current?.contains(focusedElement)
        ) {
          focusedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);
    };

    // Add event listeners for iOS devices
    document.addEventListener("focusin", handleIOSKeyboard);

    return () => {
      document.removeEventListener("focusin", handleIOSKeyboard);
    };
  }, []);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  // Calculate modal container styles based on keyboard visibility
  const getModalContainerStyle = () => {
    if (isKeyboardVisible) {
      // When keyboard is visible, position the modal at the top with appropriate height
      return {
        height: `${viewportHeight}px`,
        position: "fixed",
        top: 0,
        alignItems: "flex-start",
        paddingTop: "10px",
        paddingBottom: "20px", // Add padding at the bottom for better visibility
        overflowY: "auto", // Enable scrolling when keyboard is visible
        display: "flex",
        flexDirection: "column",
      } as React.CSSProperties;
    }
    return {};
  };

  return createPortal(
    <AnimatePresence mode="wait">
      {open && (
        <div className="w-full h-screen fixed top-0 right-0 z-50">
          {/* Separate backdrop from content for better performance */}
          <motion.div
            {...overlayAnimation}
            className="absolute inset-0 bg-white/5 backdrop-blur-sm"
          />

          <div
            className={`w-full h-full flex ${
              isKeyboardVisible ? "items-start" : "items-center"
            } justify-center py-4 sm:py-6 lg:py-8 px-2 sm:px-4 lg:px-6`}
            style={getModalContainerStyle()}
          >
            <motion.div
              ref={ref}
              {...modalAnimation}
              className={`w-full flex flex-col ${
                isKeyboardVisible
                  ? "h-[80vh]"
                  : size === "sm"
                  ? "h-auto max-h-[60vh]"
                  : size === "md"
                  ? "h-auto max-h-[70vh]"
                  : size === "lg"
                  ? "h-auto max-h-[80vh]"
                  : size === "xl"
                  ? "h-auto max-h-[85vh]"
                  : "h-auto max-h-[80vh]"
              } ${
                size === "sm"
                  ? "max-w-[400px] sm:max-w-[450px]"
                  : size === "md"
                  ? "max-w-[500px] sm:max-w-[550px] lg:max-w-[650px]"
                  : size === "lg"
                  ? "max-w-[600px] sm:max-w-[700px] lg:max-w-[800px]"
                  : size === "xl"
                  ? "max-w-[650px] sm:max-w-[750px] lg:max-w-[900px]"
                  : "max-w-[700px] sm:max-w-[800px] lg:max-w-[950px]"
              } mt-4 sm:mt-6 lg:mt-8 rounded-xl sm:rounded-2xl bg-white z-[60] border border-gray-300 shadow-[0_0_20px_0_rgba(29,34,65,0.1)] overflow-hidden will-change-transform`}
            >
              <div className="w-full flex justify-between items-center p-3 px-4 sm:p-4 lg:px-5 sticky top-0 bg-white z-10 rounded-t-xl sm:rounded-t-2xl ">
                <div className="text-base sm:text-lg lg:text-xl font-bold text-secondary truncate pr-3">
                  {title}
                </div>
                <button
                  onClick={onClose}
                  className="hover:opacity-80 active:opacity-70 transition-opacity flex-shrink-0"
                >
                  <CrossSmallIcon className="w-8 h-8 sm:w-9 sm:h-9 lg:w-10 lg:h-10 text-secondary" />
                </button>
              </div>
              <div
                ref={modalContentRef}
                className={`overflow-y-auto flex-1 pdf-modal-content rounded-b-xl sm:rounded-b-2xl overscroll-contain ${
                  isKeyboardVisible ? "keyboard-visible" : ""
                }`}
                style={{
                  height: isKeyboardVisible
                    ? `${viewportHeight * 0.7}px`
                    : "calc(100% - 80px)", // Subtract header height
                  minHeight: 0, // Allow flex item to shrink
                }}
              >
                <div className="h-full w-full px-2 sm:pb-4 pb-2">
                  {children}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>,
    document.body
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(ModalComponent);
