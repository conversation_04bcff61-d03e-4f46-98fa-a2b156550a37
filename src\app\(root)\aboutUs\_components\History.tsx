"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import { DownIcon } from "@/ui/icons/navigation";
import Link from "next/link";

// const data = [
//   {
//     label: "2024",
//     items: [
//       {
//         text: "Lorem ipsum dolor sit",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra",
//       },
//       {
//         text: "Lorem ipsum dolor sit",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra",
//       },
//       {
//         text: "Lorem ipsum dolor sit",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do",
//       },
//     ],
//   },
//   {
//     label: "2025",
//     items: [
//       {
//         text: "Lorem ipsum dolor sit",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra",
//       },
//       {
//         text: "Lorem ipsum dolor sit",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra",
//       },
//       {
//         text: "Lorem ipsum dolor sit",
//         desc: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do",
//       },
//     ],
//   },
// ];
export default function History() {
  return (
    <div className="mt-8 lg:mt-[84px] grid grid-cols-1 text-center gap-4 items-center lg:gap-16">
      <div className="w-full lg:p-16 bg-primary p-8 rounded-2xl text-white">
        <div className="text-2xl lg:text-[32px] font-black">
          Born in Sydney,
          Built for You
        </div>
        <p className="mt-4 text-sm lg:text-base">
          Founded in Sydney, SEOAnalyser was built with one clear goal: to make
          local SEO smarter, faster, and more accessible for Australian
          businesses. Over time, we’ve grown into a trusted SEO platform that
          empowers companies across Australia—from small businesses to national
          brands—to own their digital presence with clarity, confidence, and
          real results.
        </p>
        <button className="!w-full lg:!w-auto mx-auto btn btn--primary !bg-white !text-primary !border-white mt-6 lg:mt-8">
          <Link href="/login">
         Start Free Trial </Link>
          
        </button>
      </div>
      {/* <div>
        <Swiper
          navigation={{
            nextEl: `.history-arrow-left`,
            prevEl: `.history-arrow-right`,
          }}
          modules={[Navigation]}
          spaceBetween={16}
        >
          {data.map((item, index) => (
            <SwiperSlide key={index}>
              <div className="p-4 rounded-lg bg-primary/10 text-sm font-bold text-primary">
                {item.label}
              </div>
              <div className="flex flex-col gap-4 mt-4">
                {item.items.map((subItem, subIndex) => (
                  <div
                    key={subIndex}
                    className="pb-4 border-b border-b-light-gray last:border-0"
                  >
                    <div className="text-xl lg:text-2xl font-bold text-secondary">
                      {subItem.text}
                    </div>
                    <p className="text-sm text-secondary mt-2 lg:text-base">
                      {subItem.desc}
                    </p>
                  </div>
                ))}
              </div>
            </SwiperSlide>
          ))}

          <div className="flex items-center gap-4 mt-4 lg:mt-8">
            <button
              className={`history-arrow-right btn btn--outline !py-1 !px-2.5 disabled:!border-transparent disabled:bg-secondary/20`}
            >
              <DownIcon className={"rotate-90 w-6 h-6"} />
            </button>
            <button
              className={`history-arrow-left btn btn--outline !py-1 !px-2.5 disabled:!border-transparent disabled:bg-secondary/20`}
            >
              <DownIcon className={"-rotate-90 w-6 h-6"} />
            </button>
          </div>
        </Swiper>
      </div> */}
    </div>
  );
}
