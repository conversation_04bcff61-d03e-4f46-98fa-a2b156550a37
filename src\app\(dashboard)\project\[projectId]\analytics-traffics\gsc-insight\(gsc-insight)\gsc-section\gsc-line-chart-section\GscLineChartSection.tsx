"use client";
import React, { useState } from "react";

/* =============================== COMPONENTS =============================== */
import GSCOverview from "../../../../overview/(overview)/gsc-overview/GSCOverview";
import Dropdown from "@/components/ui/Dropdown";

/* ========================================================================== */
const GscLineChartSection = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const filterOptions = ["web pages ", "Images", "Videos"];

  const [activeFilter, setActiveFilter] = useState(filterOptions[0]);
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <GSCOverview
      dropdownContent={
        <Dropdown>
          <Dropdown.Button>
            <span>{activeFilter}</span>
          </Dropdown.Button>
          <Dropdown.Options>
            {filterOptions.map((filter, index) => (
              <Dropdown.Option
                key={index}
                onClick={() => setActiveFilter(filter)}
              >
                {filter}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      }
    />
  );
};

export default GscLineChartSection;
