import { Metadata } from "next";
import Table from "@/ui/Table";

export const metadata: Metadata = {
  title: "Privacy Policy | SEO Analyser",
  description:
    "Learn how AI Business Insight Pty Ltd (SEO Analyser) collects, uses, and protects your personal information. Comprehensive privacy policy covering GDPR, CCPA, and Australian Privacy Principles.",
};

export default function PrivacyPolicy() {
  return (
    <div className="w-full mt-8 lg:mt-[84px] container">
      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <div className="mb-8 lg:mb-12">
          <span className="text-sm font-bold text-primary">Legal</span>
          <h1 className="text-2xl lg:text-[32px] font-black mt-2 lg:mt-4 text-secondary">
            Privacy Policy
          </h1>
          <p className="text-secondary text-sm lg:text-base mt-4">
            Last updated: <strong>January 18, 2025</strong>
          </p>
          <p className="text-secondary text-sm lg:text-base mt-2">
            This Privacy Policy explains how AI Business Insight Pty Ltd (ABN 68
            ***********), trading as SEO ANALYSER, handles your personal
            information in compliance with Australian Privacy Principles, GDPR,
            and US state privacy laws.
          </p>
        </div>

        {/* Table of Contents */}
        <div className="bg-light-gray-6 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-bold text-secondary mb-4">
            Table of Contents
          </h2>
          <nav className="space-y-2">
            <a
              href="#who-we-are"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              1. Who we are
            </a>
            <a
              href="#scope"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              2. Scope of this Privacy Policy
            </a>
            <a
              href="#what-we-collect"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              3. What we collect
            </a>
            <a
              href="#how-we-collect"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              4. How we collect information
            </a>
            <a
              href="#why-we-use"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              5. Why we use your information
            </a>
            <a
              href="#processor-controller"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              6. Role as Processor vs Controller
            </a>
            <a
              href="#disclosure"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              7. Disclosure to third parties
            </a>
            <a
              href="#international-transfers"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              8. International transfers
            </a>
            <a
              href="#cookies"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              9. Cookies & online advertising
            </a>
            <a
              href="#data-retention"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              10. Data retention & deletion
            </a>
            <a
              href="#security"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              11. Security
            </a>
            <a
              href="#your-rights"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              12. Your rights
            </a>
            <a
              href="#children-privacy"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              13. Children's Privacy
            </a>
            <a
              href="#complaints"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              14. Complaints
            </a>
            <a
              href="#changes"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              15. Changes to this Policy
            </a>
            <a
              href="#contact"
              className="block text-primary hover:text-secondary transition-colors duration-300"
            >
              16. Contact
            </a>
          </nav>
        </div>

        {/* Content Sections */}
        <div className="space-y-8 lg:space-y-12">
          {/* Section 1 - Who we are */}
          <section id="who-we-are">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              1. Who we are
            </h2>
            <div className="bg-light-gray-6 rounded-lg p-6">
              <p className="text-secondary text-sm lg:text-base mb-3">
                AI Business Insight Pty Ltd (ABN **************), trading as SEO
                ANALYSER ("SEO ANALYSER", "we", "us", "our"), owns and operates
                the website https://seoanalyser.com.au and related SaaS products
                (collectively, the "Services").
              </p>
              <p className="text-secondary text-sm lg:text-base">
                <strong>Privacy enquiries:</strong>{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:text-secondary transition-colors duration-300"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </section>

          {/* Section 2 - Scope */}
          <section id="scope">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              2. Scope of this Privacy Policy
            </h2>
            <p className="text-secondary text-sm lg:text-base mb-4">
              This Policy explains how we handle "personal information" under:
            </p>
            <ul className="list-disc list-inside space-y-2 text-secondary text-sm lg:text-base ml-4">
              <li>
                The Privacy Act 1988 (Cth) and Australian Privacy Principles
                (APPs);
              </li>
              <li>GDPR/UK GDPR for users in the EEA and UK; and</li>
              <li>
                US state laws such as the California Consumer Privacy Act (CCPA)
                and Nevada SB 220.
              </li>
            </ul>
          </section>

          {/* Section 3 - What we collect */}
          <section id="what-we-collect">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              3. What we collect
            </h2>

            {/* Data Collection Table */}
            <div className="mb-6 bg-white rounded-lg px-6 py-4 shadow-sm border border-light-gray">
              <Table>
                <Table.Header>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Category
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Examples
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Collected from
                  </th>
                </Table.Header>
                <Table.Body>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Account Data
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Name, email, business name, password hash
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Account creation
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Payment Data
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Last-4 digits of card, expiry, billing postcode (via
                      Stripe)
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Checkout
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Scan Data
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      URLs audited, audit results, settings, white-label logos
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Audit runs
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Usage & Device Data
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      IP address, cookie ID, browser type, OS, pages viewed
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Cookies, pixels
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Marketing Data
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Newsletter preferences, ad IDs
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Forms, emails, partners
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Support Records
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Chat logs, emails, bug reports
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Support channels
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Aggregated Statistics
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      De-identified audit trends
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Derived internally
                    </td>
                  </Table.Row>
                </Table.Body>
              </Table>
            </div>

            <div className="bg-primary/10 border border-primary rounded-lg p-4">
              <h3 className="text-lg font-semibold text-primary mb-2">
                Free Visitor Audits
              </h3>
              <p className="text-secondary text-sm lg:text-base">
                We set a first-party cookie and log IP addresses to enforce the
                "four-audits-per-domain-per-24-hours" limit. No other personal
                details are required.
              </p>
            </div>
          </section>

          {/* Section 4 - How we collect */}
          <section id="how-we-collect">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              4. How we collect information
            </h2>
            <p className="text-secondary text-sm lg:text-base mb-4">
              We collect personal information:
            </p>
            <ul className="list-disc list-inside space-y-2 text-secondary text-sm lg:text-base ml-4">
              <li>Directly from you (forms, emails, support channels)</li>
              <li>Automatically via cookies, pixels, and log files</li>
              <li>
                Through integrated third-party services (e.g., Stripe, Google
                Analytics)
              </li>
              <li>From publicly available sources (e.g., WHOIS, SERP data)</li>
            </ul>
          </section>

          {/* Section 5 - Why we use */}
          <section id="why-we-use">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              5. Why we use your information
            </h2>

            {/* Legal Basis Table */}
            <div className="mb-6 bg-white rounded-lg px-6 py-4 shadow-sm border border-light-gray">
              <Table>
                <Table.Header>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Purpose
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    AU legal basis
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    GDPR basis
                  </th>
                </Table.Header>
                <Table.Body>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Provide, maintain & secure Services
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      APP 3, 6
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Contract, Legitimate interests
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Process payments & manage billing
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      APP 3
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Contract
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Enforce fair-use & prevent fraud
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      APP 6
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Legitimate interests
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Send service/marketing emails
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      APP 6/7
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Consent (marketing), Legitimate interests (service)
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Improve product & analytics
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      APP 6
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Legitimate interests
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Meet legal & tax obligations
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      APP 3, 6
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Legal obligation
                    </td>
                  </Table.Row>
                </Table.Body>
              </Table>
            </div>

            <div className="bg-primary-yellow/10 border border-primary-yellow rounded-lg p-4">
              <p className="text-secondary text-sm lg:text-base">
                You may opt out of marketing emails anytime via the unsubscribe
                link.
              </p>
            </div>
          </section>

          {/* Section 6 - Processor vs Controller */}
          <section id="processor-controller">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              6. Role as Processor vs Controller
            </h2>
            <div className="space-y-4">
              <div className="bg-light-gray-6 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-primary mb-2">
                  Data Controller
                </h3>
                <p className="text-secondary text-sm lg:text-base">
                  For most activities, we act as the data controller (deciding
                  purposes and means of processing).
                </p>
              </div>
              <div className="bg-primary/10 border border-primary rounded-lg p-4">
                <h3 className="text-lg font-semibold text-primary mb-2">
                  Data Processor
                </h3>
                <p className="text-secondary text-sm lg:text-base">
                  When you upload Customer Content or run audits involving
                  third-party personal data, we act as your data processor and
                  handle that content strictly on your instructions, never for
                  our own benefit.
                </p>
              </div>
            </div>
          </section>

          {/* Section 7 - Disclosure */}
          <section id="disclosure">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              7. Disclosure to third parties
            </h2>

            {/* Third Party Recipients Table */}
            <div className="mb-6 bg-white rounded-lg px-6 py-4 shadow-sm border border-light-gray">
              <Table>
                <Table.Header>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Recipient
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Purpose
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Location
                  </th>
                </Table.Header>
                <Table.Body>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Stripe Payments
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Payment processing, fraud prevention
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      AU, USA
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      OVH Australia
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Cloud infrastructure
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      AU
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Google LLC
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Analytics and advertising services, APIs, and
                      authentication
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      USA
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      AWS-SES
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Transactional & marketing email
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      USA
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Retention.com
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Email retargeting
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      USA
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Moz
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Software Company
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      USA
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Professional advisers
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Accounting, legal services
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      AU
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Government bodies
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Compliance with applicable laws
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      As required
                    </td>
                  </Table.Row>
                </Table.Body>
              </Table>
            </div>

            <div className="bg-primary-red/10 border border-primary-red rounded-lg p-4">
              <h3 className="text-lg font-semibold text-primary-red mb-2">
                Important Notice
              </h3>
              <p className="text-secondary text-sm lg:text-base">
                We do not sell personal information under CCPA/Nevada laws, nor
                do we use your data to develop or train general-purpose AI/ML
                models.
              </p>
            </div>
          </section>

          {/* Section 8 - International transfers */}
          <section id="international-transfers">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              8. International transfers
            </h2>
            <p className="text-secondary text-sm lg:text-base mb-4">
              If your data is transferred outside Australia, we rely on:
            </p>
            <ul className="list-disc list-inside space-y-2 text-secondary text-sm lg:text-base ml-4">
              <li>
                OVH Australia and Stripe's binding corporate rules or Standard
                Contractual Clauses (SCCs)
              </li>
              <li>
                Contractual clauses ensuring privacy standards similar to APPs
                or GDPR
              </li>
            </ul>
          </section>

          {/* Section 9 - Cookies */}
          <section id="cookies">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              9. Cookies & online advertising
            </h2>

            {/* Cookies Table */}
            <div className="mb-6 bg-white rounded-lg px-6 py-4 shadow-sm border border-light-gray">
              <Table>
                <Table.Header>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Cookie type
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Purpose
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Lifespan
                  </th>
                </Table.Header>
                <Table.Body>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Essential
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Login sessions
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      24 hours
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Audit-limit
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Enforcing audit quotas
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      24 hours
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Analytics
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Traffic analysis (GA4, GSC)
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      2 years
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Retargeting
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Showing targeted ads (Retention.com)
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Up to 1 year
                    </td>
                  </Table.Row>
                </Table.Body>
              </Table>
            </div>

            <div className="bg-primary/10 border border-primary rounded-lg p-4">
              <p className="text-secondary text-sm lg:text-base">
                You can block cookies in your browser, although some website
                features might be impacted.
              </p>
            </div>
          </section>

          {/* Section 10 - Data retention */}
          <section id="data-retention">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              10. Data retention & deletion
            </h2>

            {/* Data Retention Table */}
            <div className="mb-6 bg-white rounded-lg">
              <Table>
                <Table.Header>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Data set
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Retention
                  </th>
                </Table.Header>
                <Table.Body>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Free-tier audit data
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      3 days
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Pro Plan audits
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Life of account + 90 days
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Payment records
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      7 years (for tax purposes)
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Support logs
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      5 years
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Backup archives
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Rolling 30–90 days
                    </td>
                  </Table.Row>
                </Table.Body>
              </Table>
            </div>

            <div className="bg-primary/10 border border-primary rounded-lg p-4">
              <p className="text-secondary text-sm lg:text-base">
                Early data-deletion requests will be honoured unless legal
                obligations require otherwise.
              </p>
            </div>
          </section>

          {/* Section 11 - Security */}
          <section id="security">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              11. Security
            </h2>
            <p className="text-secondary text-sm lg:text-base mb-4">
              We implement industry-standard security measures including:
            </p>
            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <div className="bg-primary/5 rounded-lg p-4">
                <ul className="list-disc list-inside space-y-1 text-secondary text-sm lg:text-base ml-4">
                  <li>TLS 1.3</li>
                  <li>AES-256 encryption at rest</li>
                  <li>Multi-factor authentication (MFA)</li>
                  <li>Role-based access controls</li>
                </ul>
              </div>
              <div className="bg-primary/5 rounded-lg p-4">
                <ul className="list-disc list-inside space-y-1 text-secondary text-sm lg:text-base ml-4">
                  <li>Regular security testing</li>
                  <li>24×7 monitoring</li>
                  <li>Incident response procedures</li>
                  <li>Employee security training</li>
                </ul>
              </div>
            </div>
            <div className="bg-primary-yellow/10 border border-primary-yellow rounded-lg p-4">
              <p className="text-secondary text-sm lg:text-base">
                <strong>Important:</strong> While we strive for robust security,
                no internet transmission is 100% secure.
              </p>
            </div>
          </section>

          {/* Section 12 - Your rights */}
          <section id="your-rights">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              12. Your rights
            </h2>

            {/* Rights Table */}
            <div className="mb-6 bg-white rounded-lg">
              <Table>
                <Table.Header>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Region
                  </th>
                  <th className="text-left p-4 font-semibold text-secondary">
                    Key rights
                  </th>
                </Table.Header>
                <Table.Body>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Australia
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Access and correction (APP 12-13)
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      EEA/UK
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Access, rectify, erase, restrict, portability, object,
                      withdraw consent
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      California
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Access, delete, correct, opt-out of "sale/sharing", limit
                      sensitive PI
                    </td>
                  </Table.Row>
                  <Table.Row>
                    <td className="p-4 text-secondary text-sm lg:text-base font-medium">
                      Nevada
                    </td>
                    <td className="p-4 text-secondary text-sm lg:text-base">
                      Opt-out of data "sale" (we do not sell)
                    </td>
                  </Table.Row>
                </Table.Body>
              </Table>
            </div>

            <div className="bg-primary/10 border border-primary rounded-lg p-4">
              <p className="text-secondary text-sm lg:text-base">
                To exercise your rights, email{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:text-secondary transition-colors duration-300"
                >
                  <EMAIL>
                </a>
                . We respond to all valid requests within 30 days (Australia) or
                one calendar month (EU/UK GDPR).
              </p>
            </div>
          </section>

          {/* Section 13 - Children's Privacy */}
          <section id="children-privacy">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              13. Children's Privacy
            </h2>

            <div className="bg-primary-red/10 border border-primary-red rounded-lg p-6">
              <h3 className="text-lg font-semibold text-primary-red mb-3">
                Age Restriction
              </h3>
              <p className="text-secondary text-sm lg:text-base mb-3">
                Our services are not intended for users under the age of 18. We
                do not knowingly collect personal information from minors. If we
                learn that we've collected personal data from a minor, we will
                promptly delete it.
              </p>
              <p className="text-secondary text-sm lg:text-base">
                If you believe a minor's data has been collected, contact us
                immediately at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:text-secondary transition-colors duration-300"
                >
                  <EMAIL>
                </a>
                .
              </p>
            </div>
          </section>

          {/* Section 14 - Complaints */}
          <section id="complaints">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              14. Complaints
            </h2>
            <div className="space-y-4">
              <p className="text-secondary text-sm lg:text-base">
                For any privacy concerns or complaints, please email{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:text-secondary transition-colors duration-300"
                >
                  <EMAIL>
                </a>
                .
              </p>
              <div className="bg-light-gray-6 rounded-lg p-4">
                <p className="text-secondary text-sm lg:text-base">
                  If unsatisfied with our response, you may lodge a complaint
                  with the Office of the Australian Information Commissioner
                  (OAIC) or your local data protection authority.
                </p>
              </div>
            </div>
          </section>

          {/* Section 15 - Changes */}
          <section id="changes">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              15. Changes to this Policy
            </h2>
            <div className="space-y-4">
              <p className="text-secondary text-sm lg:text-base">
                We may update this Privacy Policy periodically. Any material
                changes will be communicated via email (for registered users) or
                site notification at least 30 days before taking effect.
                Regularly reviewing this Policy is recommended.
              </p>
            </div>
          </section>

          {/* Section 16 - Contact */}
          <section id="contact">
            <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
              16. Contact
            </h2>

            <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6">
              <p className="text-secondary text-sm lg:text-base mb-4">
                To request to review, update, or delete your personal data,
                please contact us. To ask questions or comment about this
                privacy Policy and our privacy and data protection practices,
                contact us via:
              </p>

              <div className="text-center">
                <p className="text-lg font-semibold text-primary mb-2">
                  <a
                    href="mailto:<EMAIL>"
                    className="text-primary hover:text-secondary transition-colors duration-300"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </section>

          {/* Legal Disclaimer */}
          <section className="mt-12 pt-8 border-t border-light-gray">
            <div className="bg-light-gray-6 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-secondary mb-3">
                Legal Disclaimer
              </h3>
              <p className="text-secondary text-sm lg:text-base mb-3">
                This Privacy Policy is designed to comply with applicable
                privacy laws including the Australian Privacy Principles (APPs),
                the General Data Protection Regulation (GDPR), and the
                California Consumer Privacy Act (CCPA).
              </p>
              <p className="text-secondary text-sm lg:text-base">
                <strong>Note:</strong> This privacy policy is for informational
                purposes and should not be considered as legal advice. For
                specific legal questions, please consult with a qualified
                attorney.
              </p>
            </div>
          </section>

          {/* Back to Top */}
          <div className="mt-8 text-center">
            <a
              href="#top"
              className="inline-flex items-center gap-2 text-primary hover:text-secondary transition-colors duration-300 font-semibold"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 10l7-7m0 0l7 7m-7-7v18"
                />
              </svg>
              Back to Top
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
