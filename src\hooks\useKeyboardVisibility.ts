import { useState, useEffect } from 'react';

/**
 * Custom hook to detect keyboard visibility on mobile devices
 * @returns An object containing isKeyboardVisible state and viewport height
 */
export default function useKeyboardVisibility() {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [viewportHeight, setViewportHeight] = useState<number>(0);

  useEffect(() => {
    // Set initial viewport height
    setViewportHeight(window.visualViewport?.height || window.innerHeight);

    // Function to detect keyboard visibility based on viewport height changes
    const detectKeyboard = () => {
      if (!window.visualViewport) return;
      
      const currentHeight = window.visualViewport.height;
      const windowHeight = window.innerHeight;
      
      // If the visual viewport height is significantly less than the window height,
      // we can assume the keyboard is visible
      const keyboardVisible = currentHeight < windowHeight * 0.75;
      
      setIsKeyboardVisible(keyboardVisible);
      setViewportHeight(currentHeight);
    };

    // Add event listeners
    window.visualViewport?.addEventListener('resize', detectKeyboard);
    window.visualViewport?.addEventListener('scroll', detectKeyboard);

    // Initial detection
    detectKeyboard();

    // Cleanup
    return () => {
      window.visualViewport?.removeEventListener('resize', detectKeyboard);
      window.visualViewport?.removeEventListener('scroll', detectKeyboard);
    };
  }, []);

  return { isKeyboardVisible, viewportHeight };
}
