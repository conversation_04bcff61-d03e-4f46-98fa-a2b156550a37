import { ComponentProps, useCallback, useState, useEffect, memo, ReactNode } from "react";

type InputProps = {
  label: string;
  name: string;
  onChange: (value: string) => void;
  icon?: ReactNode;
} & Omit<ComponentProps<"input">, "onChange">;

// Create a memoized component to prevent unnecessary re-renders
function RHTextBoxComponent({
  label,
  className,
  onChange,
  name,
  value,
  icon,
  ...rest
}: InputProps) {
  // Local state to manage input value for smoother typing experience
  const [localValue, setLocalValue] = useState(value || "");

  // Update local value when prop value changes (only if different)
  useEffect(() => {
    if (value !== undefined && value !== localValue) {
      setLocalValue(value as string);
    }
  }, [value]);

  // Optimized onChange handler without debounce for immediate response
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      // Update local state immediately for responsive UI
      setLocalValue(newValue);

      // Call parent onChange without debounce for immediate response
      // This eliminates the lag between typing and seeing the result
      onChange(newValue);
    },
    [onChange]
  );

  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={name} className="text-secondary text-sm lg:text-base flex items-center gap-2">
        {icon && <span className="text-secondary">{icon}</span>}
        {label}
      </label>
      <input
        name={name}
        {...rest}
        value={localValue}
        onChange={handleChange}
        className={`textField__input ${className || ""}`}
        // Add performance attributes to prevent browser features from interfering
        data-lpignore="true" // Disable LastPass autofill
        autoComplete={rest.autoComplete || "off"} // Prevent browser autofill unless explicitly set
      />
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(RHTextBoxComponent);
