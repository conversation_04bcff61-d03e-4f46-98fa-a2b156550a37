import React from "react";

/* =============================== COMPONENTS =============================== */
import LineChartCard from "../../_components/LineChartCard";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import NoData from "../../../analytic-insight/_components/NoData";

/* ================================== TYPES ================================= */
import type { ChartResponse } from "./AudienceOverview.types";

/* ================================== AXIOS ================================= */
import DateRange from "../../../_components/date-range/DateRange";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ================================== TYPES ================================= */
import type { LineChartData } from "@/types/LineChartCard.type";

/* ================================ API CALLS =============================== */
import { useAudienceOverview } from "./AudienceOverview.hook";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import SmallChartSkeleton from "../../../_components/small-chart-skeleton/SmallChartSkeleton";
import Link from "next/link";
import { useProjectId } from "@/hooks/useProjectId";

/* ========================================================================== */
const AudienceOverview = () => {
  const { projectId } = useProjectId();
  const {
    data: ChartsData,
    isLoading: ChartsIsLoading,
    isPending: ChartsIsPending,
  } = useAudienceOverview();
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handleSetChartData = (chart: LineChartData) => {
    setChartData(chart);
    show();
  };

  const renderSkeletonCards = () => {
    return Array.from({ length: 10 }).map((_, index) => (
      <SmallChartSkeleton
        key={index}
        className={index > 7 ? "lg:col-span-2" : undefined}
      />
    ));
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (!ChartsData && !ChartsIsLoading && !ChartsIsPending) {
    return <NoData title="Audience Overview" />;
  }

  return (
    <Card className="space-y-6">
      <div className="space-y-2">
        <Title>Audience Overview</Title>
        <DateRange />
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4">
        {ChartsIsLoading || ChartsIsPending ? (
          renderSkeletonCards()
        ) : (
          <AnimatePresence>
            {Array.isArray(ChartsData) &&
              ChartsData?.map((chart: ChartResponse, index: number) => {
                const isLastTwoInRowOfFour =
                  ChartsData.length % 4 === 2 && index >= ChartsData.length - 2;

                const colSpan = isLastTwoInRowOfFour
                  ? "lg:col-span-2"
                  : "col-span-1";

                return (
                  <motion.div
                    key={chart.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                    className={`w-full ${colSpan}`}
                  >
                    <LineChartCard
                      title={chart.title}
                      className="w-full cursor-pointer"
                      bigNumber={chart.bigNumber}
                      smallNumber={chart.smallNumber}
                      data={chart.data}
                      onClick={() => handleSetChartData(chart)}
                    />
                  </motion.div>
                );
              })}
          </AnimatePresence>
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={`/project/${projectId}/analytics-traffics/analytic-insight?tab=audience`}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default AudienceOverview;
