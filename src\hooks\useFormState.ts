import { useState, useCallback } from "react";

export interface FormState {
  hasActiveInput: boolean;
  hasTypedContent: boolean;
  isDirty: boolean;
}

export interface FormStateActions {
  setHasActiveInput: (active: boolean) => void;
  setHasTypedContent: (hasContent: boolean) => void;
  setIsDirty: (dirty: boolean) => void;
  reset: () => void;
  shouldPreventModalClose: () => boolean;
}

export default function useFormState(): [FormState, FormStateActions] {
  const [hasActiveInput, setHasActiveInput] = useState(false);
  const [hasTypedContent, setHasTypedContent] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  const reset = useCallback(() => {
    setHasActiveInput(false);
    setHasTypedContent(false);
    setIsDirty(false);
  }, []);

  const shouldPreventModalClose = useCallback(() => {
    return hasActiveInput || hasTypedContent || isDirty;
  }, [hasActiveInput, hasTypedContent, isDirty]);

  const formState: FormState = {
    hasActiveInput,
    hasTypedContent,
    isDirty,
  };

  const actions: FormStateActions = {
    setHasActiveInput,
    setHasTypedContent,
    setIsDirty,
    reset,
    shouldPreventModalClose,
  };

  return [formState, actions];
}
