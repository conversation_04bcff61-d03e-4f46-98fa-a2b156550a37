import { TableDataRequest } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { NextResponse } from "next/server";

const EVENTS_AND_CONVERSATIONS_TABLE_DATA: TableDataRequest = {
  tableData: {
    tableHeadings: [
      "CONVERSION",
      "SESSIONS",
      "ENGAGED SESSIONS",
      "NEW USERS",
      "TOTAL USERS",
      "VIEWS",
      "ENGAGED TIME",
      "ENGAGED RATE",
      "EVENT COUNT",
      "CONVERSIONS",
    ],

    tableBody: [
      [
        { value: "Conversion Name" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Conversion Name" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Conversion Name" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      [
        { value: "Conversion Name" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
    ],
  },
  pagination: { totalPages: 20, initialPage: 1 },
};

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function GET() {
  await delay(1000);
  return NextResponse.json(EVENTS_AND_CONVERSATIONS_TABLE_DATA);
}
