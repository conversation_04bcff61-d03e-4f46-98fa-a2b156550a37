"use client";

import React, { useEffect, useState, useRef, useCallback } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  getSharedAnalysisApi,
  checkStatusAnalyzeApi,
} from "@/services/analyzeService";
import { SeoAnalyzerPdf } from "@/app/(root)/audit/[urlName]/_/components/pdf";
import { useReactToPrint } from "react-to-print";
import { DownloadIcon } from "@/ui/icons/general";
import paymentService, {
  PaymentStatusResponse,
} from "@/services/paymentService";
import profileService from "@/services/profileService";
import whiteLabelService from "@/services/whiteLabelService";
import { useAuthStore } from "@/store/authStore";
import localData from "./data.json";

// Types
interface WhiteLabelInfo {
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
}

interface AppState {
  loading: boolean;
  error: string | null;
  paymentError: string | null;
  data: any;
  paymentStatus: PaymentStatusResponse | null;
  whiteLabelInfo: WhiteLabelInfo | null;
  showAuthModal: boolean;
  hasProfileNoSubscription: boolean;
  isPdfReady: boolean;
  showSubscriptionModal: boolean;
}

export default function WhiteLabelPdfClient() {
  // ===== TESTING FLAGS =====
  // Set this flag to True to load data from local JSON file for offline testing
  // When True, the app will load data from src/app/(root)/white-label-pdf/data.json instead of making API calls
  // This is useful for development and testing when you don't have internet access
  // or want to avoid making unnecessary API calls
  const useMockData = false; // Change to true for offline testing
  // ========================

  const searchParams = useSearchParams();
  const router = useRouter();
  const shareId = searchParams?.get("share") || null;
  const paymentId = searchParams?.get("uid") || null;

  // Consolidated state
  const [state, setState] = useState<AppState>({
    loading: true,
    error: null,
    paymentError: null,
    data: null,
    paymentStatus: null,
    whiteLabelInfo: null,
    showAuthModal: false,
    hasProfileNoSubscription: false,
    isPdfReady: false,
    showSubscriptionModal: false,
  });

  // Flag to prevent multiple simultaneous API calls
  const [isDataFetching, setIsDataFetching] = useState(false);

  // Auth store
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const fetchProfile = useAuthStore((state) => state.fetchProfile);

  const contentRef = useRef<HTMLDivElement>(null);

  // Helper function to load mock data
  const loadMockData = useCallback(async () => {
    // Simulate API delay for realistic testing
    await new Promise((resolve) => setTimeout(resolve, 500));
    return localData;
  }, []);

  // Helper function to update state
  const updateState = useCallback((updates: Partial<AppState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Destructure state for easier access
  const {
    loading,
    error,
    paymentError,
    data,
    paymentStatus,
    whiteLabelInfo,
    showAuthModal,
    hasProfileNoSubscription,
    isPdfReady,
    showSubscriptionModal,
  } = state;

  // Verify payment and get white label info
  const verifyPayment = useCallback(
    async (paymentId: string): Promise<WhiteLabelInfo | null> => {
      try {
        if (!isAuthenticated) {
          const isAuth = await fetchProfile();
          if (!isAuth) {
            updateState({
              showAuthModal: true,
              error:
                "Authentication required. Please log in to view this Pro Plan PDF.",
            });
            return null;
          }
        }

        const status = await paymentService.checkPaymentStatus(paymentId);
        updateState({ paymentStatus: status });

        const hasSubscription =
          await profileService.hasActiveProPlanSubscription();
        if (!hasSubscription) {
          updateState({
            hasProfileNoSubscription: true,
            showAuthModal: false,
            loading: false,
            showSubscriptionModal: true,
            error: "You need a Pro Plan subscription to access this feature.",
          });
          return null;
        }

        if (status?.whitelabel_setting) {
          return {
            brand_name: status.whitelabel_setting.brand_name || undefined,
            brand_website: status.whitelabel_setting.website || undefined,
            brand_photo: status.whitelabel_setting.logo || null,
          };
        }

        if (
          status &&
          status.payment_status !== "success" &&
          status.status !== "success"
        ) {
          updateState({
            paymentError: `Payment verification failed: ${
              status.message || "Payment not successful"
            }`,
            showAuthModal: true,
            error: "Payment verification failed. Please log in to continue.",
          });
          return null;
        }

        updateState({
          showAuthModal: true,
          error: "No white label settings found. Please log in to continue.",
        });
        return null;
      } catch (error: any) {
        console.error("Error verifying payment:", error);
        updateState({
          showAuthModal: true,
          paymentError:
            error.response?.data?.message ||
            error.message ||
            "Failed to verify payment",
          error:
            error.response?.status === 401
              ? "Authentication required. Please log in to view this white label PDF."
              : "Payment verification failed. Please log in to continue.",
        });
        return null;
      }
    },
    [isAuthenticated, fetchProfile, updateState]
  );

  // Check white label subscription
  const checkWhiteLabelSubscription =
    useCallback(async (): Promise<WhiteLabelInfo | null> => {
      try {
        if (!isAuthenticated) {
          const isAuth = await fetchProfile();
          if (!isAuth) {
            updateState({
              showAuthModal: true,
              error:
                "Authentication required. Please log in to view this Pro Plan PDF.",
            });
            return null;
          }
        }

        const hasSubscription =
          await profileService.hasActiveProPlanSubscription();
        if (!hasSubscription) {
          updateState({
            hasProfileNoSubscription: true,
            showAuthModal: false,
            loading: false,
            showSubscriptionModal: true,
            error: "You need a Pro Plan subscription to access this feature.",
          });
          return null;
        }

        const whiteLabelResponse =
          await whiteLabelService.getWhiteLabelSettings();

        if (whiteLabelResponse.success && whiteLabelResponse.data) {
          const whiteLabelData = {
            brand_name: whiteLabelResponse.data.brand_name || undefined,
            brand_website: whiteLabelResponse.data.website || undefined,
            brand_photo: whiteLabelResponse.data.logo || null,
          };
          return whiteLabelData;
        }

        updateState({
          showAuthModal: true,
          error:
            "Failed to retrieve Pro Plan settings. Please log in to continue.",
        });
        return null;
      } catch (error: any) {
        console.error("Error checking Pro Plan subscription:", error);
        updateState({
          showAuthModal: true,
          error:
            error.response?.status === 401
              ? "Authentication required. Please log in to view this Pro Plan PDF."
              : "Failed to check subscription status. Please log in to continue.",
        });
        return null;
      }
    }, [isAuthenticated, fetchProfile, updateState]);

  // Main data fetching effect
  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      // Prevent multiple simultaneous calls
      if (!isMounted || isDataFetching) return;

      setIsDataFetching(true);
      // If mock data flag is set to true, return local data
      if (useMockData) {
        try {
          console.log("Using mock data for testing...");
          const mockData = await loadMockData();

          // Set mock white label info
          const mockWhiteLabel: WhiteLabelInfo = {
            brand_name: "seo analyser",
            brand_website: "https://www.seoanalyser.com.au",
            brand_photo: null,
          };

          updateState({
            data: mockData,
            whiteLabelInfo: mockWhiteLabel,
            loading: false,
          });
          setIsDataFetching(false);
          return;
        } catch (error) {
          console.error("Error loading mock data:", error);
          updateState({
            error: "Failed to load mock data",
            loading: false,
          });
          setIsDataFetching(false);
          return;
        }
      }

      if (!shareId) {
        updateState({
          error: "No share ID provided",
          showAuthModal: true,
          loading: false,
        });
        setIsDataFetching(false);
        return;
      }

      try {
        let whiteLabel: WhiteLabelInfo | null = null;

        if (paymentId) {
          whiteLabel = await verifyPayment(paymentId);
        } else {
          whiteLabel = await checkWhiteLabelSubscription();
        }

        if (hasProfileNoSubscription) {
          setIsDataFetching(false);
          return;
        }

        if (whiteLabel) {
          if (isMounted) {
            updateState({ whiteLabelInfo: whiteLabel });
          }
        } else {
          if (!showAuthModal) {
            updateState({
              showAuthModal: true,
              error:
                "Pro Plan information required. Please log in to continue.",
            });
          }
          updateState({ loading: false });
          setIsDataFetching(false);
          return;
        }

        // Try to get the data from the share API first
        let analysisData;
        try {
          analysisData = await getSharedAnalysisApi(shareId);
        } catch (shareError: any) {
          console.log(
            "Share API failed, trying status API as fallback:",
            shareError.message
          );

          // If share API fails, try the status API as fallback
          try {
            analysisData = await checkStatusAnalyzeApi(shareId);
          } catch (statusError: any) {
            console.error("Both share and status APIs failed:", statusError);
            throw statusError; // Re-throw the status API error
          }
        }

        if (isMounted) {
          updateState({ data: analysisData, loading: false });
          setIsDataFetching(false);
        }
      } catch (err: any) {
        console.error("Error fetching analysis data:", err);
        if (isMounted) {
          updateState({
            showAuthModal: true,
            error:
              err.message ||
              "Failed to fetch analysis data. Please log in to continue.",
            loading: false,
          });
          setIsDataFetching(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
      setIsDataFetching(false);
    };
  }, [shareId, paymentId, isAuthenticated, useMockData]);

  // Handle auth success
  const handleAuthSuccess = useCallback(() => {
    updateState({ showAuthModal: false });
    window.location.reload();
  }, [updateState]);

  // Handle subscription modal actions
  const handleGoHome = useCallback(() => {
    router.push("/");
  }, [router]);

  const handleGoPricing = useCallback(() => {
    router.push("/pricing");
  }, [router]);

  const handleCloseSubscriptionModal = useCallback(() => {
    router.push("/");
  }, [router]);

  // Enhanced auth redirect effect - uses centralized auth provider
  useEffect(() => {
    if (showAuthModal && !hasProfileNoSubscription && !isAuthenticated) {
      // Store current URL for post-login redirect
      if (typeof window !== "undefined") {
        sessionStorage.setItem("postLoginRedirect", window.location.href);
      }

      // Redirect to login page instead of showing modal
      router.push("/login");
    }
  }, [showAuthModal, hasProfileNoSubscription, isAuthenticated, router]);

  // Subscription modal effect - handle escape key and background click
  useEffect(() => {
    if (showSubscriptionModal) {
      const handleEscape = (event: KeyboardEvent) => {
        if (event.key === "Escape") {
          handleCloseSubscriptionModal();
        }
      };

      document.addEventListener("keydown", handleEscape);
      return () => {
        document.removeEventListener("keydown", handleEscape);
      };
    }
  }, [showSubscriptionModal, handleCloseSubscriptionModal]);

  // Image caching and preloading function with CORS handling
  const preloadImages = useCallback(async (): Promise<boolean> => {
    if (!data?.result) return true;

    const imageUrls: string[] = [];

    // Collect all image URLs from the data
    if (data.result.desktop_screenshot_url) {
      imageUrls.push(data.result.desktop_screenshot_url);
    }

    // Add brand photo if available
    if (whiteLabelInfo?.brand_photo) {
      imageUrls.push(whiteLabelInfo.brand_photo);
    }

    // Filter out problematic external URLs that cause CORS issues
    const validImageUrls = imageUrls.filter(
      (url) =>
        url &&
        !url.includes("cdn.simpleicons.org") &&
        !url.includes("external") &&
        !url.includes("favicon") &&
        !url.includes("icon") &&
        url.startsWith("http")
    );

    if (validImageUrls.length === 0) return true;

    try {
      const imagePromises = validImageUrls.map((url) => {
        return new Promise<boolean>((resolve) => {
          // Try multiple loading strategies for CORS issues
          const tryLoadImage = (
            crossOriginMode: string | null = null,
            attempt = 1
          ) => {
            const img = new Image();

            // Set crossOrigin based on attempt
            if (crossOriginMode) {
              img.crossOrigin = crossOriginMode;
            }

            const timeout = setTimeout(() => {
              console.warn(`Image timeout (attempt ${attempt}): ${url}`);

              // Try different CORS modes
              if (attempt === 1) {
                tryLoadImage("use-credentials", 2);
              } else if (attempt === 2) {
                tryLoadImage(null, 3); // No CORS
              } else {
                resolve(false);
              }
            }, 3000); // Reduced timeout per attempt

            img.onload = () => {
              clearTimeout(timeout);
              console.log(
                `Image loaded successfully (attempt ${attempt}): ${url}`
              );
              resolve(true);
            };

            img.onerror = (error) => {
              clearTimeout(timeout);
              console.warn(
                `Image failed to load (attempt ${attempt}): ${url}`,
                error
              );

              // Try different CORS modes
              if (attempt === 1) {
                tryLoadImage("use-credentials", 2);
              } else if (attempt === 2) {
                tryLoadImage(null, 3); // No CORS
              } else {
                // Final attempt: try to load via proxy or accept failure
                console.warn(`All attempts failed for: ${url}`);
                resolve(false);
              }
            };

            img.src = url;
          };

          // Start with anonymous CORS
          tryLoadImage("anonymous", 1);
        });
      });

      const results = await Promise.all(imagePromises);
      const successCount = results.filter(Boolean).length;
      console.log(
        `Preloaded ${successCount}/${validImageUrls.length} images successfully`
      );

      return successCount >= 0; // Return true even if no images loaded (graceful degradation)
    } catch (error) {
      console.error("Error preloading images:", error);
      return true; // Continue with PDF generation even if preloading fails
    }
  }, [data, whiteLabelInfo]);

  // PDF ready effect with image preloading
  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      let isComponentMounted = true;

      const initializePdf = async () => {
        // First, preload images
        console.log("Starting image preloading...");
        const imagesLoaded = await preloadImages();

        if (!isComponentMounted) return;

        // Wait additional time for images to be fully cached
        const additionalWait = imagesLoaded ? 2000 : 1000;

        setTimeout(() => {
          if (isComponentMounted) {
            console.log("PDF ready after image preloading");
            updateState({ isPdfReady: true });

            // Dispatch custom event for any listeners
            window.dispatchEvent(
              new CustomEvent("pdf-content-ready", {
                detail: { ready: true, imagesLoaded },
              })
            );
          }
        }, additionalWait);
      };

      initializePdf();

      const handlePdfReady = (event: Event) => {
        const customEvent = event as CustomEvent;
        if (customEvent.detail?.ready && isComponentMounted) {
          updateState({ isPdfReady: true });
        }
      };

      window.addEventListener("pdf-content-ready", handlePdfReady);

      return () => {
        isComponentMounted = false;
        window.removeEventListener("pdf-content-ready", handlePdfReady);
      };
    }
  }, [data, preloadImages, updateState]);

  // Print configuration with enhanced image handling
  const handlePrint = useReactToPrint({
    contentRef,
    documentTitle: `${whiteLabelInfo?.brand_name || "SEO"} Analysis - ${
      data?.result?.url || ""
    }`,
    onPrintError: (error) => {
      console.error("Print failed:", error);
      // Don't throw error, just log it
    },
    onBeforePrint: () => {
      console.log("Print starting, handling CORS images...");

      return new Promise<void>((resolve) => {
        // Handle images with CORS issues
        const images = document.querySelectorAll("img");
        let imagesToProcess = 0;
        let imagesProcessed = 0;

        const checkComplete = () => {
          imagesProcessed++;
          if (imagesProcessed >= imagesToProcess) {
            console.log("All images processed for print");
            resolve();
          }
        };

        images.forEach((img) => {
          // Hide all problematic external images that cause CORS issues
          if (
            img.src &&
            (img.src.includes("cdn.simpleicons.org") ||
              img.src.includes("external") ||
              img.src.includes("favicon") ||
              img.src.includes("icon"))
          ) {
            // Hide problematic images immediately
            img.style.display = "none";
            img.style.visibility = "hidden";
            return;
          }

          if (
            img.src &&
            !img.src.includes("cdn.simpleicons.org") &&
            !img.src.includes("external") &&
            !img.src.includes("favicon") &&
            !img.src.includes("icon")
          ) {
            imagesToProcess++;

            img.style.display = "block";
            img.style.visibility = "visible";
            img.style.opacity = "1";

            // Handle CORS issues by removing crossOrigin for print
            if (img.crossOrigin) {
              img.removeAttribute("crossorigin");
            }

            // If image failed to load or has CORS issues
            if (!img.complete || img.naturalHeight === 0) {
              console.warn(`Fixing image for print: ${img.src}`);

              // Create a new image element without CORS restrictions
              const newImg = new Image();
              newImg.onload = () => {
                // Replace the failed image
                img.src = newImg.src;
                img.style.display = "block";
                img.style.visibility = "visible";
                checkComplete();
              };

              newImg.onerror = () => {
                // Hide failed images to prevent broken image icons in PDF
                console.warn(`Hiding failed image: ${img.src}`);
                img.style.display = "none";
                checkComplete();
              };

              // Load without CORS restrictions
              newImg.src = img.src;
            } else {
              checkComplete();
            }
          }
        });

        // If no images to process, resolve immediately
        if (imagesToProcess === 0) {
          resolve();
        }

        // Timeout fallback
        setTimeout(() => {
          console.log("Image processing timeout, proceeding with print");
          resolve();
        }, 2000);
      });
    },
    pageStyle: `
      @page {
        size: A4 portrait;
        margin: 0.5cm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
        img {
          display: block !important;
          break-inside: avoid !important;
          max-width: 100% !important;
          visibility: visible !important;
          opacity: 1 !important;
          /* Force image loading */
          image-rendering: auto !important;
          image-rendering: -webkit-optimize-contrast !important;
        }
        /* Hide problematic external images that cause CORS issues */
        img[src=""],
        img:not([src]),
        img[src*="cdn.simpleicons.org"],
        img[src*="external"],
        img[src*="favicon"],
        img[src*="icon"],
        img[alt*="technology"],
        img[alt*="tech"] {
          display: none !important;
          visibility: hidden !important;
        }
        /* Handle CORS-blocked images gracefully */
        img[src*="seoanalyser.com.au"] {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          /* Remove any CORS restrictions for print */
          image-rendering: auto !important;
          /* Fallback for failed images */
          content: "" !important;
        }
        /* Ensure valid images are shown */
        img[src*="http"]:not([src*="cdn.simpleicons.org"]):not([src*="external"]) {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          /* Handle potential CORS issues */
          image-rendering: auto !important;
        }
        /* Hide broken image icons */
        img[alt]:after {
          display: none !important;
        }
        .print-button-bar {
          display: none !important;
        }
        .hidden {
          display: block !important;
          visibility: visible !important;
        }
        .animate-spin {
          display: none !important;
        }
        /* Ensure PDF container is visible */
        .pdf-container {
          display: block !important;
          visibility: visible !important;
        }
      }
    `,
  });

  // Enhanced print handler with CORS-aware image handling
  const reactToPrintFn = useCallback(async () => {
    console.log("Print requested, handling CORS images...");

    // Set loading state
    updateState({ isPdfReady: false });

    try {
      // Attempt to preload images (may fail due to CORS)
      console.log("Attempting image preload...");
      const imagesLoaded = await preloadImages();
      console.log(`Image preload result: ${imagesLoaded}`);

      // Shorter wait time since CORS images may not preload successfully
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Update state to ready
      updateState({ isPdfReady: true });

      // Brief wait for state update
      await new Promise((resolve) => setTimeout(resolve, 500));

      console.log(
        "Starting print (CORS images will be handled by onBeforePrint)..."
      );
      handlePrint();
    } catch (error) {
      console.error("Error during print preparation:", error);

      // Graceful fallback: proceed with print anyway
      console.log("Proceeding with print despite image loading issues...");
      updateState({ isPdfReady: true });

      setTimeout(() => {
        handlePrint();
      }, 1000);
    }
  }, [handlePrint, preloadImages, updateState]);

  // Helper function to fix URLs to use HTTPS and avoid CORS issues
  const fixImageUrl = (url: string | null): string | null => {
    if (!url || url.trim() === "") return null;

    try {
      // Validate URL format
      const urlObj = new URL(url);
      // Convert HTTP to HTTPS to avoid CORS issues
      if (urlObj.protocol === "http:") {
        urlObj.protocol = "https:";
      }
      return urlObj.toString();
    } catch (error) {
      console.error("Invalid URL:", url, error);
      return null;
    }
  };

  // Extract data for PDF
  const result = data?.result || {};
  const urlName = result.url || "";
  const shouldShowPdf =
    !loading && !error && !hasProfileNoSubscription && whiteLabelInfo;

  // Debug URLs
  const screenshotUrl = fixImageUrl(result.desktop_screenshot_url);
  const brandPhotoUrl = fixImageUrl(whiteLabelInfo?.brand_photo ?? null);

  return (
    <>
      {/* Always render PDF component but control visibility */}
      <div
        ref={contentRef}
        className={`pdf-container pt-4 w-full mx-auto transform origin-top-center md:scale-[1] ${
          shouldShowPdf ? "" : "hidden"
        }`}
      >
        <SeoAnalyzerPdf
          urlName={urlName}
          screenshotUrl={screenshotUrl}
          onPageSeoData={result.onpage_analysis || {}}
          usabilityData={result.usability_analysis || {}}
          technologyData={result.technology_review_analysis || {}}
          socialData={result.social_analysis || {}}
          performanceData={result.performance_analysis || {}}
          linksData={result.links_analysis || {}}
          pagespeedData={result.pagespeed_analysis || {}}
          pagespeedMobileData={result.pagespeed_mobile_analysis || {}}
          localSeoData={result.localseo_analysis || {}}
          childPagesData={result.child_pages || []}
          brand_name={whiteLabelInfo?.brand_name}
          brand_website={whiteLabelInfo?.brand_website}
          brand_photo={brandPhotoUrl}
        />
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex flex-col items-center justify-center min-h-screen py-20">
          {hasProfileNoSubscription ? (
            <SubscriptionRequiredMessage />
          ) : (
            <LoadingMessage brandName={whiteLabelInfo?.brand_name} />
          )}
        </div>
      )}

      {/* Loading white label data state */}
      {!loading && !error && !hasProfileNoSubscription && !whiteLabelInfo && (
        <div className="flex flex-col items-center justify-center min-h-screen py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 font-medium">
              Loading white label settings...
            </p>
          </div>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="flex flex-col items-center justify-center min-h-screen py-20">
          <ErrorMessage
            error={error}
            hasProfileNoSubscription={hasProfileNoSubscription}
            brandName={whiteLabelInfo?.brand_name}
            showAuthModal={showAuthModal}
          />
        </div>
      )}

      {/* Payment error state */}
      {paymentError && !error && (
        <PaymentErrorSection
          paymentError={paymentError}
          hasProfileNoSubscription={hasProfileNoSubscription}
          showAuthModal={showAuthModal}
          isPdfReady={isPdfReady}
          reactToPrintFn={reactToPrintFn}
        />
      )}

      {/* Success state */}
      {!loading && !error && !paymentError && (
        <SuccessSection
          hasProfileNoSubscription={hasProfileNoSubscription}
          paymentStatus={paymentStatus}
          whiteLabelInfo={whiteLabelInfo}
          paymentId={paymentId}
          isPdfReady={isPdfReady}
          reactToPrintFn={reactToPrintFn}
        />
      )}

      {/* Subscription Required Modal */}
      {showSubscriptionModal && (
        <SubscriptionModal
          onGoHome={handleGoHome}
          onGoPricing={handleGoPricing}
          onClose={handleCloseSubscriptionModal}
        />
      )}
    </>
  );
}

// Component for subscription required message
const SubscriptionRequiredMessage = () => (
  <div className="bg-yellow-50 p-6 rounded-lg max-w-md text-center">
    <h2 className="text-xl font-bold text-yellow-600 mb-2">
      Pro Plan Subscription Required
    </h2>
    <p className="text-yellow-600">
      You need a Pro Plan subscription to access this feature.
    </p>
    <p className="mt-4 text-gray-600">
      Please purchase a subscription to continue.
    </p>
    <div className="mt-6 flex flex-col gap-3">
      <button
        onClick={() => (window.location.href = "/")}
        className="px-6 py-3 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors font-semibold"
      >
        Go to Home Page
      </button>
      <button
        onClick={() => (window.location.href = "/pricing")}
        className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-semibold"
      >
        View Subscription Plans
      </button>
    </div>
  </div>
);

// Component for loading message
const LoadingMessage = ({ brandName }: { brandName?: string }) => (
  <>
    <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
    <p className="mt-4 text-gray-600">
      Loading {brandName || "SEO"} analysis data...
    </p>
  </>
);

// Component for error message
const ErrorMessage = ({
  error,
  hasProfileNoSubscription,
  brandName,
  showAuthModal,
}: {
  error: string;
  hasProfileNoSubscription: boolean;
  brandName?: string;
  showAuthModal: boolean;
}) => (
  <div
    className={`p-6 rounded-lg max-w-md text-center ${
      hasProfileNoSubscription ? "bg-yellow-50" : "bg-red-50"
    }`}
  >
    <h2
      className={`text-xl font-bold mb-2 ${
        hasProfileNoSubscription ? "text-yellow-700" : "text-red-700"
      }`}
    >
      {hasProfileNoSubscription ? "Subscription Required" : "Error"}
    </h2>
    <p
      className={hasProfileNoSubscription ? "text-yellow-600" : "text-red-600"}
    >
      {error}
    </p>
    {hasProfileNoSubscription ? (
      <SubscriptionRequiredMessage />
    ) : (
      <>
        <p className="mt-4 text-gray-600">
          Please check the URL and try again. If the problem persists, contact{" "}
          {brandName ? `${brandName} support` : "support"}.
        </p>
        {showAuthModal && (
          <p className="mt-4 text-primary font-medium">
            Please log in to continue.
          </p>
        )}
      </>
    )}
  </div>
);

// Component for payment error section
const PaymentErrorSection = ({
  paymentError,
  hasProfileNoSubscription,
  showAuthModal,
  isPdfReady,
  reactToPrintFn,
}: {
  paymentError: string;
  hasProfileNoSubscription: boolean;
  showAuthModal: boolean;
  isPdfReady: boolean;
  reactToPrintFn: () => void;
}) => (
  <div className="flex flex-col min-h-screen">
    {hasProfileNoSubscription ? (
      <div className="bg-yellow-50 p-6 rounded-lg max-w-md text-center mx-auto mt-8">
        <SubscriptionRequiredMessage />
      </div>
    ) : (
      <div className="bg-yellow-50 p-4 border-b border-yellow-200">
        <div className="container mx-auto">
          <p className="text-yellow-700">
            <strong>Payment Verification Warning:</strong> {paymentError}
          </p>
          <p className="text-yellow-600 text-sm mt-1">
            Your SEO analysis is displayed below, but white label customization
            could not be applied.
          </p>
          {showAuthModal && (
            <p className="text-primary text-sm mt-1 font-medium">
              Please log in to view white label customization.
            </p>
          )}
        </div>
      </div>
    )}
    {!hasProfileNoSubscription && (
      <PrintButton isPdfReady={isPdfReady} reactToPrintFn={reactToPrintFn} />
    )}
  </div>
);

// Component for success section
const SuccessSection = ({
  hasProfileNoSubscription,
  paymentStatus,
  whiteLabelInfo,
  paymentId,
  isPdfReady,
  reactToPrintFn,
}: {
  hasProfileNoSubscription: boolean;
  paymentStatus: PaymentStatusResponse | null;
  whiteLabelInfo: WhiteLabelInfo | null;
  paymentId: string | null;
  isPdfReady: boolean;
  reactToPrintFn: () => void;
}) => (
  <div className="flex flex-col min-h-screen">
    {hasProfileNoSubscription ? (
      <div className="bg-yellow-50 p-6 rounded-lg max-w-md text-center mx-auto mt-8">
        <SubscriptionRequiredMessage />
      </div>
    ) : (
      <>
        {(paymentStatus || (whiteLabelInfo && !paymentId)) && (
          <div className="bg-green-50 p-4 border-b border-green-200 mb-6">
            <div className="container mx-auto">
              {paymentStatus ? (
                <>
                  <p className="text-green-700">
                    <strong>Payment Status:</strong>{" "}
                    {paymentStatus.payment_status || paymentStatus.status}
                  </p>
                  {paymentStatus.plan && paymentStatus.plan_period && (
                    <p className="text-green-600 text-sm mt-1">
                      Plan: {paymentStatus.plan} ({paymentStatus.plan_period})
                      {paymentStatus.expire_date &&
                        ` - Expires: ${new Date(
                          paymentStatus.expire_date
                        ).toLocaleDateString()}`}
                    </p>
                  )}
                </>
              ) : (
                <>
                  <p className="text-green-700">
                    <strong>Active Subscription:</strong> Pro Plan
                  </p>
                  <p className="text-green-600 text-sm mt-1">
                    Using Pro Plan settings for:{" "}
                    {whiteLabelInfo?.brand_name ||
                      whiteLabelInfo?.brand_website ||
                      "Your brand"}
                  </p>
                </>
              )}
            </div>
          </div>
        )}
      </>
    )}
    {!hasProfileNoSubscription && (
      <PrintButton isPdfReady={isPdfReady} reactToPrintFn={reactToPrintFn} />
    )}
  </div>
);

// Component for print button
const PrintButton = ({
  isPdfReady,
  reactToPrintFn,
}: {
  isPdfReady: boolean;
  reactToPrintFn: () => void;
}) => (
  <div className="print-button-bar fixed bottom-0 left-0 right-0 py-4 px-6 bg-white shadow-md border-t border-gray-200 flex justify-between z-10">
    <div className="text-gray-600">
      {/* Status text can be added here if needed */}
    </div>
    <div>
      <button
        onClick={reactToPrintFn}
        disabled={!isPdfReady}
        className={`px-6 py-3 font-bold ${
          isPdfReady
            ? "bg-primary hover:bg-primary/90"
            : "bg-gray-400 cursor-not-allowed"
        } text-white rounded-lg transition-colors flex items-center gap-2 font-semibold`}
      >
        <DownloadIcon className="w-5 h-5" />
        <span>
          {isPdfReady
            ? "Download SEO Analysis PDF"
            : "Preparing SEO Analysis PDF..."}
        </span>
      </button>
    </div>
  </div>
);

// Component for subscription modal
const SubscriptionModal = ({
  onGoHome,
  onGoPricing,
  onClose,
}: {
  onGoHome: () => void;
  onGoPricing: () => void;
  onClose: () => void;
}) => (
  <div
    className="fixed inset-0 bg-gray-200/40 backdrop-blur-lg flex items-center justify-center z-50"
    onClick={onClose}
  >
    <div
      className="bg-white rounded-lg p-8 max-w-md w-full mx-4 relative"
      onClick={(e) => e.stopPropagation()}
    >
      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        aria-label="Close modal"
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      {/* Modal content */}
      <div className="text-center">
        <div className="mb-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
            <svg
              className="h-6 w-6 text-yellow-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>

        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Pro Plan Subscription Required
        </h3>

        <p className="text-gray-600 mb-6">
          You don't have a Pro Plan subscription. To access this white label PDF
          feature, please upgrade your plan.
        </p>

        <div className="flex flex-col gap-3">
          <button
            onClick={onGoPricing}
            className="w-full px-6 py-3 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors font-semibold"
          >
            View Pricing Plans
          </button>
          <button
            onClick={onGoHome}
            className="w-full px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors font-semibold"
          >
            Go to Home Page
          </button>
        </div>
      </div>
    </div>
  </div>
);
