"use client";

import { useQuery, UseQueryOptions } from "@tanstack/react-query";
import { useAnalyticsConnection } from "@/contexts/AnalyticsConnectionContext";
import { useProjectId } from "@/hooks/useProjectId";

interface UseAnalyticsAPIOptions<T> extends Omit<UseQueryOptions<T>, 'queryKey' | 'queryFn' | 'enabled'> {
  // Service requirement
  requiresGA?: boolean;
  requiresGSC?: boolean;
  
  // Custom enabled condition (in addition to connection checks)
  enabled?: boolean;
  
  // Query key parts (projectId will be automatically included)
  queryKeyParts: (string | number | boolean | null | undefined)[];
  
  // API function to call
  queryFn: () => Promise<T>;
}

/**
 * Enhanced hook for making API calls that require Google Analytics or Search Console connections
 * Automatically handles connection status checking and provides consistent error handling
 */
export const useAnalyticsAPI = <T>({
  requiresGA = false,
  requiresGSC = false,
  enabled = true,
  queryKeyParts,
  queryFn,
  ...queryOptions
}: UseAnalyticsAPIOptions<T>) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { 
    isGoogleAnalyticsReady, 
    isGoogleSearchConsoleReady,
    isGoogleAnalyticsConnected,
    isGoogleSearchConsoleConnected 
  } = useAnalyticsConnection();

  // Build the complete query key
  const queryKey = ["analytics-api", projectId, ...queryKeyParts];

  // Determine if the query should be enabled
  const shouldEnable = 
    enabled && 
    isValidProjectId && 
    !!projectId &&
    (!requiresGA || isGoogleAnalyticsReady) &&
    (!requiresGSC || isGoogleSearchConsoleReady);

  const query = useQuery({
    queryKey,
    queryFn: async () => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Double-check connection status before making API call
      if (requiresGA && !isGoogleAnalyticsConnected) {
        throw new Error("Google Analytics connection is required for this data");
      }

      if (requiresGSC && !isGoogleSearchConsoleConnected) {
        throw new Error("Google Search Console connection is required for this data");
      }

      return queryFn();
    },
    enabled: shouldEnable,
    ...queryOptions,
  });

  return {
    ...query,
    // Additional helper properties
    isConnectionReady: shouldEnable,
    connectionStatus: {
      gaConnected: isGoogleAnalyticsConnected,
      gscConnected: isGoogleSearchConsoleConnected,
      gaReady: isGoogleAnalyticsReady,
      gscReady: isGoogleSearchConsoleReady,
    },
  };
};

/**
 * Simplified hook for Google Analytics API calls
 */
export const useGoogleAnalyticsAPI = <T>(
  queryKeyParts: (string | number | boolean | null | undefined)[],
  queryFn: () => Promise<T>,
  options?: Omit<UseAnalyticsAPIOptions<T>, 'requiresGA' | 'queryKeyParts' | 'queryFn'>
) => {
  return useAnalyticsAPI({
    requiresGA: true,
    queryKeyParts,
    queryFn,
    ...options,
  });
};

/**
 * Simplified hook for Google Search Console API calls
 */
export const useGoogleSearchConsoleAPI = <T>(
  queryKeyParts: (string | number | boolean | null | undefined)[],
  queryFn: () => Promise<T>,
  options?: Omit<UseAnalyticsAPIOptions<T>, 'requiresGSC' | 'queryKeyParts' | 'queryFn'>
) => {
  return useAnalyticsAPI({
    requiresGSC: true,
    queryKeyParts,
    queryFn,
    ...options,
  });
};

/**
 * Hook for API calls that require both Google Analytics and Search Console
 */
export const useBothServicesAPI = <T>(
  queryKeyParts: (string | number | boolean | null | undefined)[],
  queryFn: () => Promise<T>,
  options?: Omit<UseAnalyticsAPIOptions<T>, 'requiresGA' | 'requiresGSC' | 'queryKeyParts' | 'queryFn'>
) => {
  return useAnalyticsAPI({
    requiresGA: true,
    requiresGSC: true,
    queryKeyParts,
    queryFn,
    ...options,
  });
};
