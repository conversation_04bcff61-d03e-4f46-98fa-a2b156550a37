"use client";
import { CheckIcon, InfoIcon } from "@/ui/icons/general";
import PricingOptions from "./PricingOptions";
import Tooltip from "@/ui/Tooltip";

// Feature data with tooltips
const pricingsData = [
  // Feature Breakdown section
  {
    label: "Feature Breakdown",
    features: [],
    isTitle: true,
  },
  {
    label: "SEO Audits",
    tooltip:
      "Quick on-page and site-wide audits (sitemaps, robots.txt, backlinks, etc.) delivered in under 20 seconds — perfect for lead generation and client prospecting.",
    features: [true],
  },
  {
    label: "Google, Bing & Mobile Tracking",
    tooltip:
      "Track rankings across Google, Bing, and mobile search results to provide comprehensive visibility into search performance.",
    features: [true],
  },
  {
    label: "See Site's Keywords and Volumes",
    tooltip:
      "Identify which keywords a site is ranking for and their search volumes to understand traffic potential and optimisation opportunities.",
    features: [true],
  },
  {
    label: "Research by URL",
    tooltip:
      "Analyze any URL to uncover its SEO strengths, weaknesses, and opportunities for improvement.",
    features: [true],
  },
  {
    label: "Site Backlink Lookups",
    tooltip:
      "Discover who's linking to a site and evaluate the quality of those backlinks to improve link building strategies.",
    features: [true],
  },
  {
    label: "Task Recommendations",
    tooltip:
      "Get actionable recommendations for improving SEO performance based on audit findings.",
    features: [true],
  },
  // White Label PDF Reporting section
  {
    label: "White Label PDF Reporting",
    features: [],
    isTitle: true,
  },
  {
    label: "PDF Report Downloads",
    tooltip:
      "Download comprehensive SEO reports in PDF format that can be shared with clients or team members.",
      features: [],
      isTitle: true,
  },
  {
    label: "Custom Branding, Logo & Company Details",
    tooltip:
      "Add your own branding, logo, and company information to reports to maintain consistent branding and professional presentation.",
      features: [],
      isTitle: true,
  },
  {
    label: "Branded Shareable Links",
    tooltip:
      "Generate and share live, custom-branded audit links with clients or team members.",
      features: [],
      isTitle: true,
  },
  {
    label: "Unlimited Analyses",
    tooltip:
      "Run as many SEO audits and checks as you need, no daily or monthly scan limits.",
      features: [],
      isTitle: true,
  },
  
];

export default function FeaturesPlan() {
  return (
    <div>
      <div className="mt-8 container flex flex-col gap-8 lg:hidden">
        <div>
          <PricingOptions.Title title="Pro Plan" variant="blue" />
          <div className="py-2.5 my-4">
            <div className="text-2xl font-black text-secondary">
              Feature Breakdown
            </div>
          </div>
          <div className="flex flex-col gap-2 ">
            {pricingsData.map((pricing, index) => {
              if (pricing.isTitle) {
                return (
                  <PricingOptions.Label key={index} label={pricing.label} />
                );
              } else {
                return (
                  <div key={index}>
                    <PricingOptions.Description
                      text={
                        <div className="flex items-center justify-center">
                          {pricing.label}
                          {pricing.tooltip && (
                            <Tooltip content={pricing.tooltip} width="lg">
                              <InfoIcon className="w-6 h-6 ml-2 text-primary" />
                            </Tooltip>
                          )}
                        </div>
                      }
                    />
                    <PricingOptions.Description
                      text={<CheckIcon className="w-6 h-6 mx-auto" />}
                    />
                  </div>
                );
              }
            })}
            {/* <PricingOptions.Description
              text={<PricingOptions.Button variant="blue" title="Free Trial" />}
            /> */}
          </div>
        </div>
      </div>

      <div className="hidden lg:flex flex-col gap-4 container mt-[84px]">
        <div className="flex items-center gap-6">
          <div className="w-[427px]">
            <div className="py-2.5">
              <div className="text-[32px] font-black text-secondary">
                Feature Breakdown
              </div>
            </div>
          </div>
          <div className="w-[789px]">
            <PricingOptions.Title title="Pro Plan" variant="blue" />
          </div>
        </div>
        {pricingsData.map((pricing, index) => (
          <div key={index} className="flex gap-6">
            <div className="w-[427px]">
              {pricing.isTitle ? (
                <PricingOptions.Label label={pricing.label} />
              ) : (
                <PricingOptions.Description
                  text={
                    <div className="flex items-center">
                      {pricing.label}
                      {pricing.tooltip && (
                        <Tooltip content={pricing.tooltip} width="lg">
                          <InfoIcon className="w-6 h-6 ml-2 text-primary" />
                        </Tooltip>
                      )}
                    </div>
                  }
                  className="min-h-full"
                />
              )}
            </div>
            <div className="w-[789px]">
              <PricingOptions.Description
                text={<CheckIcon className="w-6 h-6 mx-auto my-auto" />}
              />
            </div>
          </div>
        ))}
        <div className="flex items-center gap-6">
          <div className="w-[427px]"></div>
          <div className="w-[789px]">
            {/* <PricingOptions.Description
              text={<PricingOptions.Button variant="blue" title="Free Trial" />}
            /> */}
          </div>
        </div>
      </div>
    </div>
  );
}
