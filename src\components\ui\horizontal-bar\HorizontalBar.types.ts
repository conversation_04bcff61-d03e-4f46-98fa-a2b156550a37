export type TBar = {
  value: number;
  color: string;
};

type isLoading = {
  label?: string;
  totalValue?: number;
  bars?: TBar[];
  className?: string;
  percentageLabel?: string;
  isLoading: true;
};

type isLoaded = {
  label: string;
  totalValue: number;
  bars: TBar[];
  className?: string;
  percentageLabel?: string;
  isLoading?: false;
};

export type THorizontalBar = isLoaded | isLoading;
