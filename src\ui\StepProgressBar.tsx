"use client";
import React, { ReactNode } from "react";
import {
  UserIcon,
  VerifyIcon,
  TagIcon,
  DownloadIcon,
  CrownIcon,
  CheckIcon,
} from "@/ui/icons/general";
import StripeIcon from "@/ui/icons/payment/StripeIcon";

type Step = {
  id: string;
  label: string;
  icon?: ReactNode;
};

type StepProgressBarProps = {
  steps: Step[];
  currentStepId: string;
  className?: string;
  onStepClick?: (stepId: string) => void;
  clickableSteps?: string[]; // Array of step IDs that are clickable
};

export default function StepProgressBar({
  steps,
  currentStepId,
  className = "",
  onStepClick,
  clickableSteps = [],
}: StepProgressBarProps) {
  // Function to get the appropriate icon based on step label
  const getStepIcon = (label: string) => {
    switch (label.toLowerCase()) {
      case "register":
      case "login":
      case "authentication":
        return <UserIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "verify":
        return <VerifyIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "white label":
      case "white label setting":
        return (
          <svg
            className="w-4 h-4 md:w-5 md:h-5"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5.79416 11.8754L10.0666 7.60292M8.1246 14.2058L10.0666 12.2638M9.99983 1.45521L1.45488 10.0002C1.16362 10.2915 1 10.6866 1 11.0986C1 11.5105 1.16362 11.9056 1.45488 12.197L7.803 18.5451C8.09435 18.8364 8.48945 19 8.90142 19C9.31338 19 9.70849 18.8364 9.99983 18.5451L18.5448 10.0002C18.8362 9.70887 18.9999 9.31376 19 8.90175V2.55363C19 2.14158 18.8363 1.74641 18.545 1.45505C18.2536 1.16369 17.8584 1 17.4464 1H11.0982C10.6862 1.00009 10.2911 1.16383 9.99983 1.45521ZM16.4845 4.42239C16.4845 4.66109 16.3897 4.89001 16.2209 5.05879C16.0521 5.22757 15.8232 5.32239 15.5845 5.32239C15.3458 5.32239 15.1169 5.22757 14.9481 5.05879C14.7793 4.89001 14.6845 4.66109 14.6845 4.42239C14.6845 4.1837 14.7793 3.95478 14.9481 3.786C15.1169 3.61721 15.3458 3.52239 15.5845 3.52239C15.8232 3.52239 16.0521 3.61721 16.2209 3.786C16.3897 3.95478 16.4845 4.1837 16.4845 4.42239Z"
              stroke="currentColor"
              strokeWidth="1.2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "select plan":
      case "billing":
        return (
          <svg
            className="w-4 h-4 md:w-5 md:h-5"
            viewBox="0 0 18 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.66667 9.1H13.4444M4.55556 9.1H5M4.55556 5.95H5M4.55556 12.25H5M7.66667 5.95H13.4444M7.66667 12.25H13.4444M13.2267 1H4.77335C3.74324 1 3.22819 1 2.81279 1.14635C2.02508 1.42386 1.40665 2.06847 1.1404 2.88951C1 3.32249 1 3.85934 1 4.93305V17.5368C1 18.3091 1.87556 18.719 2.42942 18.2058C2.75482 17.9043 3.24518 17.9043 3.57058 18.2058L4 18.6037C4.5703 19.1321 5.4297 19.1321 6 18.6037C6.5703 18.0753 7.42969 18.0753 8 18.6037C8.57031 19.1321 9.42969 19.1321 10 18.6037C10.5703 18.0753 11.4297 18.0753 12 18.6037C12.5703 19.1321 13.4297 19.1321 14 18.6037L14.4294 18.2058C14.7548 17.9043 15.2452 17.9043 15.5706 18.2058C16.1244 18.719 17 18.3091 17 17.5368V4.93305C17 3.85934 17 3.32249 16.8596 2.88951C16.5933 2.06847 15.9749 1.42386 15.1872 1.14635C14.7718 1 14.2568 1 13.2267 1Z"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        );
      case "payment":
        return <StripeIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "download":
      case "generate pdf":
        return <DownloadIcon className="w-4 h-4 md:w-5 md:h-5" />;
      case "share":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-4 h-4 md:w-5 md:h-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        );
      default:
        return null;
    }
  };

  // Find the current step index
  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);

  return (
    <div className={`w-full ${className}`}>
      {/* Main container with fixed height to prevent text from affecting alignment */}
      <div className="relative">
        {/* Steps row with fixed height for the circles */}
        <div className="flex items-center justify-between h-[36px] sm:h-[40px] md:h-[44px] relative">
          {/* Steps circles */}
          {steps.map((step, index) => {
            const isActive = step.id === currentStepId;
            const isPast = index < currentStepIndex;
            const isLast = index === steps.length - 1;

            return (
              <React.Fragment key={step.id}>
                {/* Step circle with icon */}
                <div
                  className={`absolute top-0 z-10`}
                  style={{
                    left: `${(index / (steps.length - 1)) * 100}%`,
                    transform: "translateX(-50%)",
                    // First and last items need special positioning
                    ...(index === 0
                      ? { left: "0", transform: "translateX(0)" }
                      : {}),
                    ...(index === steps.length - 1
                      ? { left: "100%", transform: "translateX(-100%)" }
                      : {}),
                  }}
                >
                  <div
                    className={`flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full text-xs md:text-sm font-bold transition-all duration-300 ${
                      isActive
                        ? "bg-primary text-white shadow-md shadow-primary/30"
                        : isPast
                        ? "bg-white text-primary border-2 border-primary"
                        : "bg-white text-gray-400 border-2 border-gray-300"
                    } ${
                      clickableSteps.includes(step.id)
                        ? "cursor-pointer hover:opacity-80"
                        : ""
                    }`}
                    onClick={() => {
                      if (clickableSteps.includes(step.id) && onStepClick) {
                        onStepClick(step.id);
                      }
                    }}
                  >
                    {isPast ? (
                      <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-primary" />
                    ) : (
                      step.icon || getStepIcon(step.label)
                    )}
                  </div>
                </div>

                {/* Connector line between steps */}
                {!isLast && (
                  <div
                    className={`absolute h-[2px] z-0 ${
                      isPast
                        ? "bg-primary"
                        : "border-t-2 border-dashed border-gray-300"
                    }`}
                    style={{
                      top: "17px", // Center of the circle adjusted for mobile
                      left: `${(index / (steps.length - 1)) * 100}%`,
                      right: `${
                        100 - ((index + 1) / (steps.length - 1)) * 100
                      }%`,
                      // Adjust for the first and last items
                      ...(index === 0
                        ? {
                            left: "14px", // Half the circle width adjusted for mobile
                          }
                        : {}),
                      ...(index === steps.length - 2
                        ? {
                            right: "14px", // Half the circle width adjusted for mobile
                          }
                        : {}),
                    }}
                  />
                )}
              </React.Fragment>
            );
          })}
        </div>

        {/* Labels row - positioned to align with circles */}
        <div className="relative w-full mt-1 sm:mt-2 md:mt-3 h-[30px] md:h-[24px]">
          {steps.map((step, index) => {
            const isActive = step.id === currentStepId;
            const isPast = index < currentStepIndex;

            return (
              <div
                key={`label-${step.id}`}
                className="absolute text-center"
                style={{
                  left: `${(index / (steps.length - 1)) * 100}%`,
                  transform: "translateX(-50%)",
                  width: steps.length > 4 ? "50px" : "60px", // Narrower for more steps
                  // First and last items need special positioning
                  ...(index === 0
                    ? {
                        left: "0",
                        transform: "translateX(0)",
                        textAlign: "left",
                      }
                    : {}),
                  ...(index === steps.length - 1
                    ? {
                        left: "100%",
                        transform: "translateX(-100%)",
                        textAlign: "right",
                      }
                    : {}),
                }}
              >
                <span
                  className={`text-[9px] sm:text-[10px] ml-1 md:text-xs inline-block font-medium transition-all duration-300 text-center ${
                    isActive
                      ? "text-primary font-bold"
                      : isPast
                      ? "text-primary"
                      : "text-gray-500"
                  } ${
                    clickableSteps.includes(step.id)
                      ? "cursor-pointer hover:underline"
                      : ""
                  }`}
                  onClick={() => {
                    if (clickableSteps.includes(step.id) && onStepClick) {
                      onStepClick(step.id);
                    }
                  }}
                >
                  {step.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
