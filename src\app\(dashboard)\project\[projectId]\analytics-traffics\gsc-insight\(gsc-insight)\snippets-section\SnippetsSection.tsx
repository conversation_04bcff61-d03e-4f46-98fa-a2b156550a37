import React, { useEffect, useRef, useState } from "react";
import Timeline from "./_components/Timeline";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../_components/date-range/DateRange";
import Dropdown from "@/components/ui/Dropdown";
import Flag from "react-world-flags";
import googleLogo from "@/../public/images/create-project/google.svg";
import Image from "next/image";
import SearchInput from "./_components/SearchInput";
import { cn } from "@/utils/cn";
import { motion } from "framer-motion";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

const CardSection = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("border-2 border-md w-full rounded-2xl p-4", className)}>
      {children}
    </div>
  );
};

const KeywordItems = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "border-2 p-4 rounded-md border-[#F4F4F4] flex items-center justify-between text-sm",
        className,
      )}
    >
      {children}
    </div>
  );
};

const keywordItems = [
  {
    keyword: "Hotel welcoming",
    Volume: "10",
  },
  {
    keyword: "Hotel welcoming",
    Volume: "10",
  },
  {
    keyword: "Hotel welcoming",
    Volume: "10",
  },
  {
    keyword: "Hotel welcoming",
    Volume: "10",
  },
];

const tabButtons = [
  "Average position",
  "Traffic forecast",
  "Search visibility",
];

const badges = ["Last week", "Month", "3 Month", "6 Month"];

const SnippetsSection = () => {
  const tabButtonsRefs = useRef<HTMLButtonElement[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  const [tabRef, setTabRef] = useState<HTMLButtonElement | null>(null);
  const [activeBadge, setActiveBadge] = useState(0);
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const handleTabChange = (index: number) => {
    setActiveTab(index);
    setTabRef(tabButtonsRefs.current[index]);
  };

  useEffect(() => {
    handleTabChange(0);
  }, []);
  /* ========================================================================== */
  const [selectedPeriod, setSelectedPeriod] = useState("6 Month");
  const generateDates = (period: string): Date[] => {
    const today = new Date();
    const dates: Date[] = [];

    switch (period) {
      case "Last week":
        for (let i = 6; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(today.getDate() - i);
          dates.push(date);
        }
        break;
      case "Month":
        for (let i = 29; i >= 0; i -= 6) {
          const date = new Date(today);
          date.setDate(today.getDate() - i);
          dates.push(date);
        }
        break;
      case "3 Month":
        for (let i = 90; i >= 0; i -= 15) {
          const date = new Date(today);
          date.setDate(today.getDate() - i);
          dates.push(date);
        }
        break;
      case "6 Month":
        for (let i = 180; i >= 0; i -= 30) {
          const date = new Date(today);
          date.setDate(today.getDate() - i);
          dates.push(date);
        }
        break;
      default:
        return dates;
    }

    return dates;
  };
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="space-y-6 rounded-3xl">
      <div className="space-y-2">
        <div className="flex flex-row justify-between items-center">
          <Title>Snippets</Title>
          <Dropdown className="bg-white">
            <Dropdown.Button className="bg-white border">
              <Image
                src={googleLogo}
                alt="google"
                className="border rounded-sm p-1 w-6 h-6"
              />
              <Flag className="w-5 rounded-xs" code={"FR"} />
              <span>France</span>
            </Dropdown.Button>
            <Dropdown.Options className="border border-t-0">
              <Dropdown.Option className="bg-white">Option 1</Dropdown.Option>
            </Dropdown.Options>
          </Dropdown>
        </div>
        <DateRange />
      </div>
      <div className="flex w-full gap-3">
        <CardSection className="space-y-4">
          <SearchInput />
          <div className="flex justify-between text-secondary text-xs font-semibold">
            <span>KEYWORDS</span>
            <span>SEARCH VOLUME</span>
          </div>
          <div className="space-y-2">
            {keywordItems.map((item, index) => (
              <KeywordItems
                key={index}
                className={`${
                  index % 2 === 0 && "bg-[#F4F4F4]"
                } text-secondary`}
              >
                <span>{item.keyword}</span>
                <span>{item.Volume}</span>
              </KeywordItems>
            ))}
          </div>
        </CardSection>
        <CardSection className="">
          <div className="relative space-y-4">
            <div className="flex justify-between overflow-x-auto">
              {tabButtons.map((item, index) => (
                <button
                  ref={(el) => {
                    tabButtonsRefs.current[index] = el!;
                  }}
                  key={index}
                  className={cn(
                    `py-2 px-4 text-xs rounded-md text-secondary shrink-0`,
                  )}
                  style={
                    index === activeTab
                      ? { color: themeColor }
                      : { color: "var(--color-secondary)" }
                  }
                  onClick={() => handleTabChange(index)}
                >
                  {item}
                </button>
              ))}
            </div>
            <motion.div
              className="absolute z-0 rounded-md bg-primary/10"
              style={{
                backgroundColor: themeColor + "10",
              }}
              initial={false}
              animate={{
                top: tabRef?.offsetTop ?? 0,
                left: tabRef?.offsetLeft ?? 0,
                width: tabRef?.offsetWidth ?? 0,
                height: tabRef?.offsetHeight ?? 0,
                opacity: 1,
              }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
              }}
            />
            <div className="flex flex-wrap gap-2">
              {badges.map((item, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setActiveBadge(index);
                    setSelectedPeriod(item);
                  }}
                  className={cn(
                    "py-2 px-3 text-xs rounded-md text-secondary border transition-colors duration-300",
                  )}
                  style={
                    index === activeBadge
                      ? { borderColor: themeColor }
                      : { borderColor: "transparent" }
                  }
                >
                  {item}
                </button>
              ))}
            </div>
          </div>
          <div className="w-full h-[36vh] flex items-end justify-center">
            <Timeline dates={generateDates(selectedPeriod)} />
          </div>
        </CardSection>
      </div>
    </Card>
  );
};

export default SnippetsSection;
