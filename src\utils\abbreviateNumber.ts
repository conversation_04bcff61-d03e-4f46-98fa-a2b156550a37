/**
 * Converts a number to a shortened string format with suffixes:
 * - B for billions
 * - M for millions
 * - K for thousands
 *
 * Examples:
 *   abbreviateNumber(1500)       // "1.5K"
 *   abbreviateNumber(2000000)    // "2M"
 *   abbreviateNumber(3500000000) // "3.5B"
 *   abbreviateNumber(999)        // "999"
 *
 * Trailing `.0` decimals are removed for cleaner output, e.g. `2.0M` → `2M`.
 *
 * @param num - The number to abbreviate
 * @returns A string representing the abbreviated number
 */
export default function abbreviateNumber(num: number): string {
  if (num >= 1_000_000_000)
    return (num / 1_000_000_000).toFixed(1).replace(/\.0$/, "") + "B";
  if (num >= 1_000_000)
    return (num / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
  if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, "") + "K";
  return num.toString();
}
