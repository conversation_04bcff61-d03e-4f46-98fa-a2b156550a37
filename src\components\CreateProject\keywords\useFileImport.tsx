"use client";

import { useState } from "react";
import * as ExcelJS from "exceljs";

interface UseFileImportProps {
  selectedConfigIds: string[];
  addKeyword: (keyword: string, configIds: string[]) => void;
  setError: (error: string) => void;
}

export const useFileImport = ({
  selectedConfigIds,
  addKeyword,
  setError,
}: UseFileImportProps) => {
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    try {
      if (fileExtension === "xlsx" || fileExtension === "xls") {
        // Handle Excel files
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            const buffer = e.target?.result as ArrayBuffer;
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.load(buffer);

            const worksheet = workbook.worksheets[0];
            if (!worksheet) {
              setError("No worksheet found in the Excel file");
              return;
            }

            const importedKeywords: string[] = [];
            const headerRow = worksheet.getRow(1);
            let nameColumnIndex = -1;

            headerRow.eachCell((cell, colNumber) => {
              if (
                cell.value &&
                cell.value.toString().toLowerCase() === "name"
              ) {
                nameColumnIndex = colNumber;
              }
            });

            if (nameColumnIndex > 0) {
              worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) {
                  const cell = row.getCell(nameColumnIndex);
                  const value = cell.value;
                  if (
                    value &&
                    typeof value === "string" &&
                    value.trim().length > 0
                  ) {
                    importedKeywords.push(value.trim());
                  }
                }
              });
            } else {
              worksheet.eachRow((row) => {
                const cell = row.getCell(1);
                const value = cell.value;
                if (
                  value &&
                  typeof value === "string" &&
                  value.trim().length > 0
                ) {
                  importedKeywords.push(value.trim());
                }
              });
            }

            addKeywordsToTable(importedKeywords);
          } catch (err) {
            setError(
              "Error processing Excel file. Please check the file format."
            );
            console.error(err);
          }
        };

        reader.onerror = () => {
          setError("Failed to read Excel file. Please try again.");
        };

        reader.readAsArrayBuffer(file);
      } else {
        // Handle text and CSV files
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          if (!content) return;

          let importedKeywords: string[] = [];

          if (fileExtension === "csv") {
            importedKeywords = content
              .split("\n")
              .map((line) => {
                const firstColumn = line.split(",")[0];
                return firstColumn ? firstColumn.trim().replace(/"/g, "") : "";
              })
              .filter((line) => line.length > 0);
          } else {
            importedKeywords = content
              .split("\n")
              .map((line) => line.trim())
              .filter((line) => line.length > 0);
          }

          addKeywordsToTable(importedKeywords);
        };

        reader.onerror = () => {
          setError("Failed to read file. Please try again.");
        };

        reader.readAsText(file);
      }
    } catch (err) {
      setError("Error processing file. Please try again.");
      console.error(err);
    }

    event.target.value = "";
  };

  const addKeywordsToTable = (importedKeywords: string[]) => {
    if (selectedConfigIds.length === 0) {
      setError(
        "Please select at least one search engine configuration before importing keywords"
      );
      return;
    }

    importedKeywords.forEach((keyword) => {
      if (keyword.trim()) {
        addKeyword(keyword.trim(), selectedConfigIds);
      }
    });

    setError("");
  };

  return {
    handleFileChange,
  };
};
