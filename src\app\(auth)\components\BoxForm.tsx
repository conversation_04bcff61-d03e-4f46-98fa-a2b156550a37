"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { HomeIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

type BoxFormType = {
  children: React.ReactNode;
  title?: string;
};

export default function BoxForm({ children, title }: BoxFormType) {
  const router = useRouter();

  const handleGoBack = () => {
    // Check if there's history to go back to
    if (window.history.length > 1) {
      router.back();
    } else {
      // If no history, go to home
      router.push("/");
    }
  };

  return (
    <div className="flex justify-center items-center w-full max-w-md mx-auto relative z-10 mt-[-300px]">
      <div className="bg-white rounded-2xl flex flex-col gap-0 shadow-xl w-full">
        {/* Header with <PERSON><PERSON> and Go Back Button */}
        <div className="px-6 py-6 border-b border-light-gray">
          <div className="flex items-center justify-between mb-4">
            {/* Go Back Button */}
            <button
              onClick={handleGoBack}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-primary/30"
              aria-label="Go back to previous page"
              title="Go back"
            >
              <ArrowLeft className="w-4 h-4 group-hover:-translate-x-0.5 transition-transform duration-200" />
              <span className="text-sm font-medium">Back</span>
            </button>

            {/* Home Link */}
            <Link
              href="/"
              className="flex items-center space-x-1.5 p-2 hover:bg-gray-100 rounded-md text-sm text-gray-500 hover:text-primary transition-colors duration-200"
              title="Go to home"
            >
              <HomeIcon className="w-5 h-5" />
              <span>Home</span>
            </Link>
          </div>

          {/* Logo */}
          <div className="flex justify-center mb-4">
            <Link
              href="/"
              className="group transition-all duration-200 hover:scale-105"
              title="Go to SEO Analyser Home"
            >
              <div className="w-[140px] h-[55px] relative">
                <Image
                  src="/images/appLogo.svg"
                  alt="SEO Analyser Logo"
                  fill
                  quality={100}
                  className="w-full h-full"
                  priority
                />
              </div>
            </Link>
          </div>

          {/* Title */}
          {title && (
            <div className="text-center">
              <h1 className="text-xl font-semibold text-gray-700">{title}</h1>
            </div>
          )}
        </div>

        {/* Form Content */}
        <div className="p-6 flex flex-col gap-4">{children}</div>
      </div>
    </div>
  );
}
