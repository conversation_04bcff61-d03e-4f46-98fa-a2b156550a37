import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import React from "react";
import Skeleton from "react-loading-skeleton";

const PagesBarsSkeleton = () => {
  return (
    <Card className="space-y-6">
      <div className="space-y-2">
        <div className="flex w-full justify-between items-center">
          <Title>Pages</Title>
          <Skeleton width={145} height={45} />
        </div>
        <Skeleton width={"60%"} height={12} />
        <div className="flex h-[30%] gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-[64px] w-full">
              <Skeleton height={"100%"} width={"100%"} />
            </div>
          ))}
        </div>
      </div>
      <Skeleton height={418} />
    </Card>
  );
};

export default PagesBarsSkeleton;
