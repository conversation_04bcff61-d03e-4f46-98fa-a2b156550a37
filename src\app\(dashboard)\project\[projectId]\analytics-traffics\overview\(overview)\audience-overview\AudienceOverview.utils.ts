import type {
  AudienceOverviewApiResponse,
  ChartResponse,
} from "./AudienceOverview.types";

/**
 * Formats a number to a readable string with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Formats percentage to display with % symbol
 */
export const formatPercentage = (rate: number): string => {
  return (rate * 100).toFixed(1) + "%";
};

/**
 * Formats time in seconds to readable format
 */
export const formatTime = (seconds: number): string => {
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${Math.floor(seconds)}s`;
};

/**
 * Formats date from YYYYMMDD to readable format
 * Uses month abbreviation and day for better readability
 */
export const formatDate = (dateStr: string): string => {
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);
  const date = new Date(`${year}-${month}-${day}`);

  // Use format: Jul 16
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between current and previous period
 */
export const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return "+0%";
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

/**
 * Transforms the audience overview API response into chart data format
 */
export const transformAudienceOverviewData = (
  apiResponse: AudienceOverviewApiResponse
): ChartResponse[] => {
  const { daily_metrics, totals } = apiResponse.data;

  // Create chart data for each metric
  const charts: ChartResponse[] = [
    {
      id: 1,
      title: "Total Users",
      bigNumber: formatNumber(totals.total_users),
      smallNumber: calculateGrowth(
        totals.total_users,
        totals.total_users * 0.8
      ), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.total_users,
      })),
    },
    {
      id: 2,
      title: "New Users",
      bigNumber: formatNumber(totals.new_users),
      smallNumber: calculateGrowth(totals.new_users, totals.new_users * 0.9), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.new_users,
      })),
    },
    {
      id: 3,
      title: "Returning Users",
      bigNumber: formatNumber(totals.returning_users),
      smallNumber: formatPercentage(totals.returning_users_rate / 100),
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.returning_users,
      })),
    },
    {
      id: 4,
      title: "Page Views",
      bigNumber: formatNumber(totals.views),
      smallNumber: calculateGrowth(totals.views, totals.views * 0.85), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.views,
      })),
    },
    {
      id: 5,
      title: "Sessions",
      bigNumber: formatNumber(totals.sessions),
      smallNumber: calculateGrowth(totals.sessions, totals.sessions * 0.9), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.sessions,
      })),
    },
    {
      id: 6,
      title: "Engaged Sessions",
      bigNumber: formatNumber(totals.engaged_sessions),
      smallNumber: formatPercentage(totals.engagement_rate),
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.engaged_sessions,
      })),
    },
    {
      id: 7,
      title: "Avg. Engagement Time",
      bigNumber: formatTime(totals.avg_engagement_time),
      smallNumber: calculateGrowth(
        totals.avg_engagement_time,
        totals.avg_engagement_time * 0.8
      ), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.avg_engagement_time,
      })),
    },
    {
      id: 8,
      title: "Events",
      bigNumber: formatNumber(totals.event_count),
      smallNumber: calculateGrowth(
        totals.event_count,
        totals.event_count * 0.85
      ), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.event_count,
      })),
    },
    {
      id: 9,
      title: "Active Users",
      bigNumber: formatNumber(totals.active_users),
      smallNumber: formatPercentage(totals.active_users_rate / 100),
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.active_users,
      })),
    },
    {
      id: 10,
      title: "Engagement Rate",
      bigNumber: formatPercentage(totals.engagement_rate),
      smallNumber: calculateGrowth(
        totals.engagement_rate,
        totals.engagement_rate * 0.9
      ), // Mock growth
      data: daily_metrics.map((metric) => ({
        name: formatDate(metric.date),
        value: metric.engagement_rate * 100, // Convert to percentage for chart
      })),
    },
  ];

  return charts;
};
