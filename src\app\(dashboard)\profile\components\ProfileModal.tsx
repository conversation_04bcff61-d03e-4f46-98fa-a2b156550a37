"use client";

import React from "react";
import { UseFormHandleSubmit } from "react-hook-form";
import * as Dialog from "@radix-ui/react-dialog";
import { XMarkIcon } from "@heroicons/react/24/outline";
import ButtenSubmit from "@/components/shared/ButtenSubmit";

import FetchLoadingBox from "@/components/shared/FetchLoadingBox";
import ErrorFetch from "@/components/shared/ErrorFetch";

import CurrentProfileSection from "./CurrentProfileSection";
import PersonalInfoForm from "./PersonalInfoForm";
import MarketingSettingsAccordion from "./MarketingSettingsAccordion";

interface FormProfileType {
  first_name: string;
  last_name: string;
  phone_number: string;
}

interface ProfileModalProps {
  open: boolean;
  onClose: () => void;
  fetchProfile: boolean;
  fetchAvatar: boolean;
  isErrorAvatar: boolean;
  isErrorSetting: boolean;
  dataAvatars: any;
  dataUserProfile: any;
  selectAvatar: string;
  setSelectAvatar: (val: string) => void;
  register: any;
  errors: any;
  choice: Record<number, string[]>;
  setChoice: (choice: Record<number, string[]>) => void;
  errText: string[];
  handleSubmit: UseFormHandleSubmit<FormProfileType>;
  handleUpdateProfile: (data: FormProfileType) => void;
  isPending: boolean;
}

export default function ProfileModal({
  open,
  onClose,
  fetchProfile,
  fetchAvatar,
  isErrorAvatar,
  isErrorSetting,
  dataAvatars,
  dataUserProfile,
  selectAvatar,
  setSelectAvatar,
  register,
  errors,
  choice,
  setChoice,
  errText,
  handleSubmit,
  handleUpdateProfile,
  isPending,
}: ProfileModalProps) {
  return (
    <>
      <style jsx global>{`
        .profile-modal-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .profile-modal-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .profile-modal-scrollbar::-webkit-scrollbar-thumb {
          background-color: rgba(156, 163, 175, 0.5);
          border-radius: 3px;
        }
        .profile-modal-scrollbar::-webkit-scrollbar-thumb:hover {
          background-color: rgba(156, 163, 175, 0.7);
        }
      `}</style>
      <Dialog.Root open={open} onOpenChange={onClose}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
          <Dialog.Content className="fixed left-[50%] top-[50%] z-50 flex flex-col w-full max-w-[650px] sm:max-w-[750px] lg:max-w-[900px] max-h-[85vh] translate-x-[-50%] translate-y-[-50%] bg-white rounded-xl sm:rounded-2xl border border-gray-300 shadow-[0_0_20px_0_rgba(29,34,65,0.1)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 duration-200 overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 lg:px-5 border-b border-gray-200 flex-shrink-0">
              <Dialog.Title className="text-base sm:text-lg lg:text-xl font-bold text-secondary truncate pr-3">
                Personal Information
              </Dialog.Title>
              <Dialog.Close className="hover:opacity-80 active:opacity-70 transition-opacity flex-shrink-0">
                <XMarkIcon className="w-8 h-8 sm:w-9 sm:h-9 lg:w-10 lg:h-10 text-secondary" />
              </Dialog.Close>
            </div>
            {/* Scrollable Content */}
            <div
              className="flex-1 overflow-y-auto overflow-x-hidden px-4 sm:px-6 py-2 min-h-0 profile-modal-scrollbar"
              style={{
                scrollbarWidth: "thin",
                scrollbarColor: "rgba(156, 163, 175, 0.5) transparent",
              }}
            >
              <FetchLoadingBox isFetching={fetchProfile}>
                {/* Box 1: Current Profile Section */}
                <CurrentProfileSection
                  dataUserProfile={dataUserProfile}
                  dataAvatars={dataAvatars}
                  fetchAvatar={fetchAvatar}
                  isErrorAvatar={isErrorAvatar}
                  selectAvatar={selectAvatar}
                  setSelectAvatar={setSelectAvatar}
                />

                <ErrorFetch isError={isErrorSetting}>
                  {/* Box 2: Personal Information Edit Box */}
                  <PersonalInfoForm
                    register={register}
                    errors={errors}
                    dataUserProfile={dataUserProfile}
                  />

                  {/* Box 3: Marketing & Business Settings Accordion */}
                  <MarketingSettingsAccordion
                    choice={choice}
                    setChoice={setChoice}
                    errors={
                      errText.length > 0
                        ? { general: errText.join(", ") }
                        : undefined
                    }
                    showValidation={errText.length > 0}
                  />

                  {/* Error Messages */}
                  {Array.isArray(errText) && errText.length > 0 && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      {errText.map((error, index) => (
                        <span
                          key={index}
                          className="text-primary-red text-sm block mb-1 last:mb-0"
                        >
                          • {error}
                        </span>
                      ))}
                    </div>
                  )}
                </ErrorFetch>
              </FetchLoadingBox>
            </div>

            {/* Sticky Footer with Apply Button */}
            <div className="border-t border-gray-200 bg-white px-4 sm:px-6 py-4 flex-shrink-0">
              <ButtenSubmit
                onClick={handleSubmit(handleUpdateProfile)}
                classPluss="w-full"
                text="Apply Changes"
                textloading="Applying Changes..."
                isLoading={isPending}
              />
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </>
  );
}
