import { SVGProps } from "react";

export function CallIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.7933 19.14C12.5933 19.0959 9.19251 18.6259 5.63084 15.065C2.07001 11.5034 1.60084 8.10336 1.55584 6.90252C1.48918 5.07252 2.89084 3.29502 4.51001 2.60086C4.70499 2.51666 4.91851 2.48461 5.12962 2.50784C5.34073 2.53107 5.54217 2.60878 5.71418 2.73336C7.04751 3.70502 7.96751 5.17502 8.75751 6.33086C8.93133 6.5848 9.00565 6.89381 8.96631 7.19902C8.92697 7.50423 8.77671 7.7843 8.54418 7.98586L6.91834 9.19336C6.8398 9.25008 6.7845 9.33338 6.76274 9.42779C6.74098 9.52221 6.75422 9.62131 6.80001 9.70669C7.16834 10.3759 7.82334 11.3725 8.57334 12.1225C9.32334 12.8725 10.3675 13.5709 11.0833 13.9809C11.1731 14.0312 11.2787 14.0453 11.3786 14.0202C11.4784 13.9951 11.5648 13.9327 11.62 13.8459L12.6783 12.235C12.8729 11.9766 13.1599 11.8034 13.4793 11.7518C13.7987 11.7003 14.1256 11.7743 14.3917 11.9584C15.5642 12.77 16.9325 13.6742 17.9342 14.9567C18.0689 15.1299 18.1545 15.3362 18.1822 15.5539C18.21 15.7716 18.1787 15.9927 18.0917 16.1942C17.3942 17.8217 15.6292 19.2075 13.7933 19.14Z"
        fill="currentColor"
      />
    </svg>
  );
}
