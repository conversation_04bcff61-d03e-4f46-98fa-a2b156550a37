"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { useAnalyticsServices } from "@/hooks/useAnalyticsServices";

interface AnalyticsConnectionContextType {
  // Connection status
  isGoogleAnalyticsConnected: boolean;
  isGoogleSearchConsoleConnected: boolean;
  
  // Raw status data
  gaStatus: any;
  gscStatus: any;
  
  // Connection actions
  connectGA: () => void;
  connectGSC: () => void;
  gaConnecting: boolean;
  gscConnecting: boolean;
  
  // Dialog state (for overview page)
  showPropertyDialog: boolean;
  setShowPropertyDialog: (show: boolean) => void;
  showGSCPropertyDialog: boolean;
  setShowGSCPropertyDialog: (show: boolean) => void;
  
  // Property selection handlers
  handlePropertySelected: (property: any) => void;
  handleGSCPropertySelected: (property: any) => void;
  
  // Refetch functions
  refetchGAStatus: () => void;
  refetchGSCStatus: () => void;
  
  // Helper functions for API calls
  isGoogleAnalyticsReady: boolean;
  isGoogleSearchConsoleReady: boolean;
}

const AnalyticsConnectionContext = createContext<AnalyticsConnectionContextType | undefined>(undefined);

interface AnalyticsConnectionProviderProps {
  children: ReactNode;
  projectId: string | null;
  enableAutoDialogs?: boolean;
}

export const AnalyticsConnectionProvider: React.FC<AnalyticsConnectionProviderProps> = ({
  children,
  projectId,
  enableAutoDialogs = false,
}) => {
  const analyticsServices = useAnalyticsServices({
    projectId,
    enableAutoDialogs,
  });

  // Helper functions to determine if services are ready for API calls
  const isGoogleAnalyticsReady = analyticsServices.isGoogleAnalyticsConnected();
  const isGoogleSearchConsoleReady = analyticsServices.isGoogleSearchConsoleConnected();

  const contextValue: AnalyticsConnectionContextType = {
    // Connection status
    isGoogleAnalyticsConnected: analyticsServices.isGoogleAnalyticsConnected(),
    isGoogleSearchConsoleConnected: analyticsServices.isGoogleSearchConsoleConnected(),
    
    // Raw status data
    gaStatus: analyticsServices.gaStatus,
    gscStatus: analyticsServices.gscStatus,
    
    // Connection actions
    connectGA: analyticsServices.connectGA,
    connectGSC: analyticsServices.connectGSC,
    gaConnecting: analyticsServices.gaConnecting,
    gscConnecting: analyticsServices.gscConnecting,
    
    // Dialog state
    showPropertyDialog: analyticsServices.showPropertyDialog,
    setShowPropertyDialog: analyticsServices.setShowPropertyDialog,
    showGSCPropertyDialog: analyticsServices.showGSCPropertyDialog,
    setShowGSCPropertyDialog: analyticsServices.setShowGSCPropertyDialog,
    
    // Property selection handlers
    handlePropertySelected: analyticsServices.handlePropertySelected,
    handleGSCPropertySelected: analyticsServices.handleGSCPropertySelected,
    
    // Refetch functions
    refetchGAStatus: analyticsServices.refetchGAStatus,
    refetchGSCStatus: analyticsServices.refetchGSCStatus,
    
    // Helper functions for API calls
    isGoogleAnalyticsReady,
    isGoogleSearchConsoleReady,
  };

  return (
    <AnalyticsConnectionContext.Provider value={contextValue}>
      {children}
    </AnalyticsConnectionContext.Provider>
  );
};

export const useAnalyticsConnection = (): AnalyticsConnectionContextType => {
  const context = useContext(AnalyticsConnectionContext);
  if (context === undefined) {
    throw new Error("useAnalyticsConnection must be used within an AnalyticsConnectionProvider");
  }
  return context;
};

// Export types for use in other components
export type { AnalyticsConnectionContextType };
