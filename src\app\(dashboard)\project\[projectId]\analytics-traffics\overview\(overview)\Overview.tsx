"use client";
import React, { useState } from "react";

/* =============================== COMPONENTS =============================== */
import ConnectGoogleAnalyticsPopup from "../../_components/ConnectGoogleAnalyticsPopup";
import SharedAnalyticsHeader from "../../_components/SharedAnalyticsHeader";
import GoogleAnalyticsPropertyDialog from "@/components/CreateProject/GoogleAnalyticsPropertyDialog";
import GoogleSearchConsolePropertyDialog from "@/components/CreateProject/GoogleSearchConsolePropertyDialog";
import AudienceOverview from "../(overview)/audience-overview/AudienceOverview";
import UsersOverview from "../(overview)/users-overview/UsersOverview";
import TrafficOverview from "../(overview)/traffic-overview/TrafficOverview";
import GSCOverview from "../(overview)/gsc-overview/GSCOverview";

/* ================================ API CALLS =============================== */
import { useProjectId } from "@/hooks/useProjectId";
import { useAnalyticsConnection } from "@/contexts/AnalyticsConnectionContext";

/* ========================================================================== */
const Overview = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [connectGooglePopup, setConnectGooglePopup] = useState<boolean>(false);

  const { projectId } = useProjectId();

  // Use the analytics connection context
  const {
    showPropertyDialog,
    setShowPropertyDialog,
    showGSCPropertyDialog,
    setShowGSCPropertyDialog,
    handlePropertySelected,
    handleGSCPropertySelected,
  } = useAnalyticsConnection();

  // Note: API call moved to AudienceOverview component hook
  // This useEffect is no longer needed as data fetching is handled by the AudienceOverview component

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full h-fit space-y-6">
      <ConnectGoogleAnalyticsPopup
        connectGooglePopup={connectGooglePopup}
        setConnectGooglePopup={setConnectGooglePopup}
      />

      <SharedAnalyticsHeader title="Traffics" />

      <AudienceOverview />
      <UsersOverview />
      <TrafficOverview />
      <GSCOverview />

      {/* Google Analytics Property Selection Dialog */}
      {projectId && (
        <GoogleAnalyticsPropertyDialog
          isOpen={showPropertyDialog}
          onClose={() => setShowPropertyDialog(false)}
          projectId={projectId}
          onPropertySelected={handlePropertySelected}
        />
      )}

      {/* Google Search Console Property Selection Dialog */}
      {projectId && (
        <GoogleSearchConsolePropertyDialog
          isOpen={showGSCPropertyDialog}
          onClose={() => setShowGSCPropertyDialog(false)}
          projectId={projectId}
          onPropertySelected={handleGSCPropertySelected}
        />
      )}
    </div>
  );
};

export default Overview;
