"use client";

import React from "react";
import { AnalyticsConnectionProvider } from "@/contexts/AnalyticsConnectionContext";
import { useProjectId } from "@/hooks/useProjectId";

interface AnalyticsTrafficsLayoutProps {
  children: React.ReactNode;
}

const AnalyticsTrafficsLayout: React.FC<AnalyticsTrafficsLayoutProps> = ({
  children,
}) => {
  const { projectId } = useProjectId();

  return (
    <AnalyticsConnectionProvider
      projectId={projectId}
      enableAutoDialogs={true} // Enable auto dialogs for all analytics pages
    >
      {children}
    </AnalyticsConnectionProvider>
  );
};

export default AnalyticsTrafficsLayout;
