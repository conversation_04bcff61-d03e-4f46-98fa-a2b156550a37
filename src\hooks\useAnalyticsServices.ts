"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useQuery, useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import {
  projectAPI,
  GoogleAnalyticsConnectRequest,
  GoogleAnalyticsProperty,
  GoogleSearchConsoleConnectRequest,
  GoogleSearchConsoleProperty,
} from "@/services/projectService";

interface UseAnalyticsServicesProps {
  projectId: string | null;
  enableAutoDialogs?: boolean; // Whether to automatically show property dialogs on OAuth return
}

export const useAnalyticsServices = ({
  projectId,
  enableAutoDialogs = true,
}: UseAnalyticsServicesProps) => {
  const [showPropertyDialog, setShowPropertyDialog] = useState(false);
  const [showGSCPropertyDialog, setShowGSCPropertyDialog] = useState(false);
  const [gaStatusChecked, setGaStatusChecked] = useState(false);
  const [gscStatusChecked, setGscStatusChecked] = useState(false);

  const searchParams = useSearchParams();
  const method = searchParams.get("method");

  // Check Google Analytics status when page loads
  const { data: gaStatus, refetch: refetchGAStatus } = useQuery({
    queryKey: ["google-analytics-status", projectId],
    queryFn: async () => {
      if (!projectId) return null;

      try {
        const response = await projectAPI.getGoogleAnalyticsStatus(projectId);
        return response.data;
      } catch (error: any) {
        // Handle 404 and other errors by returning the error response data
        if (error.response && error.response.data) {
          console.warn("Google Analytics status error:", error.response.data);
          return error.response.data;
        }
        // For other errors, return a default error structure
        return {
          status: "error",
          message: "Failed to check Google Analytics connection status",
        };
      }
    },
    enabled: !!projectId,
  });

  // Check Google Search Console status when page loads
  const { data: gscStatus, refetch: refetchGSCStatus } = useQuery({
    queryKey: ["google-search-console-status", projectId],
    queryFn: async () => {
      if (!projectId) return null;

      try {
        const response = await projectAPI.getGoogleSearchConsoleStatus(projectId);
        return response.data;
      } catch (error: any) {
        // Handle 404 and other errors by returning the error response data
        if (error.response && error.response.data) {
          console.warn("Google Search Console status error:", error.response.data);
          return error.response.data;
        }
        // For other errors, return a default error structure
        return {
          status: "error",
          message: "Failed to check Google Search Console connection status",
        };
      }
    },
    enabled: !!projectId,
  });

  // Helper function to check if Google Analytics is connected
  const isGoogleAnalyticsConnected = () => {
    // Only connected if connection exists and has success or connected status
    return (
      gaStatus?.connection &&
      (gaStatus.connection.status === "success" ||
        gaStatus.connection.status === "connected")
    );
  };

  // Helper function to check if Google Search Console is connected
  const isGoogleSearchConsoleConnected = () => {
    // Only connected if connection exists and has success or connected status
    return (
      gscStatus?.connection &&
      (gscStatus.connection.status === "success" ||
        gscStatus.connection.status === "connected")
    );
  };

  // Check Google Analytics status and show dialog if needed (only for G4a method)
  useEffect(() => {
    if (!enableAutoDialogs) return;

    console.warn("Google Analytics useEffect triggered:", {
      projectId,
      gaStatus,
      gaStatusChecked,
      method,
      conditionMet:
        projectId && gaStatus && !gaStatusChecked && method === "G4a",
    });

    // Only run this logic if we're coming back from OAuth (method=G4a)
    if (projectId && gaStatus && !gaStatusChecked && method === "G4a") {
      setGaStatusChecked(true);

      console.warn("Google Analytics status check:", {
        status: gaStatus.status,
        message: gaStatus.message,
        connection: gaStatus.connection,
        method: method,
      });

      // Don't show modal if status is error (including 404 no connection found)
      const isErrorStatus = gaStatus.status === "error";

      // Specifically check for "no connection found" message
      const isNoConnectionError =
        isErrorStatus &&
        gaStatus.message &&
        gaStatus.message
          .toLowerCase()
          .includes("no google analytics connection found");

      // Only show modal if:
      // 1. NOT an error status
      // 2. Status is success/pending OR connection exists with success/pending status
      const shouldShowModal =
        !isErrorStatus &&
        (gaStatus.status === "pending" ||
          gaStatus.status === "success" ||
          (gaStatus.connection &&
            (gaStatus.connection.status === "pending" ||
              gaStatus.connection.status === "success")));

      if (isNoConnectionError) {
        console.warn(
          "Google Analytics: No connection found, skipping property dialog"
        );
      }

      if (shouldShowModal) {
        console.warn("Google Analytics: Opening property dialog");
        setShowPropertyDialog(true);
      } else {
        console.warn("Google Analytics: NOT opening property dialog");
      }
    }
  }, [gaStatus, gaStatusChecked, projectId, method, enableAutoDialogs]);

  // Check Google Search Console status and show dialog if needed (only for GSC method)
  useEffect(() => {
    if (!enableAutoDialogs) return;

    if (projectId && gscStatus && !gscStatusChecked && method === "GSC") {
      setGscStatusChecked(true);

      // Don't show modal if status is error (including 404 no connection found)
      const isErrorStatus = gscStatus.status === "error";

      // Specifically check for "no connection found" message
      const isNoConnectionError =
        isErrorStatus &&
        gscStatus.message &&
        gscStatus.message
          .toLowerCase()
          .includes("no google search console connection found");

      // Only show modal if:
      // 1. NOT an error status
      // 2. Status is success/pending OR connection exists with success/pending status
      const shouldShowModal =
        !isErrorStatus &&
        (gscStatus.status === "pending" ||
          gscStatus.status === "success" ||
          (gscStatus.connection &&
            (gscStatus.connection.status === "pending" ||
              gscStatus.connection.status === "success")));

      if (isNoConnectionError) {
        console.warn(
          "Google Search Console: No connection found, skipping property dialog"
        );
      }

      if (shouldShowModal) {
        setShowGSCPropertyDialog(true);
      }
    }
  }, [gscStatus, gscStatusChecked, projectId, method, enableAutoDialogs]);

  // Google Analytics connection mutation
  const { mutate: connectGA, isPending: gaConnecting } = useMutation({
    mutationFn: async (): Promise<any> => {
      if (!projectId) {
        throw new Error("Project ID is missing");
      }

      const data: GoogleAnalyticsConnectRequest = {
        project_id: projectId,
      };

      const response = await projectAPI.connectGoogleAnalytics(data);
      return response.data;
    },
    onSuccess: (response) => {
      // Redirect to the authorization URL
      if (response.authorization_url) {
        window.location.href = response.authorization_url;
      }
      console.warn("Google Analytics authorization URL received:", response);
    },
    onError: (error: any) => {
      console.error("Error connecting Google Analytics:", error);
      toast.error("Failed to connect Google Analytics");
    },
  });

  // Google Search Console connection mutation
  const { mutate: connectGSC, isPending: gscConnecting } = useMutation({
    mutationFn: async (): Promise<any> => {
      if (!projectId) {
        throw new Error("Project ID is missing");
      }

      const data: GoogleSearchConsoleConnectRequest = {
        project_id: projectId,
      };

      const response = await projectAPI.connectGoogleSearchConsole(data);
      return response.data;
    },
    onSuccess: (response) => {
      // Redirect to the authorization URL
      if (response.authorization_url) {
        window.location.href = response.authorization_url;
      }
      console.warn("Google Search Console authorization URL received:", response);
    },
    onError: (error: any) => {
      console.error("Error connecting Google Search Console:", error);
      toast.error("Failed to connect Google Search Console");
    },
  });

  // Handle property selection
  const handlePropertySelected = (property: GoogleAnalyticsProperty) => {
    console.warn("Selected property:", property);
    setShowPropertyDialog(false);
    // Refresh the status to show updated connection
    refetchGAStatus();
  };

  // Handle Google Search Console property selection
  const handleGSCPropertySelected = (property: GoogleSearchConsoleProperty) => {
    console.warn("Selected GSC property:", property);
    setShowGSCPropertyDialog(false);
    // Refresh the status to show updated connection
    refetchGSCStatus();
  };

  return {
    // Status data
    gaStatus,
    gscStatus,
    
    // Connection status helpers
    isGoogleAnalyticsConnected,
    isGoogleSearchConsoleConnected,
    
    // Connection actions
    connectGA,
    connectGSC,
    gaConnecting,
    gscConnecting,
    
    // Dialog state
    showPropertyDialog,
    setShowPropertyDialog,
    showGSCPropertyDialog,
    setShowGSCPropertyDialog,
    
    // Property selection handlers
    handlePropertySelected,
    handleGSCPropertySelected,
    
    // Refetch functions
    refetchGAStatus,
    refetchGSCStatus,
  };
};
