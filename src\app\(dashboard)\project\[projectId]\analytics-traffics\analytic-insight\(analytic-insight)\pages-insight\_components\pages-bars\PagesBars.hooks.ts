import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import type { PagesBarsResponse } from "./PagesBars.types";
import { useProjectId } from "@/hooks/useProjectId";

const usePagesBars = ({ tab, filter }: { tab: string; filter: string }) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["pages-bars", projectId, tab, filter],
    queryFn: async (): Promise<PagesBarsResponse> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/pages-insight/pages-bars",
        {
          params: {
            projectId,
            tab,
            filter,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default usePagesBars;
