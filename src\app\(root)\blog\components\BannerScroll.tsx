"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";

import Link from "next/link";
import Image from "next/image";
import BlogPic from "../blog.jpg";
import DateHolder from "./DateHolder";

import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { DownIcon } from "@/ui/icons/navigation/DownIcon";
import { BlogCardProps } from "./BlogCard";

export interface BannerScrollProps {
  blogData: BlogCardProps[];
}

const BannerScroll: React.FC<BannerScrollProps> = ({ blogData }) => {
  // Only show the first 3 blog posts in the banner
  const featuredBlogs = blogData?.slice(0, 3) || [];

  return (
    <>
      <Swiper
        navigation={{
          nextEl: `.history-arrow-left`,
          prevEl: `.history-arrow-right`,
        }}
        modules={[Navigation, Autoplay]}
        spaceBetween={8}
        breakpoints={{
          640: {
            spaceBetween: 12,
          },
          768: {
            spaceBetween: 16,
          },
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
      >
        {featuredBlogs.map((blog, index) => (
          <SwiperSlide key={index}>
            <BannerBlogItem
              id={blog.id}
              title={blog.title}
              slug={blog.slug}
              author={blog.author}
              body={blog.body}
              snippet={blog.snippet}
              publish_timestamp={blog.publish_timestamp}
              url={blog.url}
              tags={blog.tags}
              category={blog.category}
              cover_image={blog.cover_image}
            />
          </SwiperSlide>
        ))}
        <div className="flex items-center gap-2 sm:gap-3 md:gap-4 mt-3 sm:mt-4 lg:mt-6 mb-3 sm:mb-4 lg:mb-6 pb-3 sm:pb-4 lg:pb-6 border-b border-b-light-gray">
          <button
            className={`history-arrow-right btn btn--outline !py-0.5 sm:!py-1 !px-2 sm:!px-2.5 disabled:!border-transparent disabled:bg-secondary/20`}
          >
            <DownIcon className={"rotate-90 w-6 h-6"} />
          </button>
          <button
            className={`history-arrow-left btn btn--outline !py-0.5 sm:!py-1 !px-2 sm:!px-2.5 disabled:!border-transparent disabled:bg-secondary/20`}
          >
            <DownIcon className={"-rotate-90 w-6 h-6"} />
          </button>
        </div>
      </Swiper>
    </>
  );
};

export default BannerScroll;

export const BannerBlogItem: React.FC<BlogCardProps> = ({
  title,
  slug,
  author,
  body,
  snippet,
  publish_timestamp,
  url,
  category,
  cover_image,
}) => {
  // Get author display name based on author type
  const getAuthorName = () => {
    if (!author) return "Unknown Author";

    if (typeof author === "string") {
      // If author is a string (email), use it or extract name part
      return author.split("@")[0] || author;
    } else {
      // If author is an object, use display_name
      return author.display_name || "Unknown Author";
    }
  };

  // Get description from snippet or body
  const getDescription = () => {
    // Use snippet if available
    if (snippet) {
      return snippet;
    }

    // Otherwise extract from body
    if (!body) return "";

    if (typeof window === "undefined") {
      // Server-side: use regex to strip HTML tags
      const text = body.replace(/<[^>]*>?/gm, "").trim();
      // Use shorter description on smaller screens (handled by CSS line-clamp)
      return text.substring(0, 200) + (text.length > 200 ? "..." : "");
    } else {
      // Client-side: use DOM to parse HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = body;
      // Get text content and limit to 200 characters
      const text = tempDiv.textContent || tempDiv.innerText || "";
      return text.substring(0, 200) + (text.length > 200 ? "..." : "");
    }
  };

  // Determine the correct URL to use
  const getPostUrl = () => {
    // If a URL is provided from the API, use it
    if (url) {
      return url;
    }

    // If a category is provided, use the category/slug format
    if (category && category.slug) {
      return `/blog/${category.slug}/${slug}`;
    }

    // Default to SEO category if no category is provided
    return `/blog/SEO/${slug}`;
  };

  return (
    <Link
      href={getPostUrl()}
      className="w-full decoration-0 relative overflow-hidden rounded-2xl block"
    >
      <Image
        src={cover_image || BlogPic}
        width={930}
        height={450}
        alt={title}
        className="w-full h-[220px] md:h-[400px] max-h-[550px] overflow-hidden rounded-2xl object-cover object-center"
      />
      <div className="p-3 sm:p-4 md:p-6 overflow-hidden rounded-2xl flex flex-col max-h-[400px] justify-between absolute top-0 left-0 w-full h-full bg-gradient-to-b from-[#15233000] to-[#152330]">
        <div className="flex gap-1 sm:gap-2 items-start justify-between">
          <DateHolder date={publish_timestamp} />
          <div className="bg-white flex flex-col gap-1 rounded-lg px-2 md:px-4 py-2 text-[10px] sm:text-[11px] md:text-[14px]">
            <div>
              Written by <b>{getAuthorName()}</b>
              {category && (
                <div className="text-gray-700 text-[8px] sm:text-[9px] md:text-[10px]">
                  in {category.name}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="text-white">
          <h3 className="font-black text-lg md:text-xl lg:text-2xl mb-1 sm:mb-2 md:mb-3 lg:mb-4 line-clamp-2">
            {title}
          </h3>
          <p className="text-[10px] sm:text-xs md:text-sm lg:text-base line-clamp-3 sm:line-clamp-4 md:line-clamp-5">
            {getDescription()}
          </p>
        </div>
      </div>
    </Link>
  );
};
