import React from "react";

/* ========================= REACT LOADING SKELETON ========================= */
import Skeleton from "react-loading-skeleton";

/* ================================= ZUSTAND ================================ */
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ========================================================================== */
const DateRange = ({
  variation = "long",
}: {
  variation?: "short" | "long" | "range";
}) => {
  const { selectedRange, comparisonRange, isComparisonEnabled } =
    useDateRangeStore();
  const themeColor = useProjectThemeColor((state) => state.themeColor);

  // Helper function to format date for display
  const formatDateForDisplay = (date: Date | undefined): string => {
    if (!date) return "";
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Check if we have valid date ranges
  const hasSelectedRange = selectedRange?.from && selectedRange?.to;
  const hasComparisonRange = comparisonRange?.from && comparisonRange?.to;
  // Show loading state if no date range is selected yet
  if (!hasSelectedRange) {
    return (
      <div className="space-x-1 text-xs font-bold text-secondary flex">
        <span className="font-normal">Date Range :</span>
        <div className="flex gap-1">
          <Skeleton width={100} /> <span> -- </span>
          <Skeleton width={100} />
        </div>
        {variation !== "range" && (
          <>
            <div className="text-primary" style={{ color: themeColor }}>
              Compared to
            </div>
            <div className="flex gap-1">
              <Skeleton width={100} /> <span> -- </span>
              <Skeleton width={100} />
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="space-x-1 text-xs font-bold text-secondary">
      {variation === "long" || variation === "range" ? (
        <>
          <span className="font-normal">Date Range :</span>
          <span>
            {formatDateForDisplay(selectedRange.from)} --{" "}
            {formatDateForDisplay(selectedRange.to)}
          </span>
          {variation !== "range" &&
            isComparisonEnabled &&
            hasComparisonRange && (
              <>
                <span className="text-primary" style={{ color: themeColor }}>
                  Compared to
                </span>
                <span>
                  {formatDateForDisplay(comparisonRange.from)} --{" "}
                  {formatDateForDisplay(comparisonRange.to)}
                </span>
              </>
            )}
        </>
      ) : (
        <>
          <span className="font-normal">Date Range :</span>
          <span>{formatDateForDisplay(selectedRange.from)}</span>
        </>
      )}
    </div>
  );
};

export default DateRange;
