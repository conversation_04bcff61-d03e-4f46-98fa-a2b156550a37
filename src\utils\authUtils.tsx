"use client";

import { useAuth } from "@/providers/AuthProvider";
import { useRouter } from "next/navigation";
import { useEffect, ReactNode } from "react";

/**
 * Higher-order component that requires authentication
 * Redirects to login if user is not authenticated
 */
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isHydrated, redirectToLogin } = useAuth();

    useEffect(() => {
      // Only check auth after hydration to prevent SSR issues
      if (isHydrated && !isAuthenticated) {
        redirectToLogin();
      }
    }, [isAuthenticated, isHydrated, redirectToLogin]);

    // Show loading while hydrating or redirecting
    if (!isHydrated || !isAuthenticated) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
}

/**
 * Protected route component that requires authentication
 * Use this to wrap pages or components that need auth
 */
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  requireAuth?: boolean;
}

export function ProtectedRoute({
  children,
  fallback,
  requireAuth = true,
}: ProtectedRouteProps) {
  const { isAuthenticated, isHydrated, redirectToLogin } = useAuth();

  useEffect(() => {
    if (requireAuth && isHydrated && !isAuthenticated) {
      redirectToLogin();
    }
  }, [requireAuth, isAuthenticated, isHydrated, redirectToLogin]);

  // Show fallback while hydrating
  if (!isHydrated) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      )
    );
  }

  // Show fallback if auth is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Authentication Required
            </h2>
            <p className="text-gray-600">Redirecting to login...</p>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}

/**
 * Auth guard hook for conditional rendering based on auth state
 */
export function useAuthGuard() {
  const { isAuthenticated, isHydrated, user, redirectToLogin } = useAuth();

  return {
    isAuthenticated,
    isHydrated,
    user,
    redirectToLogin,
    // Helper functions
    isLoggedIn: isAuthenticated && isHydrated,
    isGuest: !isAuthenticated && isHydrated,
    isLoading: !isHydrated,
  };
}

/**
 * Hook for feature-specific auth requirements
 * Useful for features like PDF download, sharing, etc.
 */
export function useFeatureAuth(featureName: string) {
  const { isAuthenticated, requireLogin } = useAuth();

  const requireAuthForFeature = (callback: () => void) => {
    return requireLogin(callback, window.location.href);
  };

  return {
    isAuthenticated,
    requireAuthForFeature,
    // Helper to check if feature is available
    isFeatureAvailable: isAuthenticated,
  };
}

/**
 * Utility function to check if current route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  const protectedRoutes = [
    "/dashboard",
    "/my-projects",
    "/profile",
    "/settings",
    "/billing",
    "/checkout",
  ];

  return protectedRoutes.some((route) => pathname.startsWith(route));
}

/**
 * Utility function to get redirect URL after login
 */
export function getPostLoginRedirect(): string {
  if (typeof window === "undefined") return "/my-projects";

  // Check for stored redirect URL
  const storedRedirect = sessionStorage.getItem("postLoginRedirect");
  if (storedRedirect) {
    return storedRedirect;
  }

  // Check for analysis context
  const callbackData = sessionStorage.getItem("postLoginCallback");
  if (callbackData) {
    try {
      const data = JSON.parse(callbackData);
      if (data.originalUrl) {
        return data.originalUrl;
      }
    } catch (error) {
      console.error("Failed to parse callback data:", error);
    }
  }

  // Default redirect
  return "/my-projects";
}

/**
 * Utility function to clear auth-related session storage
 */
export function clearAuthSession(): void {
  if (typeof window === "undefined") return;

  sessionStorage.removeItem("postLoginRedirect");
  sessionStorage.removeItem("postLoginCallback");
}

/**
 * Hook to handle auth state changes and redirects
 */
export function useAuthRedirect() {
  const { isAuthenticated, isHydrated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isHydrated && isAuthenticated) {
      // Check if there's a pending redirect
      const redirectUrl = getPostLoginRedirect();
      if (redirectUrl && redirectUrl !== window.location.href) {
        clearAuthSession();
        router.push(redirectUrl);
      }
    }
  }, [isAuthenticated, isHydrated, router]);

  return {
    isAuthenticated,
    isHydrated,
    isReady: isHydrated,
  };
}

/**
 * Component that shows different content based on auth state
 */
interface AuthStateProps {
  authenticated?: ReactNode;
  unauthenticated?: ReactNode;
  loading?: ReactNode;
}

export function AuthState({
  authenticated,
  unauthenticated,
  loading,
}: AuthStateProps) {
  const { isAuthenticated, isHydrated } = useAuth();

  if (!isHydrated) {
    return (
      <>
        {loading || (
          <div className="animate-pulse bg-gray-200 h-8 rounded"></div>
        )}
      </>
    );
  }

  if (isAuthenticated) {
    return <>{authenticated}</>;
  }

  return <>{unauthenticated}</>;
}
