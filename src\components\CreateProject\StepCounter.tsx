import React from "react";

interface StepCounterProps {
  stepNumber: number;
  title: string;
  className?: string;
}

export default function StepCounter({
  stepNumber,
  title,
  className = "",
}: StepCounterProps) {
  return (
    <div className={`flex items-center gap-3 mb-6 ${className}`}>
      <div className="w-10 h-10 bg-primary/15 rounded-md flex items-center justify-center border border-primary/20">
        <span className="text-primary font-bold text-base">{stepNumber}</span>
      </div>
      <h3 className="text-base font-bold text-[#344054]">{title}</h3>
    </div>
  );
}
