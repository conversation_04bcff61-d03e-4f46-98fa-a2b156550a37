# Create Project Flow Documentation

## Overview

The create-project feature provides a comprehensive multi-step wizard for creating and editing SEO projects. It supports multiple user flows with proper state management and navigation.

## Architecture

### File Structure
```
src/app/create-project/
├── page.tsx                           # Main entry point (shortcut page)
├── (Information)/                     # Route group for advanced flow
│   ├── layout.tsx                     # Layout with navigation validation
│   ├── project-information/page.tsx   # Step 1: Basic project info
│   ├── search-engines/page.tsx        # Step 2: Search engine selection
│   ├── keywords/page.tsx              # Step 3: Keyword management
│   ├── competitors/page.tsx           # Step 4: Competitor analysis
│   └── analytics-services/page.tsx    # Step 5: Analytics integration
└── README.md                          # This documentation
```

### Components Structure
```
src/components/CreateProject/
├── SidebarCreateProject.tsx           # Desktop navigation sidebar
├── StepMobileCreateProject.tsx        # Mobile step progress bar
├── NavbarCreateProject.tsx            # Top navigation with exit functionality
├── ShortcutPage.tsx                   # Quick project creation form
├── PageTransition.tsx                 # Animation wrapper
└── [step-specific]/                   # Step-specific components
```

## User Flows

### 1. Normal Create Mode Flow
**Entry Points:**
- `/my-projects` → "Let's Create A Project" button
- Empty projects list → "Create Project" button
- Direct navigation to `/create-project`

**Flow:**
1. **Shortcut Page** (`/create-project`)
   - Quick form with name, domain, basic settings
   - Option to submit directly or go to advanced flow
   - Uses `ShortcutPage` component

2. **Advanced Flow** (if user clicks "Advanced Project Setup")
   - `project-information` → `search-engines` → `keywords` → `competitors` → `analytics-services`
   - Sequential navigation with step validation
   - Cannot skip steps without completing previous ones

**State Management:**
- `isCreateFromScratch: true`
- `isEditMode: false`
- Step completion tracking via `stepCompletion` object

### 2. Edit Mode Flow
**Entry Points:**
- My Projects page → Edit button → `/create-project/project-information?project_id=123`

**Flow:**
- Loads existing project data via `useEditProject` hook
- Can navigate freely between all steps (no validation restrictions)
- Preserves `project_id` parameter in all URLs
- Updates existing project instead of creating new one

**State Management:**
- `isEditMode: true`
- `editProjectId: string`
- `canAccessStep()` returns `true` for all steps

### 3. Direct Create Mode Flow
**Entry Points:**
- URLs with `?mode=create` parameter

**Flow:**
- Bypasses shortcut page
- Goes directly to `project-information` step
- Follows same sequential validation as normal create mode

**State Management:**
- `isCreateFromScratch: true`
- Automatic redirect to advanced flow

### 4. Analytics Services Flow
**Entry Points:**
- Analytics services page with `?pid=projectId&m=method` parameters
- OAuth callbacks from Google Analytics/Search Console

**Flow:**
- Handles Google Analytics and Search Console connections
- Can work independently of main create flow
- Supports property selection dialogs

## Key Hooks and Utilities

### `useEditProject`
- Manages edit mode state and project data loading
- Provides mode detection and validation
- Handles automatic data cleanup and leakage prevention

### `useEditNavigation`
- Provides consistent URL building with edit mode support
- Automatically preserves `project_id` parameter
- Returns memoized URLs for all steps

### `useCreateProjectStore`
- Centralized state management for the entire flow
- Handles step completion tracking
- Provides `canAccessStep()` function for navigation validation

## Navigation Logic

### Step Accessibility (`canAccessStep`)
```typescript
// In edit mode: all steps accessible
if (isEditMode) return true;

// In create mode: sequential validation
switch (step) {
  case "project-information": return true; // Always accessible
  case "search-engines": return !!projectInfo;
  case "keywords": return !!projectInfo && searchEngineConfigs.length > 0;
  case "competitors": return !!projectInfo && searchEngineConfigs.length > 0 && keywords.length > 0;
  case "analytics-services": return !!projectInfo && searchEngineConfigs.length > 0 && keywords.length > 0;
}
```

### URL Building
All navigation uses the `useEditNavigation` hook to ensure consistent URL building:

```typescript
const { urls } = useEditNavigation();

// Automatically handles edit mode parameters
urls.projectInformation  // /create-project/project-information?project_id=123 (edit mode)
urls.searchEngines      // /create-project/search-engines (create mode)
```

## Best Practices

### 1. Always Use Navigation Hooks
❌ **Don't use hardcoded URLs:**
```typescript
<Link href="/create-project/search-engines">
```

✅ **Use the navigation hook:**
```typescript
const { urls } = useEditNavigation();
<Link href={urls.searchEngines}>
```

### 2. Respect Step Validation
All navigation components should check `canAccessStep()` before allowing navigation:

```typescript
const { canAccessStep } = useCreateProjectStore();
const isAccessible = canAccessStep(stepUrl);
```

### 3. Handle Both Modes
Components should work correctly in both create and edit modes:

```typescript
const { isEditMode } = useEditProject();
// Adjust behavior based on mode
```

## Common Issues and Solutions

### Issue: Hardcoded URLs Breaking Edit Mode
**Problem:** Using hardcoded URLs like `/create-project/step` doesn't preserve `project_id` parameter.

**Solution:** Always use `useEditNavigation` hook for URL building.

### Issue: Step Validation in Edit Mode
**Problem:** Users can't navigate freely when editing existing projects.

**Solution:** The `canAccessStep` function now returns `true` for all steps in edit mode.

### Issue: State Leakage Between Modes
**Problem:** Data from edit mode persisting in create mode or vice versa.

**Solution:** The `useEditProject` hook includes automatic cleanup and leakage detection.

## Testing Scenarios

1. **Create Flow:** Start from `/create-project`, go through all steps
2. **Edit Flow:** Edit existing project, verify all steps accessible
3. **Direct Create:** Navigate to `/create-project/project-information?mode=create`
4. **Mode Switching:** Switch between projects and verify state cleanup
5. **URL Preservation:** Verify `project_id` parameter preserved in edit mode
6. **Step Validation:** Verify sequential validation in create mode

## Performance Considerations

- URLs are memoized in `useEditNavigation` to prevent unnecessary re-calculations
- Project data loading is optimized with proper caching and stale time
- Mode validation includes performance optimizations to prevent excessive re-renders
