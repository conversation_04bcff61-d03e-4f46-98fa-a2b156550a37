import type {
  CardTabType,
  LineChartPoint,
  ColorConfig,
  CardsData,
} from "../../../types/AnalyticsTraffics.types";

export type SelectedLines = string[];

export type LineChartCardsData = {
  name: string;
  value: number;
};

export type LineChartCards = {
  title: string;
  bigNumber: string;
  smallNumber: string;
  data: LineChartCardsData[];
};

export type AudienceResponse = {
  cardTabs: CardTabType[];
  lineChartData: LineChartPoint[];
  colors: ColorConfig[];
  selectedLines: SelectedLines;
  cardsData: CardsData;
  lineChartCards: LineChartCards[];
};

export type ActiveTab = {
  height: number;
  left: number;
  width: number;
  top: number;
};
