import { BookIcon } from "@/ui/icons/general";
import CustomerSlider from "./CustomerSlider";
import { CustomerType } from "./OurCustomerType";

export default function BottomSlider({ data }: CustomerType) {
  // const items = Array(8).fill({
  //   label: "Lorem Ipsum",
  //   description: "Lorem ipsum dolor sit consectetur",
  //   icon: <BookIcon />,
  // });

  return (
    <div className="w-full relative">
      <div className="flex items-center justify-end gap-6 translate-x-[100px] animate-[our-customer-animate-bottom_35s_linear_infinite]">
        {[...data].reverse().map((item, index) => (
          <CustomerSlider
            key={index}
            label={item.label}
            description={item.content}
            icon={<BookIcon />}
          />
        ))}
      </div>
    </div>
  );
}
