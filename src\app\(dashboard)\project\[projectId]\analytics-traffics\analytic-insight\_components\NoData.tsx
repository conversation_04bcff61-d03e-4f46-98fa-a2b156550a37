import React from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";

/* ================================== ICONS ================================= */
import { CgInfo } from "react-icons/cg";

const NoData = ({ title }: { title: string }) => {
  return (
    <Card className="flex flex-col">
      <div className="border-b pb-2">
        <span className="text-base text-secondary font-extrabold">{title}</span>
      </div>
      <div className="flex gap-4 items-start text-secondary py-4">
        <CgInfo className="scale-y-[-1] size-6" />
        <h4 className="text-xl font-extrabold">No Data Found</h4>
      </div>
      <span className="text-secondary/70 text-xs">
        {`Unable to get data for ${title}`}
      </span>
    </Card>
  );
};

export default NoData;
