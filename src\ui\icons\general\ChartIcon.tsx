import { SVGProps } from "react";

export function ChartIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.8562 18.7382V33.8495C6.8562 34.7308 7.57067 35.4453 8.452 35.4453H16.431V18.7382C16.431 17.8568 15.7165 17.1423 14.8352 17.1423H8.452C7.57067 17.1423 6.8562 17.8568 6.8562 18.7382Z"
        stroke="currentColor"
        strokeWidth="2.5"
        strokeLinecap="round"
      />
      <path
        d="M33.9848 21.5825H27.6015C26.7202 21.5825 26.0058 22.297 26.0058 23.1784V35.4507H33.9848C34.8662 35.4507 35.5805 34.7362 35.5805 33.8549V23.1784C35.5805 22.297 34.8662 21.5825 33.9848 21.5825Z"
        stroke="currentColor"
        strokeWidth="2.5"
        strokeLinecap="round"
      />
      <path
        d="M16.431 6.53672V17.1428V35.446H26.0058V6.53672C26.0058 5.65537 25.2913 4.94092 24.41 4.94092H18.0268C17.1454 4.94092 16.431 5.65538 16.431 6.53672Z"
        stroke="#914AC4"
        strokeWidth="2.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
