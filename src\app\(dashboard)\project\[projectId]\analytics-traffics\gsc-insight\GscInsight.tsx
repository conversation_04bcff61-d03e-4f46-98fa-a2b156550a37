"use client";
import React, { useEffect, useRef, useState } from "react";

/* =============================== HEADLESS UI ============================== */
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from "@headlessui/react";
import GscSection from "./(gsc-insight)/gsc-section/GscSection";
import SharedAnalyticsHeader from "../_components/SharedAnalyticsHeader";
import { motion } from "framer-motion";
import { ActiveTab } from "../analytic-insight/(analytic-insight)/audience/Audience.types";
import SnippetsSection from "./(gsc-insight)/snippets-section/SnippetsSection";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";
import { useSearchParams } from "next/navigation";

/* ========================================================================== */
const GscInsight = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [selectedIndex, setSelectedIndex] = useState(0);
  const tabRefs = useRef<HTMLElement[]>([]);
  const [activeTab, setActiveTab] = useState<ActiveTab | null>(null);
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const searchParams = useSearchParams();
  const tab = searchParams?.get("tab");
  const tabs = [
    { tab: "Snippets", content: <SnippetsSection /> },
    { tab: "GSC", content: <GscSection /> },
  ];

  const tabNameToIndex = tabs.reduce((acc, { tab }, idx) => {
    acc[tab.toLowerCase().replace(/\s+/g, "-")] = idx;
    return acc;
  }, {} as Record<string, number>);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (showDateRangePicker) {
        if (
          toggleButtonRef.current?.contains(event.target as Node) ||
          dateRangePickerRef.current?.contains(event.target as Node)
        ) {
          return;
        }
        setShowDateRangePicker(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDateRangePicker, dateRangePickerRef]);

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handleIndicatorPosition = (index: number) => {
    const currentTab = tabRefs.current[index];
    const x = {
      height: currentTab.offsetHeight,
      left: currentTab.offsetLeft,
      width: currentTab.offsetWidth,
      top: currentTab.offsetTop,
    };
    setActiveTab(x);
  };

  const handleTabChange = (index: number) => {
    setSelectedIndex(index);
    const currentTab = tabRefs.current[index];
    if (currentTab) {
      setActiveTab({
        height: currentTab.offsetHeight,
        left: currentTab.offsetLeft,
        width: currentTab.offsetWidth,
        top: currentTab.offsetTop,
      });
    }
  };

  /* ========================================================================== */
  /*                                 USE EFFECTS                                */
  /* ========================================================================== */
  useEffect(() => {
    const firstTab = tabRefs.current[0];
    if (firstTab) {
      setActiveTab({
        height: firstTab.offsetHeight,
        left: firstTab.offsetLeft,
        width: firstTab.offsetWidth,
        top: firstTab.offsetTop,
      });
    }
  }, []);

  useEffect(() => {
    handleTabChange(0);
  }, []);

  useEffect(() => {
    if (tab) {
      const normalizedTab = tab.toLowerCase().replace(/\s+/g, "-");
      const index = tabNameToIndex[normalizedTab];
      if (typeof index === "number" && index !== selectedIndex) {
        handleTabChange(index);
      }
    }
  }, [tab, selectedIndex, tabNameToIndex]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="space-y-4 w-full">
      <SharedAnalyticsHeader title="GSC Insight" />
      <TabGroup
        selectedIndex={selectedIndex}
        onChange={handleTabChange}
        className={"space-y-4"}
      >
        {" "}
        <TabList
          className={
            "space-x-2 relative overflow-x-auto flex items-center flex-nowrap"
          }
        >
          {tabs.map(({ tab }, index) => (
            <Tab
              key={index}
              onClick={() => handleIndicatorPosition(index)}
              ref={(el) => {
                if (el) tabRefs.current[index] = el;
              }}
              className={"px-4 py-2 aria-selected:text-primary text-nowrap"}
              style={
                selectedIndex === index
                  ? { color: themeColor }
                  : { color: "var(--color-secondary)" }
              }
            >
              {tab}
            </Tab>
          ))}
          {activeTab && (
            <motion.div
              layout
              initial={false}
              animate={activeTab}
              transition={{ type: "tween", ease: "easeInOut" }}
              className="h-8 w-8 bg-primary/10 rounded-lg absolute top-0"
              style={{
                backgroundColor: themeColor + "10",
                height: activeTab.height,
                width: activeTab.width,
                left: activeTab.left,
                top: activeTab.top,
              }}
            />
          )}
        </TabList>
        <TabPanels>
          {tabs.map(({ content }, index) => (
            <TabPanel
              hidden={index !== selectedIndex}
              key={index}
              as={motion.div}
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -100, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {content}
            </TabPanel>
          ))}
        </TabPanels>
      </TabGroup>
    </div>
  );
};

export default GscInsight;
