/**
 * Utility functions for currency formatting and conversion
 */

/**
 * Convert API currency code to uppercase display format
 * @param currency - Currency code from API (e.g., "aud", "usd")
 * @returns Uppercase currency code (e.g., "AUD", "USD")
 */
export function formatCurrencyCode(currency: string): string {
  return currency.toUpperCase();
}

/**
 * Get currency symbol based on currency code
 * @param currency - Currency code (e.g., "aud", "usd")
 * @returns Currency symbol (e.g., "$", "€", "£")
 */
export function getCurrencySymbol(currency: string): string {
  const currencySymbols: Record<string, string> = {
    aud: '$',
    usd: '$',
    eur: '€',
    gbp: '£',
    cad: '$',
    jpy: '¥',
    cny: '¥',
    inr: '₹',
    // Add more currencies as needed
  };
  
  return currencySymbols[currency.toLowerCase()] || '$';
}

/**
 * Extract currency information from pricing plan data
 * @param planData - Pricing plan object from API
 * @returns Object with currency symbol and code
 */
export function extractCurrencyInfo(planData: { currency?: string } | null | undefined) {
  const currency = planData?.currency || 'aud';
  return {
    symbol: getCurrencySymbol(currency),
    code: formatCurrencyCode(currency),
  };
}
