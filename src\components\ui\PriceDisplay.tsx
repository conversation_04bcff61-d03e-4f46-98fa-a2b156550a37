import React from "react";

interface PriceDisplayProps {
  /** The price amount (can be string or number) */
  price: string | number;
  /** Currency symbol (default: '$') */
  currency?: string;
  /** Currency code to display (default: 'AUD') */
  currencyCode?: string;
  /** Size variant for the price display */
  size?: "sm" | "md" | "lg" | "xl";
  /** Font weight for the price */
  weight?: "normal" | "medium" | "semibold" | "bold";
  /** Additional CSS classes for the price container */
  className?: string;
  /** Whether to show the currency code */
  showCurrencyCode?: boolean;
}

const PriceDisplay: React.FC<PriceDisplayProps> = ({
  price,
  currency = "$",
  currencyCode = "AUD",
  size = "md",
  weight = "medium",
  className = "",
  showCurrencyCode = true,
}) => {
  // Size mappings for price and currency code
  const sizeClasses = {
    sm: {
      price: "text-sm",
      currency: "text-sm",
    },
    md: {
      price: "text-base",
      currency: "text-sm",
    },
    lg: {
      price: "text-lg",
      currency: "text-sm",
    },
    xl: {
      price: "text-xl",
      currency: "text-sm",
    },
  };

  // Weight mappings
  const weightClasses = {
    normal: "font-normal",
    medium: "font-medium",
    semibold: "font-semibold",
    bold: "font-bold",
  };

  // Format price to ensure it's a string
  const formattedPrice = typeof price === "number" ? price.toFixed(2) : price;

  return (
    <span className={`inline-flex items-end`}>
      <span
        className={`${sizeClasses[size].price} ${weightClasses[weight]} ${className}`}
      >
        {currency}
        {formattedPrice}
      </span>
      {showCurrencyCode && (
        <span
          className={`${sizeClasses[size].currency} font-normal text-gray-600 ml-1 leading-none`}
        >
          {currencyCode}
        </span>
      )}
    </span>
  );
};

export default PriceDisplay;
