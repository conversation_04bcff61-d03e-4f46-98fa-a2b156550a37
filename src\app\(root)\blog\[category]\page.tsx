import React from "react";
import Link from "next/link";
import { Metadata } from "next";
import Blog<PERSON><PERSON>, { BlogCardProps } from "../components/BlogCard";
import SearchInput from "../components/SearchInput";
import CategoryList from "../components/CategoryList";
import { Pagination } from "../components/Pagination";
import http from "@/services/httpService";

// Define the props for the page component
interface CategoryPageProps {
  params: {
    category: string;
  };
  searchParams: {
    page?: string;
    tag?: string;
    q?: string; // Search query parameter
  };
}

interface BlogPostResult {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body: string;
  publish_timestamp: number;
  status: string;
  snippet: string;
  cover_image: string | null;
  url: string;
  tags: string[];
  similar_posts: Array<{
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
  }>;
}

interface BlogApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPostResult[];
  categories: Category[];
}

interface Category {
  name: string;
  slug: string;
}

// Number of posts per page
const POSTS_PER_PAGE = 10;

// Generate metadata for the page
export async function generateMetadata({
  params,
}: CategoryPageProps): Promise<Metadata> {
  // Fetch category data to get the proper name
  try {
    // First try to get the category name from the API
    const response = await http.get(`/api/blog/category/${params.category}/`);
    const data = response.data;
    const categoryName =
      data.categories.find((cat) => cat.slug === params.category)?.name ||
      decodeURIComponent(params.category);

    return {
      title: `${categoryName} - Blog Category`,
      description: `Browse all blog posts in the ${categoryName} category.`,
      openGraph: {
        title: `${categoryName} - Blog Category`,
        description: `Browse all blog posts in the ${categoryName} category.`,
      },
    };
  } catch (error) {
    // Fallback to using the slug if API call fails
    const categoryName = decodeURIComponent(params.category);

    return {
      title: `${categoryName} - Blog Category`,
      description: `Browse all blog posts in the ${categoryName} category.`,
      openGraph: {
        title: `${categoryName} - Blog Category`,
        description: `Browse all blog posts in the ${categoryName} category.`,
      },
    };
  }
}

// The main page component
export default async function CategoryPage({
  params,
  searchParams,
}: CategoryPageProps) {
  // Properly await the params by using them in an async context
  const categorySlug = await Promise.resolve(params.category);
  const currentPage = Number(searchParams?.page ?? 1);
  const tag = searchParams?.tag;
  const searchQuery = searchParams?.q || "";

  try {
    // If search query is provided, redirect to main blog page with search query
    if (searchQuery) {
      return {
        redirect: {
          destination: `/blog?q=${encodeURIComponent(searchQuery)}`,
          permanent: false,
        },
      };
    }

    // Ensure current page is valid
    if (isNaN(currentPage) || currentPage < 1) {
      return {
        redirect: {
          destination: `/blog/${categorySlug}`,
          permanent: false,
        },
      };
    }

    // Fetch posts by category using the API endpoint with pagination
    const response = await http.get(
      `/api/blog/category/${categorySlug}/?${
        currentPage > 1 ? `page=${currentPage}` : ""
      }`
    );

    const data: BlogApiResponse = response.data;
    const categoryName =
      data.categories.find((cat) => cat.slug === categorySlug)?.name ||
      decodeURIComponent(categorySlug);

    // Calculate total pages
    const totalPages = Math.ceil(data.count / POSTS_PER_PAGE);

    return (
      <div className="w-full mt-8 lg:mt-[84px] mb-12 container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput defaultValue={searchQuery} />
            <CategoryList
              categories={data.categories}
              currentCategory={categorySlug}
            />
          </div>

          {/* Main content */}
          <div className="lg:col-span-3">
            <div className="mb-6">
              <div className="flex flex-wrap gap-2 mb-4">
                <Link
                  href="/blog"
                  className="text-primary hover:underline inline-block"
                >
                  Blog
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-gray-600 capitalize">{categoryName}</span>
              </div>
              <h1 className="text-secondary font-black text-2xl lg:text-4xl mt-2">
                {categoryName}
              </h1>
              <p className="text-gray-600 mt-2">
                Browse all blog posts in the {categoryName} category.
              </p>
            </div>

            {/* Blog posts grid */}
            {data.results.length > 0 ? (
              <>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                  {data.results.map((post) => (
                    <BlogCard
                      key={post.id}
                      id={post.id}
                      snippet={post.snippet}
                      title={post.title}
                      slug={post.slug}
                      author={post.author}
                      publish_timestamp={post.publish_timestamp}
                      url={post.url}
                      tags={post.tags}
                      cover_image={post.cover_image}
                      category={{
                        name: categoryName,
                        slug: categorySlug,
                      }}
                    />
                  ))}
                </div>

                {totalPages > 1 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    nextPageUrl={data.next}
                    previousPageUrl={data.previous}
                  />
                )}
              </>
            ) : (
              <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg text-center">
                <h2 className="text-xl font-bold text-gray-700 mb-2">
                  No Posts Found in This Category
                </h2>
                <p className="text-gray-600">
                  There are currently no blog posts available in the{" "}
                  {categoryName} category. Please check back later or browse
                  other categories.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching category posts:", error);

    // Attempt to fetch categories for the error state
    let categories: Category[] = [];
    try {
      const catResponse = await http.get("/api/blog/categories/");
      categories = catResponse.data.categories;
    } catch (e) {
      console.error("Error fetching categories:", e);
    }

    // Return error state
    return (
      <div className="w-full mt-8 lg:mt-[84px] container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput defaultValue={searchQuery} />
            <CategoryList
              categories={categories}
              currentCategory={categorySlug}
            />
          </div>
          <div className="lg:col-span-3">
            <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
              <h2 className="text-xl font-bold text-red-700 mb-2">
                Error Loading Category
              </h2>
              <p className="text-red-600 mb-4">
                We're having trouble loading the blog posts for this category.
                Please try again later.
              </p>
              <Link href="/blog" className="text-primary hover:underline">
                ← Back to Blog
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
