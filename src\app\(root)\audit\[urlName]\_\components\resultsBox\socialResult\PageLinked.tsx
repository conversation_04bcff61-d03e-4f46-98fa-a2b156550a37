import React, { ReactNode } from "react";
import Link<PERSON><PERSON> from "../../LinkCard";

type Props = {
  title: string;
  desscription: string;
  pageLink: string;
  pageIcon?: React.ReactNode;
  children?: ReactNode;
};

export default function PageLinked({
  desscription,
  pageIcon,
  pageLink,
  title,
  children,
}: Props) {
  return (
    <div>
      <div className="flex items-center gap-3 mb-2">
        {pageIcon && <div>{pageIcon}</div>}
        <h4 className="text-secondary font-semibold">{title}</h4>
      </div>
      <p className="text-sm text-secondary/60 my-2">{desscription}</p>
      {pageLink && <LinkCard title={pageLink}></LinkCard>}
      <div className="mt-2 flex flex-col gap-2">{children}</div>
    </div>
  );
}
