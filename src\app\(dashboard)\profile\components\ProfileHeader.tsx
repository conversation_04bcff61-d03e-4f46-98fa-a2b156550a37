"use client";

import React, { useState } from "react";
import Image from "next/image";
import TitlePageDashboard from "@/components/dashboard/TitlePageDashboard";
import EditPenIcon from "@/ui/icons/dashboard/EditPenIcon";
import { UserIcon, AtSymbolIcon } from "@heroicons/react/24/outline";
import FetchLoadingBox from "@/components/shared/FetchLoadingBox";
import ErrorFetch from "@/components/shared/ErrorFetch";
import { forgotPassword } from "@/services/authService";
import storageService from "@/services/storageService";
import { EmailIcon } from "@/ui/icons/socialMedia";
import { CheckIcon } from "@/ui/icons/general";
import { motion, AnimatePresence } from "framer-motion";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import ResetPasswordButton from "./ResetPasswordButton";

interface ProfileHeaderProps {
  dataUserProfile: any;
  fetchUser: boolean;
  isErrorProfile: boolean;
  onEditClick: () => void;
}

export default function ProfileHeader({
  dataUserProfile,
  fetchUser,
  isErrorProfile,
  onEditClick,
}: ProfileHeaderProps) {
  const [imageError, setImageError] = useState(false);
  const [resetPasswordError, setResetPasswordError] = useState("");
  const [isResetLoading, setIsResetLoading] = useState<boolean>(false);
  const [resetPasswordOpen, setResetPasswordOpen] = useState<boolean>(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const resetPassword = async () => {
    setIsResetLoading(true);
    const userInfo = storageService.getUser();
    const response = (await forgotPassword({
      email: userInfo?.email || "",
    })) as any;
    if (response.statusCode === 200) {
      setResetPasswordOpen(true);
      setResetPasswordError("");
    } else {
      setResetPasswordError(
        response?.email
          ? response?.email[0]
          : response?.error || "Failed to resend Email."
      );
    }
    setIsResetLoading(false);
  };
  return (
    <>
      <FetchLoadingBox isFetching={fetchUser}>
        <ErrorFetch isError={isErrorProfile} nameError="Profile">
          <div className="flex justify-between items-center">
            <TitlePageDashboard value="Personal Information" />
            <button
              onClick={onEditClick}
              type="button"
              title="Edit Information"
            >
              <EditPenIcon />
            </button>
          </div>
        </ErrorFetch>
        {isErrorProfile ? (
          /* Fallback Profile Section */
          <div className="flex gap-4 lg:gap-6 items-center p-6">
            <figure className="min-w-[80px] relative">
              <div className="rounded-full w-[80px] h-[80px] lg:w-[100px] lg:h-[100px] flex items-center justify-center">
                <UserIcon className="w-8 h-8 lg:w-12 lg:h-12 text-gray-600" />
              </div>
            </figure>
            <div className="flex flex-col gap-2 cutline cutline-1 flex-1">
              <span className="text-2xl font-bold text-secondary">Profile</span>
              <p className="text-gray-600 cutline cutline-1 flex items-center gap-2">
                <AtSymbolIcon className="w-4 h-4 text-gray-500" />
                Email: Unable to load profile information
              </p>
            </div>
            <ResetPasswordButton
              onClick={resetPassword}
              isLoading={isResetLoading}
              variant="rounded-md"
            />
            {/* <button
            onClick={onEditClick}
            className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
            type="button"
            title="Change Profile"
          >
            <EditPenIcon />
            Change Profile
          </button> */}
          </div>
        ) : (
          <div className="flex gap-4 lg:gap-6 items-center p-6">
            <figure className="min-w-[80px] relative">
              {dataUserProfile?.data?.avatar_url && !imageError ? (
                <Image
                  alt="Profile avatar"
                  src={dataUserProfile.data.avatar_url}
                  width={100}
                  height={100}
                  className="rounded-full h-[80px] w-[80px] lg:h-[100px] lg:w-[100px] object-cover"
                  onError={handleImageError}
                />
              ) : (
                <div className="rounded-full w-[80px] h-[80px] lg:w-[100px] lg:h-[100px] flex items-center justify-center">
                  <UserIcon className="w-8 h-8 lg:w-12 lg:h-12 text-gray-600" />
                </div>
              )}
              {/* <div className="absolute bg-green-500 w-5 h-5  -bottom-1 right-1  rounded-full p-2 shadow-lg"></div> */}
            </figure>
            <div className="flex flex-col gap-2 cutline cutline-1 flex-1">
              <span className="text-2xl font-bold text-secondary">
                {`${dataUserProfile?.data?.first_name || "User"} ${
                  dataUserProfile?.data?.last_name || ""
                }`.trim()}
              </span>
              <p className="text-gray-600 cutline cutline-1 flex items-center gap-2">
                Email: {dataUserProfile?.data?.email || "No email available"}
              </p>
            </div>
            <ResetPasswordButton
              onClick={resetPassword}
              isLoading={isResetLoading}
              variant="rounded-sm"
            />
            {/* <button
            onClick={onEditClick}
            className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
            type="button"
            title="Change Profile"
          >
            <EditPenIcon />
            Change Profile
          </button> */}
          </div>
        )}

        {/* Reset Password Error */}
        {resetPasswordError && (
          <div className="mt-2 text-primary-red text-xs px-6">
            {resetPasswordError}
          </div>
        )}
      </FetchLoadingBox>

      {/* Reset Password Success Modal */}
      <AnimatePresence>
        {resetPasswordOpen && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setResetPasswordOpen(false)}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6"
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-secondary">
                  Reset Password
                </h2>
                <button
                  onClick={() => setResetPasswordOpen(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className="flex flex-col items-center text-center">
                {/* Success Icon */}
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <CheckIcon className="w-8 h-8 text-primary" />
                </div>

                {/* Title */}
                <h3 className="text-lg font-semibold text-secondary mb-2">
                  Email Sent Successfully!
                </h3>

                {/* Email Icon and Address */}
                <div className="flex items-center gap-2 mb-4 p-3 bg-gray-50 rounded-lg">
                  <EmailIcon className="w-5 h-5 text-primary flex-shrink-0" />
                  <span className="text-sm text-secondary font-medium break-all">
                    {dataUserProfile?.data?.email ||
                      storageService.getUser()?.email}
                  </span>
                </div>

                {/* Description */}
                <p className="text-sm text-secondary/80 mb-6 leading-relaxed">
                  We have sent password reset instructions to your email
                  address. Please check your inbox and follow the link to reset
                  your password.
                </p>

                {/* Close Button */}
                <div className="w-full">
                  <ButtenSubmit
                    text="Got it"
                    onClick={() => setResetPasswordOpen(false)}
                    classPluss="w-full"
                    color="primary"
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
